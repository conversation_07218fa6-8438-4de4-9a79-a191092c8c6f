# Git
.git
.gitignore
.gitattributes

# Documentation
README.md
DEPLOYMENT.md
*.md

# Environment files
.env
.env.*
!.env.production

# Dependencies
node_modules
vendor

# IDE
.vscode
.idea
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Laravel
/storage/logs/*
/storage/framework/cache/*
/storage/framework/sessions/*
/storage/framework/views/*
/bootstrap/cache/*
!storage/logs/.gitkeep
!storage/framework/cache/.gitkeep
!storage/framework/sessions/.gitkeep
!storage/framework/views/.gitkeep
!bootstrap/cache/.gitkeep

# Testing
/tests
phpunit.xml
.phpunit.result.cache

# Build files
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Temporary files
*.tmp
*.temp
*.log

# Backup files
*.bak
*.backup

# Docker
docker-compose.override.yml
Dockerfile.dev

# Deployment
deploy.sh
backup.sh

# Cache
.cache
*.cache
