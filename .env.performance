# Performance Optimization Settings for MU Game Admin Panel

# Cache Configuration (for better performance)
CACHE_DRIVER=file
# For production, consider using Redis:
# CACHE_DRIVER=redis
# REDIS_HOST=127.0.0.1
# REDIS_PASSWORD=null
# REDIS_PORT=6379

# Session Configuration (optimized)
SESSION_DRIVER=file
SESSION_LIFETIME=120
# For production with Redis:
# SESSION_DRIVER=redis

# Queue Configuration (for async tasks)
QUEUE_CONNECTION=sync
# For production, use Redis or database:
# QUEUE_CONNECTION=redis

# Database Query Optimization
DB_CONNECTION=mysql
# Enable query caching
DB_QUERY_CACHE=true

# Logging Configuration (reduce I/O)
LOG_CHANNEL=single
LOG_LEVEL=warning
# In production, use 'error' level to reduce log writes

# Performance Settings
# Disable debug mode in production
APP_DEBUG=false

# Enable OPcache (PHP configuration)
# opcache.enable=1
# opcache.memory_consumption=128
# opcache.interned_strings_buffer=8
# opcache.max_accelerated_files=4000
# opcache.revalidate_freq=2
# opcache.fast_shutdown=1

# MySQL Performance Settings (add to my.cnf)
# [mysqld]
# innodb_buffer_pool_size=256M
# innodb_log_file_size=64M
# innodb_flush_log_at_trx_commit=2
# query_cache_type=1
# query_cache_size=32M
# max_connections=100
# thread_cache_size=8

# Laravel Performance Tips:
# 1. Run: php artisan config:cache
# 2. Run: php artisan route:cache
# 3. Run: php artisan view:cache
# 4. Run: php artisan optimize
# 5. Use: composer install --optimize-autoloader --no-dev

# Additional Performance Settings
BCMATH_SCALE=2
MAIL_MAILER=log
BROADCAST_DRIVER=log

# Asset Optimization
# Use CDN for static assets in production
# ASSET_URL=https://cdn.yourdomain.com

# Security & Performance
SESSION_SECURE_COOKIE=false
# Set to true in production with HTTPS
SESSION_SAME_SITE=lax
