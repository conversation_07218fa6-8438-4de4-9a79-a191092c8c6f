# MU Game Admin Panel - Production Environment

APP_NAME="MU Game Admin Panel"
APP_ENV=production
APP_KEY=base64:2i4tc815u6hWhvPyf7pKHqM7UpLEeA465OF4e8Lk5pY=
APP_DEBUG=false
APP_URL=http://**************

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=error

# Website Database (External MySQL Server)
DB_CONNECTION=mysql
DB_HOST=**************
DB_PORT=3321
DB_DATABASE=zythe_platform_sdk
DB_USERNAME=root
DB_PASSWORD=123456

# Game Database (External)
GAME_DB_CONNECTION=game_mysql
GAME_DB_HOST=**************
GAME_DB_PORT=3321
GAME_DB_DATABASE=mu_game_1
GAME_DB_USERNAME=root
GAME_DB_PASSWORD=123456

# Cache & Session
BROADCAST_DRIVER=log
CACHE_DRIVER=redis
FILESYSTEM_DISK=local
QUEUE_CONNECTION=redis
SESSION_DRIVER=redis
SESSION_LIFETIME=120

# Redis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# Mail (Optional)
MAIL_MAILER=smtp
MAIL_HOST=mailpit
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

# Security
ADMIN_IP_WHITELIST=
ENABLE_IP_RESTRICTION=false
MAX_LOGIN_ATTEMPTS=5
LOGIN_LOCKOUT_DURATION=300

# Performance
CACHE_TTL=3600
QUERY_CACHE_ENABLED=true
REDIS_CACHE_PREFIX=mu_admin

# File Upload
MAX_UPLOAD_SIZE=100M
ALLOWED_IMAGE_TYPES=jpg,jpeg,png,gif,webp
UPLOAD_PATH=public/uploads

# Game Integration
GAME_SERVER_IP=**************
GAME_SERVER_PORT=44405
ENABLE_GAME_INTEGRATION=true

# Analytics
ENABLE_ANALYTICS=true
ANALYTICS_RETENTION_DAYS=90

# Backup
BACKUP_ENABLED=true
BACKUP_SCHEDULE="0 2 * * *"
BACKUP_RETENTION_DAYS=30

# Monitoring
ENABLE_HEALTH_CHECK=true
HEALTH_CHECK_TOKEN=your_health_check_token_here

# Rate Limiting
RATE_LIMIT_LOGIN=5
RATE_LIMIT_API=60
RATE_LIMIT_ADMIN=100
