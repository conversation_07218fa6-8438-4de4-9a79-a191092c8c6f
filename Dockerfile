# Laravel MU Game Admin Panel - Optimized for Ubuntu VPS
FROM php:8.2-fpm-alpine

# Set build arguments for optimization
ARG TARGETPLATFORM
ARG BUILDPLATFORM

# Install system dependencies in optimized layers
RUN apk add --no-cache --virtual .build-deps \
    autoconf \
    gcc \
    g++ \
    make \
    pkgconfig \
    && apk add --no-cache \
    # Core tools
    git \
    curl \
    zip \
    unzip \
    bash \
    # Node.js for asset building
    nodejs \
    npm \
    # Image processing libraries
    libpng-dev \
    libjpeg-turbo-dev \
    libwebp-dev \
    freetype-dev \
    # PHP dependencies
    libxml2-dev \
    oniguruma-dev \
    libzip-dev \
    icu-dev \
    # Database clients
    mysql-client \
    # Web server
    nginx \
    supervisor \
    # Process management
    htop \
    procps

# Configure and install PHP extensions in optimized order
RUN docker-php-ext-configure gd --with-freetype --with-jpeg --with-webp \
    && docker-php-ext-configure intl \
    && docker-php-ext-install -j$(nproc) \
        # Database
        pdo_mysql \
        # String processing
        mbstring \
        intl \
        # File handling
        exif \
        zip \
        # Math
        bcmath \
        # Graphics
        gd \
        # XML
        xml \
        # Process control
        pcntl \
        # Performance
        opcache

# Install Redis extension with better error handling
RUN pecl channel-update pecl.php.net \
    && (pecl install redis-6.0.2 && docker-php-ext-enable redis) || \
    (echo "⚠️  Redis extension failed, using file cache fallback" && \
     echo "<?php if (!extension_loaded('redis')) define('REDIS_DISABLED', true);" > /usr/local/etc/php/conf.d/redis-fallback.php) \
    && rm -rf /tmp/pear /var/cache/apk/*

# Clean up build dependencies to reduce image size
RUN apk del .build-deps \
    && rm -rf /var/cache/apk/* /tmp/* /var/tmp/*

# Install Composer with version pinning for stability
COPY --from=composer:2.6 /usr/bin/composer /usr/bin/composer

# Set working directory
WORKDIR /var/www/html

# Copy package files for better layer caching
COPY composer.json composer.lock package*.json ./

# Install Node.js dependencies first (for asset building)
RUN if [ -f "package.json" ]; then \
        npm ci --only=production --no-audit --no-fund && \
        npm cache clean --force; \
    fi

# Install PHP dependencies with optimizations
RUN composer install \
    --no-dev \
    --optimize-autoloader \
    --no-scripts \
    --no-interaction \
    --prefer-dist \
    && composer clear-cache

# Copy application code (excluding node_modules and vendor)
COPY --chown=www-data:www-data . .

# Copy optimized configuration files
COPY docker/nginx.conf /etc/nginx/nginx.conf
COPY docker/php-fpm.conf /usr/local/etc/php-fpm.d/www.conf
COPY docker/supervisord.conf /etc/supervisor/conf.d/supervisord.conf
COPY docker/php.ini /usr/local/etc/php/php.ini

# Create necessary directories
RUN mkdir -p /var/log/nginx \
    && mkdir -p /var/log/supervisor \
    && mkdir -p /run/nginx \
    && mkdir -p storage/logs \
    && mkdir -p storage/framework/cache \
    && mkdir -p storage/framework/sessions \
    && mkdir -p storage/framework/views \
    && mkdir -p bootstrap/cache

# Set permissions
RUN chown -R www-data:www-data /var/www/html \
    && chmod -R 755 storage \
    && chmod -R 755 bootstrap/cache \
    && chmod -R 755 public

# Laravel optimizations (skip key generation, will be done at runtime)
RUN php artisan config:clear \
    && php artisan route:clear \
    && php artisan view:clear \
    && composer dump-autoload --optimize

# Expose port 80
EXPOSE 80

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:80/health || exit 1

# Start supervisor
CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf"]
