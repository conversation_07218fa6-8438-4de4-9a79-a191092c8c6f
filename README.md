# 🎮 MU Game Admin Panel

Hệ thống quản trị toàn diện cho máy chủ game MU Online với giao diện hiện đại và tối ưu hóa hiệu suất cao.

## ✨ Tính năng chính

### 👥 Quản lý người dùng
- Quản lý tài khoản người chơi
- <PERSON>ệ thống phân quyền admin
- Theo dõi hoạt động người dùng
- Quản lý IP và bảo mật

### 🎯 Quản lý nhân vật
- Xem thông tin chi tiết nhân vật
- Quản lý tiền tệ trong game
- Cấm/mở cấm nhân vật
- Lịch sử hoạt động nhân vật

### 💰 Hệ thống nạp tiền
- Nạp coin tự động
- <PERSON> dõi giao dịch
- <PERSON><PERSON><PERSON> c<PERSON>o doanh thu
- <PERSON><PERSON><PERSON><PERSON> lý phương thức thanh toán

### 🎁 <PERSON><PERSON> thống Giftcode
- Tạo và quản lý giftcode
- <PERSON><PERSON> loại theo sự kiện
- <PERSON> dõ<PERSON> sử dụng
- <PERSON><PERSON><PERSON> c<PERSON><PERSON> thống kê

### 📊 Dashboard & Analytics
- Thống kê real-time
- Biểu đồ doanh thu
- Phân tích người dùng
- Monitoring hệ thống

## 🚀 Yêu cầu hệ thống

- **PHP**: 8.1+
- **MySQL**: 5.7+ (External server: **************:3321)
- **Redis**: 6.0+ (Caching & Sessions)
- **Composer**: 2.0+
- **Docker**: 20.0+ (Khuyến nghị)
- **Ubuntu**: 20.04+ (Production)

## 📦 Cài đặt nhanh

### 🐳 Docker Deployment (Khuyến nghị)

```bash
# Clone repository
git clone <repository-url>
cd game-admin-panel

# Deploy trên Ubuntu VPS
chmod +x deploy-ubuntu.sh
sudo ./deploy-ubuntu.sh
```

### 🔧 Manual Installation

```bash
# Cài đặt dependencies
composer install --no-dev --optimize-autoloader

# Cấu hình environment
cp .env.production .env
php artisan key:generate

# Tối ưu hóa
php artisan config:cache
php artisan route:cache
php artisan view:cache
```

## ⚙️ Cấu hình

### 🗃️ Database Setup

Hệ thống sử dụng 2 database MySQL:

1. **Website Database** (`zythe_platform_sdk`)
   - Tài khoản người dùng
   - Admin users
   - System logs
   - Giftcodes

2. **Game Database** (`mu_game_1`)
   - Nhân vật game (t_roles)
   - Tiền tệ game (t_money)
   - Items và equipment

### 🔐 Environment Variables

```env
# Application
APP_NAME="MU Game Admin Panel"
APP_ENV=production
APP_DEBUG=false
APP_URL=http://**************

# Website Database
DB_HOST=**************
DB_PORT=3321
DB_DATABASE=zythe_platform_sdk
DB_USERNAME=root
DB_PASSWORD=123456

# Game Database
GAME_DB_HOST=**************
GAME_DB_PORT=3321
GAME_DB_DATABASE=mu_game_1
GAME_DB_USERNAME=root
GAME_DB_PASSWORD=123456

# Redis (Performance)
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
CACHE_DRIVER=redis
SESSION_DRIVER=redis
```

## 🎯 Sử dụng

### 👨‍💼 Admin Panel

Truy cập admin tại: `http://**************/admin`

**Đăng nhập mặc định:**
- Username: `admin`
- Password: `admin123`

⚠️ **Quan trọng:** Đổi mật khẩu ngay sau lần đăng nhập đầu tiên!

### 👤 User Panel

Người dùng truy cập tại: `http://**************/user`

**Chức năng:**
- Xem thông tin nhân vật
- Nạp coin
- Sử dụng giftcode
- Lịch sử giao dịch

## 🔌 API Documentation

### Admin APIs
```
GET    /api/admin/dashboard     - Dashboard data
POST   /api/admin/users         - User management
GET    /api/admin/characters    - Character list
POST   /api/admin/giftcodes     - Create giftcode
```

### User APIs
```
POST   /api/user/login          - User authentication
GET    /api/user/characters     - User characters
POST   /api/user/giftcode       - Redeem giftcode
GET    /api/user/transactions   - Transaction history
```

## ⚡ Tối ưu hóa hiệu suất

### 🚀 Performance Features
- **Redis Caching**: Database queries, sessions
- **Optimized Queries**: Giảm latency cho server VN
- **Gzip Compression**: Giảm bandwidth
- **Asset Optimization**: CSS/JS minification
- **Connection Pooling**: MySQL connection reuse

### 📈 Monitoring
- Real-time user activity
- System performance metrics
- Database connection status
- Error tracking
- Admin action logging

## 🛡️ Bảo mật

### 🔒 Security Features
- Admin IP whitelisting
- Rate limiting (Login, API)
- CSRF protection
- XSS prevention
- SQL injection protection
- Admin activity logging
- Session security

### 🚨 Security Best Practices
```bash
# Firewall setup
ufw enable
ufw allow ssh
ufw allow 80/tcp
ufw allow 443/tcp

# SSL Certificate (Optional)
certbot --nginx -d your-domain.com
```

## 🚀 Production Deployment

### 📋 Ubuntu VPS Deployment

```bash
# 1. Chuẩn bị server
sudo apt update && sudo apt upgrade -y

# 2. Clone project
git clone <repository-url>
cd game-admin-panel

# 3. Deploy tự động
chmod +x deploy-ubuntu.sh
sudo ./deploy-ubuntu.sh

# 4. Kiểm tra
curl http://**************
```

### 🐳 Docker Production

```bash
# Production deployment
docker-compose up -d --build

# Health check
docker-compose ps
docker-compose logs -f app
```

## 🔧 Maintenance

### 💾 Backup Strategy

```bash
# Database backup (tự động hàng ngày)
mysqldump -h ************** -P 3321 -u root -p123456 zythe_platform_sdk > backup_$(date +%Y%m%d).sql

# File backup
tar -czf backup_files_$(date +%Y%m%d).tar.gz /var/www/mu-game-admin-panel
```

### 🔄 Updates

```bash
# Update code
git pull origin main

# Update dependencies
composer install --no-dev --optimize-autoloader

# Apply optimizations
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Restart services
docker-compose restart
```

## 🐛 Troubleshooting

### ❗ Common Issues

1. **Database Connection Error**
   ```bash
   # Check connection
   php artisan tinker
   DB::connection()->getPdo();
   ```

2. **Permission Issues**
   ```bash
   chmod -R 755 storage bootstrap/cache
   chown -R www-data:www-data storage
   ```

3. **Redis Connection Error**
   ```bash
   redis-cli ping
   systemctl status redis-server
   ```

### 📋 Logs Location
- Application: `storage/logs/laravel.log`
- Admin Activity: `storage/logs/admin.log`
- Nginx: `/var/log/nginx/`
- Docker: `docker-compose logs -f`

## 📞 Support

### 🆘 Getting Help
1. Check logs for error details
2. Verify `.env` configuration
3. Test database connectivity
4. Check Redis status
5. Review firewall settings

### 📊 System Status
```bash
# Check services
docker-compose ps

# View logs
docker-compose logs -f app

# Monitor resources
htop
```

## 🏆 Production Ready

✅ **Optimized for Vietnamese servers**
✅ **External MySQL database support**
✅ **Redis caching enabled**
✅ **Security hardened**
✅ **Performance optimized**
✅ **Docker containerized**
✅ **Auto-deployment scripts**

---

## 📝 License

Phần mềm độc quyền cho quản lý máy chủ game MU Online.

**Server Info:**
- IP: **************
- Port: 80 (HTTP)
- Admin: http://**************/admin
- User: http://**************/user
