<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\GameCacheService;
use App\Services\AuthService;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Redis;

class CacheManagement extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cache:game {action : warmup, clear, stats, flush}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Manage game cache (warmup, clear, stats, flush)';

    protected $gameCacheService;

    public function __construct(GameCacheService $gameCacheService)
    {
        parent::__construct();
        $this->gameCacheService = $gameCacheService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $action = $this->argument('action');

        switch ($action) {
            case 'warmup':
                $this->warmupCache();
                break;
            case 'clear':
                $this->clearCache();
                break;
            case 'stats':
                $this->showStats();
                break;
            case 'flush':
                $this->flushCache();
                break;
            default:
                $this->error('Invalid action. Use: warmup, clear, stats, or flush');
                return 1;
        }

        return 0;
    }

    private function warmupCache()
    {
        $this->info('🔥 Warming up cache...');

        $this->line('- Loading giftcodes...');
        $this->gameCacheService->getActiveGiftcodes();

        $this->line('- Loading monthly card packages...');
        $this->gameCacheService->getMonthlyCardPackages();

        $this->info('✅ Cache warmed up successfully!');
    }

    private function clearCache()
    {
        $this->info('🧹 Clearing game cache...');

        $this->line('- Clearing giftcode cache...');
        $this->gameCacheService->clearGiftcodeCache();

        $this->line('- Clearing monthly card cache...');
        $this->gameCacheService->clearMonthlyCardCache();

        $this->info('✅ Game cache cleared successfully!');
    }

    private function showStats()
    {
        $this->info('📊 Cache Statistics:');

        $stats = $this->gameCacheService->getCacheStats();

        if (isset($stats['error'])) {
            $this->error($stats['error']);
            return;
        }

        $this->table(
            ['Metric', 'Value'],
            [
                ['Memory Used', $stats['memory_used']],
                ['Memory Peak', $stats['memory_peak']],
                ['Connected Clients', $stats['connected_clients']],
                ['Total Keys', $stats['total_keys']],
                ['Cache Hits', $stats['cache_hits']],
                ['Cache Misses', $stats['cache_misses']],
            ]
        );

        if ($stats['cache_hits'] > 0 && $stats['cache_misses'] > 0) {
            $hitRate = ($stats['cache_hits'] / ($stats['cache_hits'] + $stats['cache_misses'])) * 100;
            $this->info("Cache Hit Rate: " . number_format($hitRate, 2) . "%");
        }
    }

    private function flushCache()
    {
        if (!$this->confirm('⚠️  This will flush ALL game cache. Are you sure?')) {
            $this->info('Operation cancelled.');
            return;
        }

        $this->info('🗑️  Flushing all game cache...');

        if ($this->gameCacheService->flushGameCache()) {
            $this->info('✅ All game cache flushed successfully!');
        } else {
            $this->error('❌ Failed to flush cache.');
        }
    }
}
