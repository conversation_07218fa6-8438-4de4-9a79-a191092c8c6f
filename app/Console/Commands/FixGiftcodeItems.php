<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Giftcode;
use App\Models\GameGiftcode;
use App\Services\ItemService;

class FixGiftcodeItems extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'giftcode:fix-items {--dry-run : Show what would be changed without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix giftcode items with incorrect IDs and validate item data';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $dryRun = $this->option('dry-run');
        
        $this->info('🔍 Analyzing giftcode items...');
        
        // Get all giftcodes
        $giftcodes = Giftcode::all();
        $fixedCount = 0;
        $issuesFound = [];
        
        foreach ($giftcodes as $giftcode) {
            $this->info("Checking Giftcode ID: {$giftcode->id}");
            
            $items = $giftcode->getItemsArray();
            $hasChanges = false;
            $fixedItems = [];
            
            foreach ($items as $itemString) {
                $itemParts = explode(',', trim($itemString));
                
                if (count($itemParts) < 7) {
                    $issuesFound[] = "Giftcode {$giftcode->id}: Invalid item format - {$itemString}";
                    continue;
                }
                
                $originalId = trim($itemParts[0]);
                $correctedId = ItemService::validateAndCorrectItemId($originalId);
                
                if ($originalId != $correctedId) {
                    $this->warn("  Item ID {$originalId} -> {$correctedId}");
                    $itemParts[0] = $correctedId;
                    $hasChanges = true;
                }
                
                // Clean up any \r characters
                $cleanedItem = str_replace("\r", "", implode(',', $itemParts));
                $fixedItems[] = $cleanedItem;
                
                // Validate if item exists
                if (!ItemService::isValidItemId($correctedId)) {
                    $issuesFound[] = "Giftcode {$giftcode->id}: Unknown item ID {$correctedId}";
                }
            }
            
            if ($hasChanges) {
                $fixedCount++;
                
                if (!$dryRun) {
                    $giftcode->items = $fixedItems;
                    $giftcode->save();
                    $this->info("  ✅ Fixed giftcode {$giftcode->id}");
                } else {
                    $this->info("  🔧 Would fix giftcode {$giftcode->id}");
                }
            } else {
                $this->info("  ✅ Giftcode {$giftcode->id} is OK");
            }
        }
        
        // Check game giftcodes too
        $this->info("\n🎮 Checking game server giftcodes...");
        $gameGiftcodes = GameGiftcode::all();
        
        foreach ($gameGiftcodes as $gameGiftcode) {
            $items = $gameGiftcode->getItemsArray();
            $hasChanges = false;
            $fixedItems = [];
            
            foreach ($items as $itemString) {
                $itemParts = explode(',', trim($itemString));
                
                if (count($itemParts) >= 2) {
                    $originalId = trim($itemParts[0]);
                    $correctedId = ItemService::validateAndCorrectItemId($originalId);
                    
                    if ($originalId != $correctedId) {
                        $this->warn("  Game Giftcode {$gameGiftcode->code}: Item ID {$originalId} -> {$correctedId}");
                        $itemParts[0] = $correctedId;
                        $hasChanges = true;
                    }
                    
                    $fixedItems[] = implode(',', $itemParts);
                }
            }
            
            if ($hasChanges) {
                if (!$dryRun) {
                    $gameGiftcode->setItemsArray($fixedItems);
                    $gameGiftcode->save();
                    $this->info("  ✅ Fixed game giftcode {$gameGiftcode->code}");
                } else {
                    $this->info("  🔧 Would fix game giftcode {$gameGiftcode->code}");
                }
            }
        }
        
        // Summary
        $this->info("\n📊 Summary:");
        $this->info("Giftcodes that need fixing: {$fixedCount}");
        
        if (!empty($issuesFound)) {
            $this->error("\n❌ Issues found:");
            foreach ($issuesFound as $issue) {
                $this->error("  - {$issue}");
            }
        }
        
        if ($dryRun) {
            $this->info("\n💡 Run without --dry-run to apply fixes");
        }
        
        return 0;
    }
}
