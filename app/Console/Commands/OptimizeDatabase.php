<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;

class OptimizeDatabase extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'db:optimize {--clear-cache : Clear all cache}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Optimize database performance and clear cache';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('🚀 Starting database optimization...');

        // Clear cache if requested
        if ($this->option('clear-cache')) {
            $this->info('🧹 Clearing cache...');
            Cache::flush();
            $this->info('✅ Cache cleared');
        }

        // Optimize tables
        $this->optimizeTables();

        // Update table statistics
        $this->updateStatistics();

        // Clear query cache
        $this->clearQueryCache();

        $this->info('✅ Database optimization completed!');
        return 0;
    }

    private function optimizeTables()
    {
        $this->info('🔧 Optimizing tables...');
        
        $tables = [
            'admin_users',
            'giftcodes', 
            'giftcode_logs',
            't_account',
            't_roles',
            't_mail'
        ];

        foreach ($tables as $table) {
            try {
                if ($this->tableExists($table)) {
                    DB::statement("OPTIMIZE TABLE {$table}");
                    $this->line("  ✓ Optimized {$table}");
                } else {
                    $this->line("  ⚠ Table {$table} not found");
                }
            } catch (\Exception $e) {
                $this->error("  ✗ Failed to optimize {$table}: " . $e->getMessage());
            }
        }
    }

    private function updateStatistics()
    {
        $this->info('📊 Updating table statistics...');
        
        try {
            DB::statement('ANALYZE TABLE admin_users, giftcodes, giftcode_logs');
            $this->line('  ✓ Statistics updated');
        } catch (\Exception $e) {
            $this->error('  ✗ Failed to update statistics: ' . $e->getMessage());
        }
    }

    private function clearQueryCache()
    {
        $this->info('🗑️ Clearing query cache...');
        
        try {
            DB::statement('RESET QUERY CACHE');
            $this->line('  ✓ Query cache cleared');
        } catch (\Exception $e) {
            $this->line('  ⚠ Query cache clear not supported or failed');
        }
    }

    private function tableExists($table)
    {
        try {
            return DB::select("SHOW TABLES LIKE '{$table}'");
        } catch (\Exception $e) {
            return false;
        }
    }
}
