<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Giftcode;
use App\Models\GameGiftcode;
use App\Services\GiftcodeService;
use App\Services\ItemService;
use App\Models\Account;
use App\Models\Role;

class TestGiftcode extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'giftcode:test {code} {--user-id=1} {--character-id=1} {--zone-id=1}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test a giftcode to verify it works correctly';

    protected $giftcodeService;

    public function __construct(GiftcodeService $giftcodeService)
    {
        parent::__construct();
        $this->giftcodeService = $giftcodeService;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $code = $this->argument('code');
        $userId = $this->option('user-id');
        $characterId = $this->option('character-id');
        $zoneId = $this->option('zone-id');

        $this->info("🧪 Testing giftcode: {$code}");
        $this->info("User ID: {$userId}, Character ID: {$characterId}, Zone ID: {$zoneId}");

        // Step 1: Check if giftcode exists in website database
        $this->info("\n1️⃣ Checking website database...");
        $giftcode = Giftcode::where('code', 'LIKE', '%"' . $code . '"%')->first();
        
        if (!$giftcode) {
            $this->error("❌ Giftcode not found in website database");
            return 1;
        }
        
        $this->info("✅ Found giftcode in website database");
        $this->displayGiftcodeInfo($giftcode);

        // Step 2: Check if giftcode exists in game database
        $this->info("\n2️⃣ Checking game database...");
        $gameGiftcode = GameGiftcode::where('code', $code)->first();
        
        if (!$gameGiftcode) {
            $this->error("❌ Giftcode not found in game database");
            return 1;
        }
        
        $this->info("✅ Found giftcode in game database");
        $this->displayGameGiftcodeInfo($gameGiftcode);

        // Step 3: Validate items
        $this->info("\n3️⃣ Validating items...");
        $this->validateItems($giftcode);

        // Step 4: Check user and character
        $this->info("\n4️⃣ Checking user and character...");
        $user = Account::find($userId);
        if (!$user) {
            $this->error("❌ User not found");
            return 1;
        }
        $this->info("✅ User found: {$user->UserName}");

        $character = Role::where('rid', $characterId)->first();
        if (!$character) {
            $this->error("❌ Character not found");
            return 1;
        }
        $this->info("✅ Character found: {$character->rname}");

        // Step 5: Test giftcode usage (dry run)
        $this->info("\n5️⃣ Testing giftcode usage...");
        
        if ($this->confirm('Do you want to actually use this giftcode? (This will consume it)')) {
            $result = $this->giftcodeService->useGiftcode($code, $userId, $characterId, $zoneId);
            
            if ($result['success']) {
                $this->info("✅ Giftcode used successfully!");
                $this->info("Message: {$result['message']}");
                if (isset($result['items'])) {
                    $this->info("Items received:");
                    foreach ($result['items'] as $item) {
                        $this->info("  - {$item['name']} x{$item['count']}");
                    }
                }
            } else {
                $this->error("❌ Failed to use giftcode: {$result['message']}");
            }
        } else {
            $this->info("🔍 Dry run - giftcode not consumed");
        }

        return 0;
    }

    private function displayGiftcodeInfo($giftcode)
    {
        $this->table(['Property', 'Value'], [
            ['ID', $giftcode->id],
            ['Type', $giftcode->type],
            ['Content', $giftcode->content],
            ['Limit', $giftcode->limit],
            ['Period', $giftcode->period],
            ['Zone ID', $giftcode->zoneid],
            ['Active', $giftcode->is_active ? 'Yes' : 'No'],
            ['Created', $giftcode->created_at],
        ]);

        $this->info("Items:");
        foreach ($giftcode->getItemsArray() as $item) {
            $this->info("  - {$item}");
        }
    }

    private function displayGameGiftcodeInfo($gameGiftcode)
    {
        $this->table(['Property', 'Value'], [
            ['Code', $gameGiftcode->code],
            ['Mail Type', $gameGiftcode->mail],
            ['Count', $gameGiftcode->count],
            ['Max Count', $gameGiftcode->maxcount],
            ['User ID', $gameGiftcode->userid ?: 'All'],
        ]);

        $this->info("Items:");
        foreach ($gameGiftcode->getItemsArray() as $item) {
            $this->info("  - {$item}");
        }
    }

    private function validateItems($giftcode)
    {
        $items = $giftcode->getItemsArray();
        $allValid = true;

        foreach ($items as $itemString) {
            $parts = explode(',', trim($itemString));
            
            if (count($parts) < 7) {
                $this->error("❌ Invalid item format: {$itemString}");
                $allValid = false;
                continue;
            }

            $itemId = trim($parts[0]);
            $count = $parts[1];
            
            // Check if item ID is valid
            if (!ItemService::isValidItemId($itemId)) {
                $this->error("❌ Unknown item ID: {$itemId}");
                $allValid = false;
            } else {
                $itemInfo = ItemService::getItemById($itemId);
                $this->info("✅ {$itemInfo['name']} (ID: {$itemId}) x{$count}");
            }
        }

        if ($allValid) {
            $this->info("✅ All items are valid");
        }
    }
}
