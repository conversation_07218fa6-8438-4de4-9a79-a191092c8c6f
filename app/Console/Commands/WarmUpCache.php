<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\CacheService;
use Illuminate\Support\Facades\Log;

class WarmUpCache extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cache:warmup {--force : Force cache refresh}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Warm up application cache for better performance';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('🔥 Starting cache warm up...');

        if ($this->option('force')) {
            $this->info('🗑️  Clearing existing cache...');
            CacheService::clearAllCache();
        }

        try {
            $startTime = microtime(true);

            // Warm up cache
            CacheService::warmUpCache();

            $endTime = microtime(true);
            $duration = round(($endTime - $startTime) * 1000, 2);

            $this->info("✅ Cache warm up completed in {$duration}ms");
            
            Log::info('Cache warm up completed', [
                'duration_ms' => $duration,
                'forced' => $this->option('force')
            ]);

            return Command::SUCCESS;

        } catch (\Exception $e) {
            $this->error('❌ Cache warm up failed: ' . $e->getMessage());
            Log::error('Cache warm up failed', ['error' => $e->getMessage()]);
            return Command::FAILURE;
        }
    }
}
