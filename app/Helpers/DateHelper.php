<?php

namespace App\Helpers;

use Carbon\Carbon;

class DateHelper
{
    /**
     * Safely format date from string or Carbon object
     *
     * @param mixed $date
     * @param string $format
     * @param string $default
     * @return string
     */
    public static function safeFormat($date, $format = 'd/m/Y H:i:s', $default = 'N/A')
    {
        if (!$date) {
            return $default;
        }

        try {
            if (is_string($date)) {
                return Carbon::parse($date)->format($format);
            }
            
            if ($date instanceof Carbon) {
                return $date->format($format);
            }
            
            if (is_object($date) && method_exists($date, 'format')) {
                return $date->format($format);
            }
            
            return $default;
        } catch (\Exception $e) {
            return $default;
        }
    }

    /**
     * Safely get human readable time difference
     *
     * @param mixed $date
     * @param string $default
     * @return string
     */
    public static function safeDiffForHumans($date, $default = 'N/A')
    {
        if (!$date) {
            return $default;
        }

        try {
            if (is_string($date)) {
                return Carbon::parse($date)->diffForHumans();
            }
            
            if ($date instanceof Carbon) {
                return $date->diffForHumans();
            }
            
            return $default;
        } catch (\Exception $e) {
            return $default;
        }
    }

    /**
     * Check if date is today
     *
     * @param mixed $date
     * @return bool
     */
    public static function isToday($date)
    {
        if (!$date) {
            return false;
        }

        try {
            if (is_string($date)) {
                return Carbon::parse($date)->isToday();
            }
            
            if ($date instanceof Carbon) {
                return $date->isToday();
            }
            
            return false;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Get Vietnamese day name
     *
     * @param mixed $date
     * @return string
     */
    public static function getVietnameseDayName($date)
    {
        $dayNames = [
            'Monday' => 'Thứ Hai',
            'Tuesday' => 'Thứ Ba', 
            'Wednesday' => 'Thứ Tư',
            'Thursday' => 'Thứ Năm',
            'Friday' => 'Thứ Sáu',
            'Saturday' => 'Thứ Bảy',
            'Sunday' => 'Chủ Nhật'
        ];

        try {
            if (is_string($date)) {
                $dayName = Carbon::parse($date)->format('l');
            } elseif ($date instanceof Carbon) {
                $dayName = $date->format('l');
            } else {
                return 'N/A';
            }
            
            return $dayNames[$dayName] ?? $dayName;
        } catch (\Exception $e) {
            return 'N/A';
        }
    }

    /**
     * Format date range
     *
     * @param mixed $startDate
     * @param mixed $endDate
     * @param string $format
     * @return string
     */
    public static function formatDateRange($startDate, $endDate, $format = 'd/m/Y')
    {
        $start = self::safeFormat($startDate, $format, null);
        $end = self::safeFormat($endDate, $format, null);
        
        if (!$start && !$end) {
            return 'N/A';
        }
        
        if (!$start) {
            return "Đến {$end}";
        }
        
        if (!$end) {
            return "Từ {$start}";
        }
        
        if ($start === $end) {
            return $start;
        }
        
        return "{$start} - {$end}";
    }
}
