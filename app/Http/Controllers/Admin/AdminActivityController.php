<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\DB;
use App\Services\AdminActivityService;

class AdminActivityController extends Controller
{
    protected $activityService;

    public function __construct(AdminActivityService $activityService)
    {
        $this->activityService = $activityService;
    }

    /**
     * Dashboard hoạt động admin
     */
    public function dashboard()
    {
        $admin = Session::get('admin_user');
        
        // Lấy danh sách admin online
        $onlineAdmins = $this->activityService->getOnlineAdmins();
        
        // Lấy conflicts chưa giải quyết
        $pendingConflicts = $this->activityService->getPendingConflicts();
        
        // Lấy queue operations
        $queuedOperations = $this->activityService->getQueuedOperations();
        
        // Thống kê
        $stats = [
            'online_admins' => $onlineAdmins->count(),
            'pending_conflicts' => $pendingConflicts->count(),
            'queued_operations' => $queuedOperations->count(),
            'active_locks' => $this->getActiveLocksCount()
        ];

        return view('admin.activity.dashboard', compact(
            'admin', 'onlineAdmins', 'pendingConflicts', 'queuedOperations', 'stats'
        ));
    }

    /**
     * API lấy admin online (real-time)
     */
    public function getOnlineAdmins()
    {
        $onlineAdmins = $this->activityService->getOnlineAdmins();
        
        return response()->json([
            'success' => true,
            'admins' => $onlineAdmins,
            'count' => $onlineAdmins->count()
        ]);
    }

    /**
     * API kiểm tra conflict cho thao tác
     */
    public function checkConflict(Request $request)
    {
        $request->validate([
            'resource_type' => 'required|string',
            'resource_id' => 'required|string',
            'operation_type' => 'required|string'
        ]);

        $admin = Session::get('admin_user');
        
        // Kiểm tra lock hiện tại
        $activeLock = $this->activityService->getActiveLock(
            $request->resource_type,
            $request->resource_id,
            $request->operation_type
        );

        if ($activeLock && $activeLock->locked_by_admin_id != $admin['id']) {
            return response()->json([
                'success' => false,
                'conflict' => true,
                'message' => "Resource đang được xử lý bởi {$activeLock->locked_by_admin_username}",
                'locked_by' => $activeLock->locked_by_admin_username,
                'locked_at' => $activeLock->locked_at,
                'expires_at' => $activeLock->expires_at
            ]);
        }

        // Phát hiện potential conflict
        $hasConflict = $this->activityService->detectConflict(
            $request->resource_type,
            $request->resource_id,
            $request->operation_type,
            $admin['id'],
            $admin['username'],
            $request->all()
        );

        return response()->json([
            'success' => true,
            'conflict' => $hasConflict,
            'message' => $hasConflict ? 'Phát hiện potential conflict' : 'Không có conflict'
        ]);
    }

    /**
     * Tạo lock cho thao tác
     */
    public function createLock(Request $request)
    {
        $request->validate([
            'resource_type' => 'required|string',
            'resource_id' => 'required|string',
            'operation_type' => 'required|string',
            'operation_details' => 'nullable|string'
        ]);

        $admin = Session::get('admin_user');

        try {
            $lockToken = $this->activityService->createOperationLock(
                $request->resource_type,
                $request->resource_id,
                $request->operation_type,
                $admin['id'],
                $admin['username'],
                $request->operation_details
            );

            return response()->json([
                'success' => true,
                'lock_token' => $lockToken,
                'message' => 'Lock created successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 409);
        }
    }

    /**
     * Giải phóng lock
     */
    public function releaseLock(Request $request)
    {
        $request->validate([
            'lock_token' => 'required|string'
        ]);

        $released = $this->activityService->releaseLock($request->lock_token);

        return response()->json([
            'success' => $released,
            'message' => $released ? 'Lock released successfully' : 'Lock not found or already released'
        ]);
    }

    /**
     * Lấy conflicts
     */
    public function getConflicts()
    {
        $conflicts = $this->activityService->getPendingConflicts();
        
        return response()->json([
            'success' => true,
            'conflicts' => $conflicts
        ]);
    }

    /**
     * Resolve conflict
     */
    public function resolveConflict(Request $request, $conflictId)
    {
        $request->validate([
            'resolution' => 'required|in:auto_resolved,manual_required,ignored',
            'notes' => 'nullable|string'
        ]);

        $updated = DB::table('admin_operation_conflicts')
            ->where('id', $conflictId)
            ->update([
                'resolution' => $request->resolution,
                'resolution_notes' => $request->notes,
                'resolved_at' => now(),
                'updated_at' => now()
            ]);

        return response()->json([
            'success' => $updated > 0,
            'message' => $updated > 0 ? 'Conflict resolved' : 'Conflict not found'
        ]);
    }

    /**
     * Đếm số lock đang active
     */
    private function getActiveLocksCount()
    {
        return DB::table('admin_operation_locks')
            ->where('expires_at', '>', now())
            ->count();
    }

    /**
     * Force unlock (chỉ super admin)
     */
    public function forceUnlock(Request $request)
    {
        $admin = Session::get('admin_user');

        // Chỉ super admin mới được force unlock
        if ($admin['role'] !== 'super_admin') {
            return response()->json([
                'success' => false,
                'message' => 'Chỉ Super Admin mới có quyền force unlock'
            ], 403);
        }

        $request->validate([
            'resource_type' => 'required|string',
            'resource_id' => 'required|string',
            'operation_type' => 'required|string'
        ]);

        $deleted = DB::table('admin_operation_locks')
            ->where('resource_type', $request->resource_type)
            ->where('resource_id', $request->resource_id)
            ->where('operation_type', $request->operation_type)
            ->delete();

        return response()->json([
            'success' => $deleted > 0,
            'message' => $deleted > 0 ? 'Force unlock successful' : 'No lock found'
        ]);
    }

    /**
     * Update admin activity (called by JavaScript)
     */
    public function updateActivity(Request $request)
    {
        $admin = Session::get('admin_user');
        if (!$admin) {
            return response()->json(['success' => false, 'message' => 'Not authenticated']);
        }

        $sessionId = Session::getId();
        $currentActions = $request->get('current_actions', []);

        $this->activityService->updateAdminActivity($admin['id'], $sessionId, $currentActions);

        return response()->json(['success' => true]);
    }
}
