<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Schema;
use Carbon\Carbon;
use Exception;

class AnalyticsController extends Controller
{
    // Cache duration in minutes
    private const CACHE_DURATION = 15;
    private const CACHE_PREFIX = 'analytics_';

    public function index(Request $request)
    {
        $admin = Session::get('admin_user');
        $period = $request->get('period', '7'); // 7, 30, 90 days

        // Calculate date range
        $endDate = Carbon::now();
        $startDate = Carbon::now()->subDays($period);

        // Use cache for expensive operations
        $cacheKey = self::CACHE_PREFIX . "dashboard_{$period}_" . $startDate->format('Y-m-d');

        $data = Cache::remember($cacheKey, self::CACHE_DURATION, function () use ($startDate, $endDate) {
            return [
                'stats' => $this->getOverviewStatsOptimized($startDate, $endDate),
                'chartData' => $this->getChartDataOptimized($startDate, $endDate),
                'topData' => $this->getTopPerformersOptimized()
            ];
        });

        return view('admin.analytics.index', array_merge($data, compact('admin', 'period')));
    }

    /**
     * Optimized version with batch queries and reduced database calls
     */
    private function getOverviewStatsOptimized($startDate, $endDate)
    {
        // Batch account statistics in single query - using correct table and field names
        $accountStats = DB::table('t_account')
            ->selectRaw('
                COUNT(*) as total,
                SUM(CASE WHEN CreateTime BETWEEN ? AND ? THEN 1 ELSE 0 END) as new_accounts,
                SUM(CASE WHEN LastLoginTime >= ? THEN 1 ELSE 0 END) as active_accounts,
                SUM(CASE WHEN Status = 0 THEN 1 ELSE 0 END) as banned_accounts
            ', [$startDate, $endDate, $startDate])
            ->first();

        // Batch character statistics in single query - using correct database and field names
        $characterStats = DB::connection('game_mysql')->table('t_roles')
            ->selectRaw('
                COUNT(*) as total,
                SUM(CASE WHEN regtime BETWEEN ? AND ? THEN 1 ELSE 0 END) as new_characters,
                SUM(CASE WHEN lasttime >= ? THEN 1 ELSE 0 END) as active_characters
            ', [$startDate, $endDate, $startDate])
            ->where('isdel', 0)
            ->first();

        // Batch revenue statistics with category breakdown - using correct table
        $revenueStats = DB::table('coin_recharge_logs')
            ->selectRaw('
                COALESCE(SUM(amount_vnd), 0) as total_revenue,
                COALESCE(SUM(CASE WHEN created_at BETWEEN ? AND ? THEN amount_vnd ELSE 0 END), 0) as period_revenue,
                COUNT(CASE WHEN created_at BETWEEN ? AND ? THEN 1 END) as period_transactions,
                COALESCE(SUM(CASE WHEN recharge_category = "customer" THEN amount_vnd ELSE 0 END), 0) as customer_total,
                COALESCE(SUM(CASE WHEN recharge_category = "founder_team" THEN amount_vnd ELSE 0 END), 0) as founder_total,
                COALESCE(SUM(CASE WHEN created_at BETWEEN ? AND ? AND recharge_category = "customer" THEN amount_vnd ELSE 0 END), 0) as customer_period,
                COALESCE(SUM(CASE WHEN created_at BETWEEN ? AND ? AND recharge_category = "founder_team" THEN amount_vnd ELSE 0 END), 0) as founder_period,
                COUNT(CASE WHEN created_at BETWEEN ? AND ? AND recharge_category = "customer" THEN 1 END) as customer_transactions,
                COUNT(CASE WHEN created_at BETWEEN ? AND ? AND recharge_category = "founder_team" THEN 1 END) as founder_transactions
            ', [$startDate, $endDate, $startDate, $endDate, $startDate, $endDate, $startDate, $endDate, $startDate, $endDate, $startDate, $endDate])
            ->where('status', 'completed')
            ->first();

        // Calculate growth rates in single query
        $periodDays = $endDate->diffInDays($startDate);
        $prevStartDate = $startDate->copy()->subDays($periodDays);
        $prevEndDate = $startDate->copy();

        $growthStats = DB::table('t_account')
            ->selectRaw('
                COUNT(CASE WHEN CreateTime BETWEEN ? AND ? THEN 1 END) as prev_accounts
            ', [$prevStartDate, $prevEndDate])
            ->first();

        $prevRevenueStats = DB::table('coin_recharge_logs')
            ->selectRaw('COALESCE(SUM(amount_vnd), 0) as prev_revenue')
            ->where('status', 'completed')
            ->whereBetween('created_at', [$prevStartDate, $prevEndDate])
            ->first();

        // Calculate metrics with null safety
        $avgTransactionValue = $revenueStats->period_transactions > 0
            ? $revenueStats->period_revenue / $revenueStats->period_transactions
            : 0;

        $accountGrowth = $growthStats->prev_accounts > 0
            ? (($accountStats->new_accounts - $growthStats->prev_accounts) / $growthStats->prev_accounts) * 100
            : 0;

        $revenueGrowth = $prevRevenueStats->prev_revenue > 0
            ? (($revenueStats->period_revenue - $prevRevenueStats->prev_revenue) / $prevRevenueStats->prev_revenue) * 100
            : 0;

        // Get giftcode stats - using correct table structure
        $giftcodeStats = (object) [
            'total' => 0,
            'active' => 0,
            'usage' => 0
        ];

        try {
            if (Schema::hasTable('giftcodes')) {
                $giftcodeStats = DB::table('giftcodes')
                    ->selectRaw('
                        COUNT(*) as total,
                        SUM(CASE WHEN is_active = 1 AND (expires_at IS NULL OR expires_at > NOW()) THEN 1 ELSE 0 END) as active,
                        COALESCE(SUM(used_count), 0) as usage
                    ')
                    ->first();
            }
        } catch (Exception $e) {
            // Table doesn't exist or has different structure, use defaults
        }

        // Monthly cards and battle pass - these tables may not exist yet, so use safe defaults
        $monthlyCardStats = (object) [
            'total' => 0,
            'active' => 0,
            'revenue' => 0
        ];

        $battlePassStats = (object) [
            'total' => 0,
            'active' => 0,
            'revenue' => 0
        ];

        // Get monthly card stats from monthly_card_purchases table
        try {
            if (Schema::hasTable('monthly_card_purchases')) {
                $monthlyCardStats = DB::table('monthly_card_purchases')
                    ->selectRaw('
                        COUNT(*) as total,
                        SUM(CASE WHEN status = "active" AND expires_at > NOW() THEN 1 ELSE 0 END) as active,
                        COALESCE(SUM(cost_coins), 0) as revenue,
                        COALESCE(SUM(CASE WHEN created_at BETWEEN ? AND ? THEN cost_coins ELSE 0 END), 0) as period_revenue,
                        COUNT(CASE WHEN created_at BETWEEN ? AND ? THEN 1 END) as period_transactions
                    ', [$startDate, $endDate, $startDate, $endDate])
                    ->first();
            }
        } catch (Exception $e) {
            // Table doesn't exist, use defaults
        }

        // Try to get battle pass stats if table exists
        try {
            if (Schema::hasTable('battle_pass')) {
                $battlePassStats = DB::table('battle_pass')
                    ->selectRaw('
                        COUNT(*) as total,
                        SUM(CASE WHEN status = "active" THEN 1 ELSE 0 END) as active,
                        COALESCE(SUM(CASE WHEN status = "active" THEN price ELSE 0 END), 0) as revenue
                    ')
                    ->first();
            }
        } catch (Exception $e) {
            // Table doesn't exist, use defaults
        }

        return [
            'accounts' => [
                'total' => $accountStats->total,
                'new' => $accountStats->new_accounts,
                'active' => $accountStats->active_accounts,
                'banned' => $accountStats->banned_accounts,
                'growth' => $accountGrowth
            ],
            'characters' => [
                'total' => $characterStats->total,
                'new' => $characterStats->new_characters,
                'active' => $characterStats->active_characters
            ],
            'revenue' => [
                'total' => $revenueStats->total_revenue,
                'period' => $revenueStats->period_revenue,
                'transactions' => $revenueStats->period_transactions,
                'avg_value' => $avgTransactionValue,
                'growth' => $revenueGrowth,
                'breakdown' => [
                    'coin_recharge' => [
                        'total' => $revenueStats->total_revenue,
                        'period' => $revenueStats->period_revenue,
                        'transactions' => $revenueStats->period_transactions,
                        'customer' => [
                            'total' => $revenueStats->customer_total,
                            'period' => $revenueStats->customer_period,
                            'transactions' => $revenueStats->customer_transactions
                        ],
                        'founder_team' => [
                            'total' => $revenueStats->founder_total,
                            'period' => $revenueStats->founder_period,
                            'transactions' => $revenueStats->founder_transactions
                        ]
                    ],
                    'monthly_cards' => [
                        'total' => $monthlyCardStats->revenue ?? 0,
                        'period' => $monthlyCardStats->period_revenue ?? 0,
                        'transactions' => $monthlyCardStats->period_transactions ?? 0
                    ]
                ]
            ],
            'giftcodes' => [
                'total' => $giftcodeStats->total ?? 0,
                'active' => $giftcodeStats->active ?? 0,
                'usage' => $giftcodeStats->usage ?? 0
            ],
            'monthly_cards' => [
                'total' => $monthlyCardStats->total ?? 0,
                'active' => $monthlyCardStats->active ?? 0,
                'revenue' => $monthlyCardStats->revenue ?? 0
            ],
            'battle_pass' => [
                'total' => $battlePassStats->total ?? 0,
                'active' => $battlePassStats->active ?? 0,
                'revenue' => $battlePassStats->revenue ?? 0
            ]
        ];
    }

    /**
     * Keep original method for export functionality
     */
    private function getOverviewStats($startDate, $endDate)
    {
        return $this->getOverviewStatsOptimized($startDate, $endDate);
    }

    /**
     * Optimized chart data with parallel queries and caching
     */
    private function getChartDataOptimized($startDate, $endDate)
    {
        // Use parallel execution for independent queries
        $queries = [
            'registrations' => function() use ($startDate, $endDate) {
                return DB::table('t_account')
                    ->select(DB::raw('DATE(CreateTime) as date'), DB::raw('COUNT(*) as count'))
                    ->whereBetween('CreateTime', [$startDate, $endDate])
                    ->groupBy(DB::raw('DATE(CreateTime)'))
                    ->orderBy('date')
                    ->get();
            },
            'revenues' => function() use ($startDate, $endDate) {
                return DB::table('coin_recharge_logs')
                    ->select(DB::raw('DATE(created_at) as date'), DB::raw('SUM(amount_vnd) as amount'))
                    ->where('status', 'completed')
                    ->whereBetween('created_at', [$startDate, $endDate])
                    ->groupBy(DB::raw('DATE(created_at)'))
                    ->orderBy('date')
                    ->get();
            },
            'levels' => function() {
                return DB::connection('game_mysql')->table('t_roles')
                    ->select(
                        DB::raw('CASE
                            WHEN level BETWEEN 1 AND 10 THEN "1-10"
                            WHEN level BETWEEN 11 AND 50 THEN "11-50"
                            WHEN level BETWEEN 51 AND 100 THEN "51-100"
                            WHEN level BETWEEN 101 AND 200 THEN "101-200"
                            ELSE "200+"
                        END as level_range'),
                        DB::raw('COUNT(*) as count')
                    )
                    ->where('isdel', 0)
                    ->groupBy('level_range')
                    ->get();
            }
        ];

        // Execute queries
        $results = [];
        foreach ($queries as $key => $query) {
            $results[$key] = $query();
        }

        // Server stats (cached separately as it's static)
        $serverStats = Cache::remember(self::CACHE_PREFIX . 'server_stats', 60, function() {
            return collect([
                (object) ['serverid' => 'Server 1', 'count' => DB::connection('game_mysql')->table('t_roles')->where('isdel', 0)->count()]
            ]);
        });

        return [
            'registrations' => $results['registrations'],
            'revenues' => $results['revenues'],
            'card_revenues' => collect(),
            'servers' => $serverStats,
            'levels' => $results['levels']
        ];
    }

    /**
     * Keep original method for backward compatibility
     */
    private function getChartData($startDate, $endDate)
    {
        return $this->getChartDataOptimized($startDate, $endDate);
    }

    /**
     * Optimized top performers with JOIN queries to eliminate N+1 problem
     */
    private function getTopPerformersOptimized()
    {
        // Top spenders - already optimized
        $topSpenders = DB::table('coin_recharge_logs')
            ->select('username', DB::raw('SUM(amount_vnd) as total_spent'), DB::raw('COUNT(id) as transaction_count'))
            ->where('status', 'completed')
            ->groupBy('username')
            ->orderBy('total_spent', 'desc')
            ->limit(10)
            ->get();

        // Top characters with JOIN to get username in single query
        $topCharacters = DB::connection('game_mysql')->table('t_roles as r')
            ->leftJoin('zythe_platform_sdk.t_account as a', function($join) {
                $join->whereRaw('a.ID = CAST(SUBSTRING(r.userid, 3) AS UNSIGNED)');
            })
            ->select('r.rname', 'r.level', 'r.userid', 'a.UserName as username')
            ->where('r.isdel', 0)
            ->orderBy('r.level', 'desc')
            ->limit(10)
            ->get()
            ->map(function($character) {
                $character->username = $character->username ?? 'Unknown';
                $character->serverid = 1; // Default server
                return $character;
            });

        // Top giftcodes - get most used giftcodes
        $topGiftcodes = collect();
        try {
            if (Schema::hasTable('giftcodes')) {
                $topGiftcodes = DB::table('giftcodes')
                    ->select('code', 'name', 'used_count', 'max_uses')
                    ->where('is_active', 1)
                    ->orderBy('used_count', 'desc')
                    ->limit(10)
                    ->get();
            }
        } catch (Exception $e) {
            // Table doesn't exist, use empty collection
        }

        // Recent admin actions with specific fields only
        $recentActions = collect();
        try {
            if (Schema::hasTable('admin_action_logs')) {
                $recentActions = DB::table('admin_action_logs')
                    ->select('admin_username', 'action', 'target_name', 'created_at')
                    ->orderBy('created_at', 'desc')
                    ->limit(15)
                    ->get();
            }
        } catch (Exception $e) {
            // Table doesn't exist, use empty collection
        }

        // Top monthly cards from monthly_card_purchases table
        $topMonthlyCards = collect();
        try {
            if (Schema::hasTable('monthly_card_purchases')) {
                $topMonthlyCards = DB::table('monthly_card_purchases as mcp')
                    ->leftJoin('t_account as a', 'mcp.user_id', '=', 'a.ID')
                    ->select('mcp.*', 'a.UserName as username', 'a.Email as email')
                    ->where('mcp.status', 'active')
                    ->where('mcp.expires_at', '>', now())
                    ->orderBy('mcp.created_at', 'desc')
                    ->limit(10)
                    ->get()
                    ->map(function($card) {
                        $card->username = $card->username ?? 'Unknown';
                        $card->type = 'monthly_card';
                        $card->purchase_count = 1; // Each record is one purchase
                        return $card;
                    });
            }
        } catch (Exception $e) {
            // Table doesn't exist, use empty collection
        }

        return [
            'spenders' => $topSpenders,
            'characters' => $topCharacters,
            'giftcodes' => $topGiftcodes,
            'monthly_cards' => $topMonthlyCards,
            'actions' => $recentActions
        ];
    }

    /**
     * Keep original method for backward compatibility
     */
    private function getTopPerformers()
    {
        return $this->getTopPerformersOptimized();
    }

    /**
     * Clear analytics cache
     */
    public function clearCache()
    {
        $keys = [
            self::CACHE_PREFIX . 'dashboard_7_*',
            self::CACHE_PREFIX . 'dashboard_30_*',
            self::CACHE_PREFIX . 'dashboard_90_*',
            self::CACHE_PREFIX . 'server_stats'
        ];

        foreach ($keys as $pattern) {
            Cache::forget($pattern);
        }

        return response()->json(['success' => true, 'message' => 'Cache cleared successfully']);
    }

    /**
     * Get analytics data via AJAX for real-time updates
     */
    public function ajaxData(Request $request)
    {
        $type = $request->get('type', 'overview');
        $period = $request->get('period', '7');

        $endDate = Carbon::now();
        $startDate = Carbon::now()->subDays($period);

        switch ($type) {
            case 'stats':
                return response()->json($this->getOverviewStatsOptimized($startDate, $endDate));
            case 'charts':
                return response()->json($this->getChartDataOptimized($startDate, $endDate));
            case 'top':
                return response()->json($this->getTopPerformersOptimized());
            default:
                return response()->json(['error' => 'Invalid type'], 400);
        }
    }

    public function export(Request $request)
    {
        $type = $request->get('type', 'overview');
        $period = $request->get('period', '30');

        $endDate = Carbon::now();
        $startDate = Carbon::now()->subDays($period);

        switch ($type) {
            case 'accounts':
                return $this->exportAccounts($startDate, $endDate);
            case 'revenue':
                return $this->exportRevenue($startDate, $endDate);
            case 'characters':
                return $this->exportCharacters($startDate, $endDate);
            case 'monthly_cards':
                return $this->exportMonthlyCards($startDate, $endDate);
            default:
                return $this->exportOverview($startDate, $endDate);
        }
    }

    private function exportOverview($startDate, $endDate)
    {
        $stats = $this->getOverviewStats($startDate, $endDate);

        $filename = 'analytics_overview_' . $startDate->format('Y-m-d') . '_to_' . $endDate->format('Y-m-d') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function () use ($stats) {
            $file = fopen('php://output', 'w');

            // Add BOM for UTF-8 Excel compatibility
            fprintf($file, chr(0xEF) . chr(0xBB) . chr(0xBF));

            // CSV Headers
            fputcsv($file, ['Danh mục', 'Giá trị', 'Ghi chú'], ';');

            // Empty row for separation
            fputcsv($file, [''], ';');

            // Account stats section
            fputcsv($file, ['=== THỐNG KÊ TÀI KHOẢN ===', '', ''], ';');
            fputcsv($file, ['Tổng số tài khoản', $stats['accounts']['total'], 'accounts'], ';');
            fputcsv($file, ['Tài khoản mới', $stats['accounts']['new'], 'accounts'], ';');
            fputcsv($file, ['Tài khoản hoạt động', $stats['accounts']['active'], 'accounts'], ';');
            fputcsv($file, ['Tài khoản bị khóa', $stats['accounts']['banned'], 'accounts'], ';');
            fputcsv($file, ['Tăng trưởng tài khoản', number_format($stats['accounts']['growth'], 1) . '%', 'percentage'], ';');

            // Empty row
            fputcsv($file, [''], ';');

            // Revenue stats section
            fputcsv($file, ['=== THỐNG KÊ DOANH THU ===', '', ''], ';');
            fputcsv($file, ['Tổng doanh thu', $stats['revenue']['total'], 'VND'], ';');
            fputcsv($file, ['Doanh thu kỳ này', $stats['revenue']['period'], 'VND'], ';');
            fputcsv($file, ['Tổng giao dịch', $stats['revenue']['transactions'], 'transactions'], ';');
            fputcsv($file, ['Giá trị TB/giao dịch', $stats['revenue']['avg_value'], 'VND'], ';');
            fputcsv($file, ['Tăng trưởng doanh thu', number_format($stats['revenue']['growth'], 1) . '%', 'percentage'], ';');

            // Revenue breakdown
            fputcsv($file, [''], ';');
            fputcsv($file, ['--- Chi tiết doanh thu ---', '', ''], ';');
            fputcsv($file, ['Nạp Coin - Tổng', $stats['revenue']['breakdown']['coin_recharge']['total'], 'VND'], ';');
            fputcsv($file, ['Nạp Coin - Kỳ này', $stats['revenue']['breakdown']['coin_recharge']['period'], 'VND'], ';');
            fputcsv($file, ['Nạp Coin - Giao dịch', $stats['revenue']['breakdown']['coin_recharge']['transactions'], 'transactions'], ';');

            // Customer revenue breakdown
            fputcsv($file, [''], ';');
            fputcsv($file, ['--- Doanh thu khách hàng ---', '', ''], ';');
            fputcsv($file, ['Khách hàng - Tổng', $stats['revenue']['breakdown']['coin_recharge']['customer']['total'], 'VND'], ';');
            fputcsv($file, ['Khách hàng - Kỳ này', $stats['revenue']['breakdown']['coin_recharge']['customer']['period'], 'VND'], ';');
            fputcsv($file, ['Khách hàng - Giao dịch', $stats['revenue']['breakdown']['coin_recharge']['customer']['transactions'], 'transactions'], ';');

            // Founder team revenue breakdown
            fputcsv($file, [''], ';');
            fputcsv($file, ['--- Doanh thu nhóm phát triển ---', '', ''], ';');
            fputcsv($file, ['Nhóm phát triển - Tổng', $stats['revenue']['breakdown']['coin_recharge']['founder_team']['total'], 'VND'], ';');
            fputcsv($file, ['Nhóm phát triển - Kỳ này', $stats['revenue']['breakdown']['coin_recharge']['founder_team']['period'], 'VND'], ';');
            fputcsv($file, ['Nhóm phát triển - Giao dịch', $stats['revenue']['breakdown']['coin_recharge']['founder_team']['transactions'], 'transactions'], ';');

            fputcsv($file, ['Monthly Cards - Tổng', $stats['revenue']['breakdown']['monthly_cards']['total'], 'VND'], ';');
            fputcsv($file, ['Monthly Cards - Kỳ này', $stats['revenue']['breakdown']['monthly_cards']['period'], 'VND'], ';');
            fputcsv($file, ['Monthly Cards - Giao dịch', $stats['revenue']['breakdown']['monthly_cards']['transactions'], 'transactions'], ';');

            // Empty row
            fputcsv($file, [''], ';');

            // Character stats section
            fputcsv($file, ['=== THỐNG KÊ NHÂN VẬT ===', '', ''], ';');
            fputcsv($file, ['Tổng số nhân vật', $stats['characters']['total'], 'characters'], ';');
            fputcsv($file, ['Nhân vật mới', $stats['characters']['new'], 'characters'], ';');
            fputcsv($file, ['Nhân vật hoạt động', $stats['characters']['active'], 'characters'], ';');

            // Empty row
            fputcsv($file, [''], ';');

            // Monthly Cards & Battle Pass stats
            fputcsv($file, ['=== MONTHLY CARDS & BATTLE PASS ===', '', ''], ';');
            fputcsv($file, ['Monthly Cards - Tổng', $stats['monthly_cards']['total'], 'cards'], ';');
            fputcsv($file, ['Monthly Cards - Hoạt động', $stats['monthly_cards']['active'], 'cards'], ';');
            fputcsv($file, ['Monthly Cards - Doanh thu', $stats['monthly_cards']['revenue'], 'VND'], ';');
            fputcsv($file, ['Battle Pass - Tổng', $stats['battle_pass']['total'], 'passes'], ';');
            fputcsv($file, ['Battle Pass - Hoạt động', $stats['battle_pass']['active'], 'passes'], ';');
            fputcsv($file, ['Battle Pass - Doanh thu', $stats['battle_pass']['revenue'], 'VND'], ';');

            // Empty row
            fputcsv($file, [''], ';');

            // Giftcode stats section
            fputcsv($file, ['=== THỐNG KÊ GIFTCODE ===', '', ''], ';');
            fputcsv($file, ['Tổng giftcode', $stats['giftcodes']['total'], 'codes'], ';');
            fputcsv($file, ['Giftcode hoạt động', $stats['giftcodes']['active'], 'codes'], ';');
            fputcsv($file, ['Lượt sử dụng kỳ này', $stats['giftcodes']['usage'], 'usages'], ';');

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    private function exportAccounts($startDate, $endDate)
    {
        $accounts = DB::table('t_account')
            ->whereBetween('CreateTime', [$startDate, $endDate])
            ->orderBy('CreateTime', 'desc')
            ->get();

        $filename = 'accounts_' . $startDate->format('Y-m-d') . '_to_' . $endDate->format('Y-m-d') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function () use ($accounts) {
            $file = fopen('php://output', 'w');

            // Add BOM for UTF-8 Excel compatibility
            fprintf($file, chr(0xEF) . chr(0xBB) . chr(0xBF));

            // CSV Header
            fputcsv($file, ['ID', 'Username', 'Email', 'Trạng thái', 'Ngày tạo', 'Lần đăng nhập cuối'], ';');

            foreach ($accounts as $account) {
                $statusLabel = $account->Status == 1 ? 'Hoạt động' : 'Bị khóa';

                fputcsv($file, [
                    $account->ID,
                    $account->UserName,
                    $account->Email ?? '',
                    $statusLabel,
                    date('d/m/Y H:i:s', strtotime($account->CreateTime)),
                    $account->LastLoginTime ? date('d/m/Y H:i:s', strtotime($account->LastLoginTime)) : 'Chưa đăng nhập'
                ], ';');
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    private function exportRevenue($startDate, $endDate)
    {
        // Get coin recharge transactions separated by category
        $customerTransactions = DB::table('coin_recharge_logs')
            ->where('status', 'completed')
            ->where('recharge_category', 'customer')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->get();

        $founderTransactions = DB::table('coin_recharge_logs')
            ->where('status', 'completed')
            ->where('recharge_category', 'founder_team')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->get();

        $allCoinTransactions = DB::table('coin_recharge_logs')
            ->where('status', 'completed')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->get();

        // Get monthly cards & battle pass transactions (empty for now)
        $cardTransactions = collect(); // Empty collection since monthly_cards table may not exist

        $filename = 'revenue_complete_' . $startDate->format('Y-m-d') . '_to_' . $endDate->format('Y-m-d') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function () use ($customerTransactions, $founderTransactions, $allCoinTransactions, $cardTransactions) {
            $file = fopen('php://output', 'w');

            // Add BOM for UTF-8 Excel compatibility
            fprintf($file, chr(0xEF) . chr(0xBB) . chr(0xBF));

            // Customer Transactions Section
            fputcsv($file, ['=== NẠP COIN - KHÁCH HÀNG ==='], ';');
            fputcsv($file, ['ID', 'Username', 'Số tiền (VND)', 'Coins nhận', 'Loại', 'Transaction ID', 'Ngày tạo'], ';');

            foreach ($customerTransactions as $transaction) {
                fputcsv($file, [
                    $transaction->id,
                    $transaction->username,
                    number_format($transaction->amount_vnd),
                    number_format($transaction->coins_added),
                    $transaction->type,
                    $transaction->transaction_id,
                    date('d/m/Y H:i:s', strtotime($transaction->created_at))
                ], ';');
            }

            // Empty rows for separation
            fputcsv($file, [''], ';');
            fputcsv($file, [''], ';');

            // Founder Team Transactions Section
            fputcsv($file, ['=== NẠP COIN - NHÓM PHÁT TRIỂN ==='], ';');
            fputcsv($file, ['ID', 'Username', 'Số tiền (VND)', 'Coins nhận', 'Loại', 'Transaction ID', 'Ngày tạo'], ';');

            foreach ($founderTransactions as $transaction) {
                fputcsv($file, [
                    $transaction->id,
                    $transaction->username,
                    number_format($transaction->amount_vnd),
                    number_format($transaction->coins_added),
                    $transaction->type,
                    $transaction->transaction_id,
                    date('d/m/Y H:i:s', strtotime($transaction->created_at))
                ], ';');
            }

            // Empty rows for separation
            fputcsv($file, [''], ';');
            fputcsv($file, [''], ';');

            // Monthly Cards & Battle Pass Section
            fputcsv($file, ['=== MONTHLY CARDS & BATTLE PASS ==='], ';');
            fputcsv($file, ['ID', 'Username', 'Email', 'Loại', 'Tên gói', 'Giá (VND)', 'Trạng thái', 'Ngày mua'], ';');

            foreach ($cardTransactions as $card) {
                $typeLabel = $card->type == 'monthly_card' ? 'Monthly Card' : 'Battle Pass';
                fputcsv($file, [
                    $card->id,
                    $card->username,
                    $card->email,
                    $typeLabel,
                    $card->package_name,
                    number_format($card->amount),
                    $card->status,
                    date('d/m/Y H:i:s', strtotime($card->created_at))
                ], ';');
            }

            // Summary section
            fputcsv($file, [''], ';');
            fputcsv($file, [''], ';');
            fputcsv($file, ['=== TỔNG KẾT ==='], ';');

            $customerTotal = $customerTransactions->sum('amount_vnd');
            $founderTotal = $founderTransactions->sum('amount_vnd');
            $coinTotal = $allCoinTransactions->sum('amount_vnd');
            $cardTotal = $cardTransactions->sum('amount_vnd');
            $grandTotal = $coinTotal + $cardTotal;

            fputcsv($file, ['Khách hàng nạp coin', number_format($customerTotal), 'VND'], ';');
            fputcsv($file, ['Nhóm phát triển nạp coin', number_format($founderTotal), 'VND'], ';');
            fputcsv($file, ['Tổng nạp coin', number_format($coinTotal), 'VND'], ';');
            fputcsv($file, ['Tổng Monthly Cards & Battle Pass', number_format($cardTotal), 'VND'], ';');
            fputcsv($file, ['TỔNG DOANH THU', number_format($grandTotal), 'VND'], ';');
            fputcsv($file, ['Giao dịch khách hàng', count($customerTransactions), 'transactions'], ';');
            fputcsv($file, ['Giao dịch nhóm phát triển', count($founderTransactions), 'transactions'], ';');
            fputcsv($file, ['Số giao dịch nạp coin', count($allCoinTransactions), 'transactions'], ';');
            fputcsv($file, ['Số giao dịch cards/pass', count($cardTransactions), 'transactions'], ';');
            fputcsv($file, ['Tổng giao dịch', count($allCoinTransactions) + count($cardTransactions), 'transactions'], ';');

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    private function exportCharacters($startDate, $endDate)
    {
        $characters = DB::connection('game_mysql')->table('t_roles')
            ->whereBetween('regtime', [$startDate, $endDate])
            ->where('isdel', 0)
            ->orderBy('level', 'desc')
            ->get();

        $filename = 'characters_' . $startDate->format('Y-m-d') . '_to_' . $endDate->format('Y-m-d') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function () use ($characters) {
            $file = fopen('php://output', 'w');

            // Add BOM for UTF-8 Excel compatibility
            fprintf($file, chr(0xEF) . chr(0xBB) . chr(0xBF));

            // CSV Header
            fputcsv($file, ['Character ID', 'Tên nhân vật', 'User ID', 'Level', 'Nghề nghiệp', 'Tiền', 'Ngày tạo', 'Lần cuối online'], ';');

            foreach ($characters as $character) {
                fputcsv($file, [
                    $character->rid,
                    $character->rname,
                    $character->userid,
                    $character->level,
                    $character->occupation,
                    number_format($character->money ?? 0),
                    date('d/m/Y H:i:s', strtotime($character->regtime)),
                    date('d/m/Y H:i:s', strtotime($character->lasttime))
                ], ';');
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    private function exportMonthlyCards($startDate, $endDate)
    {
        // Get monthly cards from monthly_card_purchases table
        $monthlyCards = collect();
        try {
            if (Schema::hasTable('monthly_card_purchases')) {
                $monthlyCards = DB::table('monthly_card_purchases as mcp')
                    ->leftJoin('t_account as a', 'mcp.user_id', '=', 'a.ID')
                    ->select('mcp.*', 'a.UserName as username', 'a.Email as email')
                    ->whereBetween('mcp.created_at', [$startDate, $endDate])
                    ->orderBy('mcp.created_at', 'desc')
                    ->get()
                    ->map(function($card) {
                        $card->type = 'monthly_card';
                        $card->price = $card->cost_coins; // Use cost_coins as price
                        $card->purchased_at = $card->created_at;
                        $card->full_name = $card->username;
                        $card->vip_level = 0;
                        $card->created_by_username = 'User Purchase';
                        $card->description = $card->package_name;
                        return $card;
                    });
            }
        } catch (Exception $e) {
            // Table doesn't exist, use empty collection
        }

        $filename = 'monthly_cards_' . $startDate->format('Y-m-d') . '_to_' . $endDate->format('Y-m-d') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function () use ($monthlyCards) {
            $file = fopen('php://output', 'w');

            // Add BOM for UTF-8 Excel compatibility
            fprintf($file, chr(0xEF) . chr(0xBB) . chr(0xBF));

            // CSV Header
            fputcsv($file, [
                'ID',
                'Loại',
                'Username',
                'Email',
                'Họ tên',
                'VIP Level',
                'Tên gói',
                'Giá (VND)',
                'Thời hạn (ngày)',
                'Trạng thái',
                'Ngày mua',
                'Hết hạn',
                'Tạo bởi',
                'Mô tả'
            ], ';');

            foreach ($monthlyCards as $card) {
                $typeLabel = $card->type == 'monthly_card' ? 'Monthly Card' : 'Battle Pass';

                switch ($card->status) {
                    case 'active':
                        $statusLabel = 'Hoạt động';
                        break;
                    case 'expired':
                        $statusLabel = 'Hết hạn';
                        break;
                    case 'cancelled':
                        $statusLabel = 'Đã hủy';
                        break;
                    default:
                        $statusLabel = $card->status;
                        break;
                }

                fputcsv($file, [
                    $card->id,
                    $typeLabel,
                    $card->username,
                    $card->email ?? 'N/A',
                    $card->full_name ?? 'N/A',
                    $card->vip_level ?? 0,
                    $card->package_name,
                    number_format($card->price),
                    $card->duration_days,
                    $statusLabel,
                    date('d/m/Y H:i:s', strtotime($card->purchased_at)),
                    date('d/m/Y H:i:s', strtotime($card->expires_at)),
                    $card->created_by_username ?? 'N/A',
                    $card->description ?? ''
                ], ';');
            }

            // Summary section
            fputcsv($file, [''], ';');
            fputcsv($file, ['=== THỐNG KÊ ==='], ';');

            $monthlyCardsCount = $monthlyCards->where('type', 'monthly_card')->count();
            $battlePassCount = $monthlyCards->where('type', 'battle_pass')->count();
            $monthlyCardsRevenue = $monthlyCards->where('type', 'monthly_card')->where('status', '!=', 'cancelled')->sum('price');
            $battlePassRevenue = $monthlyCards->where('type', 'battle_pass')->where('status', '!=', 'cancelled')->sum('price');

            fputcsv($file, ['Tổng Monthly Cards', $monthlyCardsCount, 'cards'], ';');
            fputcsv($file, ['Tổng Battle Pass', $battlePassCount, 'passes'], ';');
            fputcsv($file, ['Doanh thu Monthly Cards', number_format($monthlyCardsRevenue), 'VND'], ';');
            fputcsv($file, ['Doanh thu Battle Pass', number_format($battlePassRevenue), 'VND'], ';');
            fputcsv($file, ['Tổng doanh thu', number_format($monthlyCardsRevenue + $battlePassRevenue), 'VND'], ';');

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
