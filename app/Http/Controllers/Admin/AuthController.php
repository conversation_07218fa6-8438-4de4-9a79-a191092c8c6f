<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Services\CacheService;
use App\Services\LogoutService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Str;

class AuthController extends Controller
{
    public function showLoginForm()
    {
        // If already logged in, redirect to dashboard
        if (Session::has('admin_user')) {
            return redirect('/admin/dashboard');
        }

        return view('admin.login');
    }

    public function login(Request $request)
    {
        // Rate limiting - 5 attempts per minute per IP
        $key = 'admin_login_attempts:' . $request->ip();
        if (RateLimiter::tooManyAttempts($key, 5)) {
            $seconds = RateLimiter::availableIn($key);
            Log::warning('Admin login rate limit exceeded', [
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'seconds_remaining' => $seconds
            ]);

            return back()->withErrors([
                'login' => "Quá nhiều lần đăng nhập thất bại. Vui lòng thử lại sau {$seconds} giây."
            ]);
        }

        // Validate input
        $request->validate([
            'username' => 'required|string|max:50|regex:/^[a-zA-Z0-9_]+$/',
            'password' => 'required|string|min:6|max:255',
        ]);

        // Get user from database
        $admin = DB::table('admin_users')
            ->where('username', $request->username)
            ->where('is_active', true)
            ->first();

        // Check credentials
        if ($admin && Hash::check($request->password, $admin->password)) {
            // Clear rate limiting on successful login
            RateLimiter::clear($key);

            // Regenerate session to prevent session fixation
            $request->session()->regenerate();

            // Store user in session
            Session::put('admin_user', [
                'id' => $admin->id,
                'username' => $admin->username,
                'email' => $admin->email,
                'full_name' => $admin->full_name,
                'role' => $admin->role,
                'permissions' => json_decode($admin->permissions ?? '[]', true),
                'login_time' => now()->timestamp,
                'login_ip' => $request->ip(),
            ]);

            // Update last login info
            DB::table('admin_users')
                ->where('id', $admin->id)
                ->update([
                    'last_login_at' => now(),
                    'last_login_ip' => $request->ip(),
                    'updated_at' => now(),
                ]);

            // Log successful login
            $this->logAdminAction($admin->id, $admin->username, 'login', 'Đăng nhập thành công', $request);

            Log::info('Admin login successful', [
                'admin_id' => $admin->id,
                'username' => $admin->username,
                'ip' => $request->ip()
            ]);

            return redirect('/admin/dashboard')->with('success', 'Đăng nhập thành công!');
        }

        // Failed login - increment rate limiter
        RateLimiter::hit($key, 60); // 60 seconds decay

        // Log failed attempt
        Log::warning('Admin login failed', [
            'username' => $request->username,
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'user_exists' => $admin ? true : false,
            'user_active' => $admin ? $admin->is_active : null
        ]);

        return back()->withErrors([
            'login' => 'Tên đăng nhập hoặc mật khẩu không đúng.',
        ])->withInput($request->except('password'));
    }


    public function logout(Request $request)
    {
        // Use LogoutService for safe logout
        $success = LogoutService::logoutAdmin($request);

        if ($success) {
            return redirect('/admin/login')->with('success', 'Đã đăng xuất thành công!');
        } else {
            return redirect('/admin/login')->with('warning', 'Đã đăng xuất nhưng có một số lỗi nhỏ.');
        }
    }

    public function dashboard()
    {
        if (!Session::has('admin_user')) {
            return redirect('/admin/login')->withErrors(['login' => 'Vui lòng đăng nhập để tiếp tục.']);
        }

        $admin = Session::get('admin_user');

        // Check session timeout (4 hours)
        $loginTime = $admin['login_time'] ?? 0;
        if (now()->timestamp - $loginTime > 14400) { // 4 hours
            Session::forget('admin_user');
            return redirect('/admin/login')->withErrors(['login' => 'Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại.']);
        }

        // Verify user is still active
        $currentUser = DB::table('admin_users')
            ->where('id', $admin['id'])
            ->where('is_active', true)
            ->first();

        if (!$currentUser) {
            Session::forget('admin_user');
            return redirect('/admin/login')->withErrors(['login' => 'Tài khoản đã bị vô hiệu hóa. Vui lòng liên hệ quản trị viên.']);
        }

        // Get dashboard stats from cache
        $stats = CacheService::getDashboardStats();

        return view('dashboard', compact('admin', 'stats'));
    }

    private function logAdminAction($adminId, $username, $action, $reason, $request)
    {
        DB::table('admin_action_logs')->insert([
            'admin_id' => $adminId,
            'admin_username' => $username,
            'action' => $action,
            'target_type' => 'auth',
            'target_id' => $adminId,
            'target_name' => $username,
            'old_data' => json_encode([]),
            'new_data' => json_encode([
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'timestamp' => now()->toISOString()
            ]),
            'reason' => $reason,
            'ip_address' => $request->ip(),
            'user_agent' => $request->header('User-Agent'),
            'created_at' => now(),
            'updated_at' => now()
        ]);
    }
}
