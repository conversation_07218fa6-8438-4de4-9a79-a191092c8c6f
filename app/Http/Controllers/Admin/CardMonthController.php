<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Session;
use Carbon\Carbon;

class CardMonthController extends Controller
{

    /**
     * Show card_month management page
     */
    public function index(Request $request)
    {
        $admin = Session::get('admin_user');
        $search = $request->get('search');
        $statusFilter = $request->get('status', 'all');

        // Get monthly card purchases with account info
        $query = DB::table('monthly_card_purchases as mcp')
            ->leftJoin('t_account as a', 'mcp.user_id', '=', 'a.ID')
            ->select([
                'mcp.*',
                'a.UserName',
                'a.Email',
                'a.Status as account_status'
            ]);

        // Apply search filters
        if ($search) {
            $query->where(function($q) use ($search) {
                $q->where('a.UserName', 'like', "%{$search}%")
                  ->orWhere('a.Email', 'like', "%{$search}%")
                  ->orWhere('mcp.package_name', 'like', "%{$search}%");
            });
        }

        // Apply status filter
        if ($statusFilter !== 'all') {
            $query->where('mcp.status', $statusFilter);
        }

        $cardMonthPurchases = $query->orderBy('mcp.created_at', 'desc')->paginate(20);

        // Get statistics
        $stats = [
            'total_purchases' => DB::table('monthly_card_purchases')->count(),
            'active_cards' => DB::table('monthly_card_purchases')
                ->where('status', 'active')
                ->where('expires_at', '>', now())
                ->count(),
            'expired_cards' => DB::table('monthly_card_purchases')
                ->where('status', 'expired')
                ->orWhere('expires_at', '<=', now())
                ->count(),
            'total_revenue' => DB::table('monthly_card_purchases')->sum('cost_coins'),
            'monthly_revenue' => DB::table('monthly_card_purchases')
                ->whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->sum('cost_coins')
        ];

        return view('admin.card-month.index', compact(
            'admin', 'cardMonthPurchases', 'search', 'statusFilter', 'stats'
        ));
    }

    /**
     * Create card_month for user (admin function)
     */
    public function create(Request $request)
    {
        $admin = Session::get('admin_user');

        $request->validate([
            'username' => 'required|string|max:50',
            'character_id' => 'required|integer',
            'duration_days' => 'required|integer|min:1|max:365',
            'daily_coins' => 'required|integer|min:1',
            'reason' => 'nullable|string|max:500'
        ]);

        // Check if account exists
        $account = DB::table('t_account')->where('UserName', $request->username)->first();
        if (!$account) {
            return redirect()->back()->withErrors(['username' => 'Không tìm thấy tài khoản với username này.'])->withInput();
        }

        // Verify character belongs to this account
        $gameUserId = 'ZT' . str_pad($account->ID, 4, '0', STR_PAD_LEFT);
        $character = DB::connection('game_mysql')
            ->table('t_roles')
            ->where('rid', $request->character_id)
            ->where('userid', $gameUserId)
            ->where('isdel', 0)
            ->first();

        if (!$character) {
            return redirect()->back()->withErrors(['character_id' => 'Nhân vật không hợp lệ hoặc không thuộc về tài khoản này.'])->withInput();
        }

        // Check if this character already has active monthly card
        $existingCard = DB::table('monthly_card_purchases')
            ->where('user_id', $account->ID)
            ->where('character_id', $request->character_id)
            ->where('status', 'active')
            ->where('expires_at', '>', now())
            ->first();

        if ($existingCard) {
            return redirect()->back()->withErrors(['character_id' => 'Nhân vật ' . $character->rname . ' đã có thẻ tháng đang hoạt động.'])->withInput();
        }

        DB::beginTransaction();
        try {
            // Create monthly card purchase record
            $activatedAt = now();
            $expiresAt = $activatedAt->copy()->addDays($request->duration_days);

            $purchaseId = DB::table('monthly_card_purchases')->insertGetId([
                'user_id' => $account->ID,
                'character_id' => $character->rid,
                'character_name' => $character->rname,
                'zone_id' => $character->zoneid ?? 1,
                'package_name' => 'Thẻ tháng Admin - ' . $request->duration_days . ' ngày',
                'package_type' => 'admin_created',
                'duration_days' => $request->duration_days,
                'cost_coins' => 0, // Admin created, no cost
                'daily_reward_coins' => $request->daily_coins,
                'bonus_items' => json_encode([]),
                'daily_items' => json_encode([]),
                'status' => 'active',
                'activated_at' => $activatedAt,
                'expires_at' => $expiresAt,
                'days_claimed' => 0,
                'notes' => 'Tạo bởi admin: ' . ($request->reason ?? 'Không có lý do'),
                'ip_address' => $request->ip(),
                'created_at' => now(),
                'updated_at' => now()
            ]);

            // Activate monthly card in game database (t_roleparams_char)
            try {
                // Get base value from idx=0 for this character
                $baseParam = DB::connection('game_mysql')
                    ->table('t_roleparams_char')
                    ->where('rid', $character->rid)
                    ->where('idx', 0)
                    ->first();

                $monthlyCardValue = '';
                if ($baseParam && $baseParam->v3) {
                    // Extract base value from v3 (format: "4959,xxxxx")
                    $v3Parts = explode(',', $baseParam->v3);
                    if (count($v3Parts) >= 1) {
                        $baseValue = intval($v3Parts[0]);
                        // Add 30 days for monthly card activation
                        $activatedValue = $baseValue + $request->duration_days;
                        $monthlyCardValue = strval($activatedValue);

                        \Log::info('Monthly card activation calculation', [
                            'character_rid' => $character->rid,
                            'base_value' => $baseValue,
                            'duration_days' => $request->duration_days,
                            'activated_value' => $activatedValue
                        ]);
                    }
                } else {
                    // Fallback: use default base value 4959 if no base param found
                    $baseValue = 4959;
                    $activatedValue = $baseValue + $request->duration_days;
                    $monthlyCardValue = strval($activatedValue);

                    \Log::warning('No base param found for character, using default', [
                        'character_rid' => $character->rid,
                        'default_base' => $baseValue,
                        'activated_value' => $activatedValue
                    ]);
                }

                // Check if monthly card parameter already exists for this character
                $existingParam = DB::connection('game_mysql')
                    ->table('t_roleparams_char')
                    ->where('rid', $character->rid)
                    ->where('idx', 10)
                    ->first();

                if ($existingParam) {
                    // Update existing parameter
                    DB::connection('game_mysql')
                        ->table('t_roleparams_char')
                        ->where('rid', $character->rid)
                        ->where('idx', 10)
                        ->update([
                            'v3' => $monthlyCardValue  // Monthly card expiration value
                        ]);
                } else {
                    // Insert new parameter
                    DB::connection('game_mysql')
                        ->table('t_roleparams_char')
                        ->insert([
                            'rid' => $character->rid,
                            'idx' => 10,  // Monthly card index
                            'v3' => $monthlyCardValue  // Monthly card expiration value
                        ]);
                }
            } catch (\Exception $e) {
                // Log error but don't fail the transaction
                \Log::error('Failed to activate monthly card in game database: ' . $e->getMessage());
            }

            // Log admin action
            $this->logAdminAction(
                $admin,
                'create_card_month',
                'monthly_card_purchase',
                $purchaseId,
                $request->username . ' - Thẻ tháng ' . $request->duration_days . ' ngày',
                [],
                [
                    'duration_days' => $request->duration_days,
                    'daily_coins' => $request->daily_coins,
                    'character_id' => $character->rid,
                    'character_name' => $character->rname,
                    'status' => 'active'
                ],
                'Tạo thẻ tháng cho ' . $request->username . ' (nhân vật: ' . $character->rname . '): ' . ($request->reason ?? 'Không có lý do'),
                $request->ip()
            );

            DB::commit();

            return redirect()->route('admin.card-month.index')
                ->with('success', 'Đã tạo thẻ tháng thành công cho ' . $request->username);

        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()->withErrors(['error' => 'Có lỗi xảy ra: ' . $e->getMessage()])->withInput();
        }
    }

    /**
     * Cancel monthly card (admin function)
     */
    public function cancel(Request $request, $id)
    {
        $admin = Session::get('admin_user');

        $request->validate([
            'reason' => 'required|string|max:500'
        ]);

        // Get the monthly card
        $card = DB::table('monthly_card_purchases')
            ->where('id', $id)
            ->where('status', 'active')
            ->first();

        if (!$card) {
            return response()->json([
                'success' => false,
                'message' => 'Không tìm thấy thẻ tháng hoặc thẻ đã bị hủy'
            ]);
        }

        // Get user info
        $user = DB::table('t_account')->where('ID', $card->user_id)->first();

        DB::beginTransaction();
        try {
            // Update card status to cancelled
            DB::table('monthly_card_purchases')
                ->where('id', $id)
                ->update([
                    'status' => 'cancelled',
                    'notes' => ($card->notes ?? '') . "\nHủy bởi admin: " . $request->reason,
                    'updated_at' => now()
                ]);

            // Log admin action
            $this->logAdminAction(
                $admin,
                'cancel_card_month',
                'monthly_card_purchase',
                $id,
                ($user->UserName ?? 'Unknown') . ' - ' . $card->package_name,
                ['status' => 'active'],
                ['status' => 'cancelled'],
                'Hủy thẻ tháng: ' . $request->reason,
                $request->ip()
            );

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Đã hủy thẻ tháng thành công'
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Extend monthly card (admin function)
     */
    public function extend(Request $request, $id)
    {
        $admin = Session::get('admin_user');

        $request->validate([
            'days' => 'required|integer|min:1|max:365',
            'reason' => 'required|string|max:500'
        ]);

        // Get the monthly card
        $card = DB::table('monthly_card_purchases')
            ->where('id', $id)
            ->where('status', 'active')
            ->first();

        if (!$card) {
            return response()->json([
                'success' => false,
                'message' => 'Không tìm thấy thẻ tháng hoặc thẻ đã bị hủy'
            ]);
        }

        // Get user info
        $user = DB::table('t_account')->where('ID', $card->user_id)->first();

        DB::beginTransaction();
        try {
            $oldExpiresAt = $card->expires_at;
            $newExpiresAt = Carbon::parse($card->expires_at)->addDays($request->days);

            // Update card expiration
            DB::table('monthly_card_purchases')
                ->where('id', $id)
                ->update([
                    'expires_at' => $newExpiresAt,
                    'duration_days' => DB::raw('duration_days + ' . $request->days),
                    'notes' => ($card->notes ?? '') . "\nGia hạn bởi admin: +" . $request->days . " ngày - " . $request->reason,
                    'updated_at' => now()
                ]);

            // Log admin action
            $this->logAdminAction(
                $admin,
                'extend_card_month',
                'monthly_card_purchase',
                $id,
                ($user->UserName ?? 'Unknown') . ' - ' . $card->package_name,
                ['expires_at' => $oldExpiresAt],
                ['expires_at' => $newExpiresAt->format('Y-m-d H:i:s')],
                'Gia hạn thẻ tháng +' . $request->days . ' ngày: ' . $request->reason,
                $request->ip()
            );

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Đã gia hạn thẻ tháng thêm ' . $request->days . ' ngày'
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Search for accounts and their characters
     */
    public function searchAccount(Request $request)
    {
        $search = $request->get('username');

        if (!$search) {
            return response()->json([]);
        }

        // Find account
        $account = DB::table('t_account')
            ->where('UserName', 'like', "%{$search}%")
            ->first();

        if (!$account) {
            return response()->json([
                'success' => false,
                'message' => 'Account not found'
            ]);
        }

        // Get characters for this account
        $gameUserId = 'ZT' . str_pad($account->ID, 4, '0', STR_PAD_LEFT);

        try {
            $characters = DB::connection('game_mysql')
                ->table('t_roles')
                ->where('userid', $gameUserId)
                ->where('isdel', 0)
                ->select('rid', 'rname', 'level', 'lasttime')
                ->orderBy('lasttime', 'desc')
                ->get();

            return response()->json([
                'success' => true,
                'account' => [
                    'id' => $account->ID,
                    'username' => $account->UserName,
                    'email' => $account->Email
                ],
                'characters' => $characters
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to load characters: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get card_month statistics from old system
     */
    private function getCardMonthStats()
    {
        // Get card_month purchases from History table (type 4 = card_month)
        $cardMonthPurchases = DB::table('history')
            ->where('type', 4)
            ->whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)
            ->get();

        // Get monthly card purchases from new system
        $monthlyCardPurchases = DB::table('monthly_card_purchases')
            ->whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)
            ->get();

        return [
            'card_month_purchases' => $cardMonthPurchases->count(),
            'card_month_revenue' => $cardMonthPurchases->sum('content.money') ?? 0,
            'monthly_card_purchases' => $monthlyCardPurchases->count(),
            'monthly_card_revenue' => $monthlyCardPurchases->sum('cost_coins'),
            'total_active_cards' => DB::table('monthly_card_purchases')
                ->where('status', 'active')
                ->where('expires_at', '>', now())
                ->count()
        ];
    }

    /**
     * Log admin action
     */
    private function logAdminAction($admin, $action, $targetType, $targetId, $targetName, $oldData, $newData, $reason, $ip)
    {
        DB::table('admin_action_logs')->insert([
            'admin_id' => $admin['id'],
            'admin_username' => $admin['username'],
            'action' => $action,
            'target_type' => $targetType,
            'target_id' => $targetId,
            'target_name' => $targetName,
            'old_data' => json_encode($oldData),
            'new_data' => json_encode($newData),
            'reason' => $reason,
            'ip_address' => $ip,
            'user_agent' => request()->header('User-Agent'),
            'created_at' => now(),
            'updated_at' => now()
        ]);
    }









}
