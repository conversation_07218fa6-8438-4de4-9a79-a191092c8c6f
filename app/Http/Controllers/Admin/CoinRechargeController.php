<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Log;
use App\Services\AdminActivityService;

class CoinRechargeController extends Controller
{
    protected $activityService;

    public function __construct(AdminActivityService $activityService)
    {
        $this->activityService = $activityService;
    }

    public function index(Request $request)
    {
        $admin = Session::get('admin_user');
        $search = $request->get('search');
        $searchType = $request->get('search_type', 'username');
        $statusFilter = $request->get('status', 'all');
        $categoryFilter = $request->get('category', 'all');

        // Get recharge logs with account info
        $query = DB::table('coin_recharge_logs as r')
            ->leftJoin('t_account as a', 'r.account_id', '=', 'a.ID')
            ->select([
                'r.*',
                'a.UserName',
                'a.Email',
                'a.Status as account_status'
            ]);

        // Apply search filters
        if ($search) {
            switch ($searchType) {
                case 'email':
                    $query->where('a.Email', 'like', "%{$search}%");
                    break;
                case 'transaction_id':
                    $query->where('r.transaction_id', 'like', "%{$search}%");
                    break;
                case 'username':
                default:
                    $query->where('r.username', 'like', "%{$search}%");
                    break;
            }
        }

        // Apply status filter
        if ($statusFilter !== 'all') {
            $query->where('r.status', $statusFilter);
        }

        // Apply category filter
        if ($categoryFilter !== 'all') {
            $query->where('r.recharge_category', $categoryFilter);
        }

        $recharges = $query->orderBy('r.created_at', 'desc')->paginate(20);

        // Get statistics
        $stats = [
            'today_total' => DB::table('coin_recharge_logs')
                ->whereDate('created_at', today())
                ->where('status', 'completed')
                ->sum('amount_vnd'),
            'today_count' => DB::table('coin_recharge_logs')
                ->whereDate('created_at', today())
                ->where('status', 'completed')
                ->count(),
            'month_total' => DB::table('coin_recharge_logs')
                ->whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->where('status', 'completed')
                ->sum('amount_vnd'),
            'pending_count' => DB::table('coin_recharge_logs')
                ->where('status', 'pending')
                ->count(),
            // Customer statistics
            'customer_today_total' => DB::table('coin_recharge_logs')
                ->whereDate('created_at', today())
                ->where('status', 'completed')
                ->where('recharge_category', 'customer')
                ->sum('amount_vnd'),
            'customer_month_total' => DB::table('coin_recharge_logs')
                ->whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->where('status', 'completed')
                ->where('recharge_category', 'customer')
                ->sum('amount_vnd'),
            // Founder team statistics
            'founder_today_total' => DB::table('coin_recharge_logs')
                ->whereDate('created_at', today())
                ->where('status', 'completed')
                ->where('recharge_category', 'founder_team')
                ->sum('amount_vnd'),
            'founder_month_total' => DB::table('coin_recharge_logs')
                ->whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->where('status', 'completed')
                ->where('recharge_category', 'founder_team')
                ->sum('amount_vnd')
        ];

        return view('admin.coin-recharge.index', compact('admin', 'recharges', 'search', 'searchType', 'statusFilter', 'categoryFilter', 'stats'));
    }

    public function create()
    {
        $admin = Session::get('admin_user');
        return view('admin.coin-recharge.create', compact('admin'));
    }

    public function store(Request $request)
    {
        $admin = Session::get('admin_user');

        $request->validate([
            'username' => 'required|string|max:50',
            'amount_vnd' => 'required|numeric|min:1000|max:*********',
            'coins_added' => 'required|integer|min:1|max:1000000',
            'note' => 'required|string|max:500',
            'recharge_category' => 'required|in:customer,founder_team',
        ]);

        // Check if account exists
        $account = DB::table('t_account')->where('UserName', $request->username)->first();
        if (!$account) {
            return redirect()->back()->withErrors(['username' => 'Không tìm thấy tài khoản với username này.']);
        }

        // Create operation lock to prevent concurrent diamond operations
        $lockToken = null;
        try {
            $lockToken = $this->activityService->createOperationLock(
                'user',
                $account->ID,
                'coin_recharge',
                $admin['id'],
                $admin['username'],
                "Nạp {$request->coins_added} kim cương cho {$request->username}"
            );
        } catch (\Exception $e) {
            return redirect()->back()->withErrors(['error' => $e->getMessage()])->withInput();
        }

        try {
            // Generate transaction ID
            $transactionId = 'MANUAL_' . time() . '_' . rand(1000, 9999);

            // Start database transaction
            DB::beginTransaction();

            // Get or create user diamonds record
            $userCoins = DB::table('user_coins')->where('account_id', $account->ID)->first();
            $oldCoins = $userCoins ? $userCoins->coins : 0;
            $newCoins = $oldCoins + $request->coins_added;

            // Update or create user diamonds
            if ($userCoins) {
                DB::table('user_coins')
                    ->where('account_id', $account->ID)
                    ->update([
                        'coins' => $newCoins,
                        'total_recharged' => DB::raw('total_recharged + ' . $request->amount_vnd),
                        'updated_at' => now()
                    ]);
            } else {
                DB::table('user_coins')->insert([
                    'account_id' => $account->ID,
                    'username' => $account->UserName,
                    'coins' => $newCoins,
                    'total_recharged' => $request->amount_vnd,
                    'total_spent' => 0,
                    'created_at' => now(),
                    'updated_at' => now()
                ]);
            }

            // Create recharge log
            $rechargeId = DB::table('coin_recharge_logs')->insertGetId([
                'account_id' => $account->ID,
                'username' => $account->UserName,
                'transaction_id' => $transactionId,
                'amount_vnd' => $request->amount_vnd,
                'coins_added' => $request->coins_added,
                'type' => 'manual',
                'status' => 'completed',
                'recharge_category' => $request->recharge_category,
                'note' => $request->note,
                'admin_id' => $admin['id'],
                'admin_username' => $admin['username'],
                'ip_address' => $request->ip(),
                'user_agent' => $request->header('User-Agent'),
                'completed_at' => now(),
                'created_at' => now(),
                'updated_at' => now()
            ]);

            // Log admin action
            $this->logAdminAction(
                $admin,
                'manual_coin_recharge',
                'coin_recharge',
                $rechargeId,
                $account->UserName,
                ['coins' => $oldCoins],
                ['coins' => $newCoins, 'amount_vnd' => $request->amount_vnd, 'coins_added' => $request->coins_added, 'recharge_category' => $request->recharge_category],
                $request->note,
                $request->ip()
            );

            // Commit transaction
            DB::commit();

            // Release lock
            if ($lockToken) {
                $this->activityService->releaseLock($lockToken);
            }

            $categoryText = $request->recharge_category === 'founder_team' ? 'team sáng lập' : 'khách hàng';
            return redirect()->route('admin.coin-recharge.index')
                ->with('success', "Đã nạp thành công {$request->coins_added} kim cương cho {$categoryText} - tài khoản {$request->username}. Mã giao dịch: {$transactionId}");

        } catch (\Exception $e) {
            // Rollback transaction
            DB::rollback();

            // Release lock
            if ($lockToken) {
                $this->activityService->releaseLock($lockToken);
            }

            Log::error('Diamond recharge failed: ' . $e->getMessage(), [
                'admin_id' => $admin['id'],
                'username' => $request->username,
                'diamonds_added' => $request->coins_added
            ]);

            return redirect()->back()
                ->withErrors(['error' => 'Có lỗi xảy ra khi nạp kim cương: ' . $e->getMessage()])
                ->withInput();
        }
    }

    public function show($id)
    {
        $admin = Session::get('admin_user');

        // Get recharge log with account info
        $recharge = DB::table('coin_recharge_logs as r')
            ->leftJoin('t_account as a', 'r.account_id', '=', 'a.ID')
            ->leftJoin('admin_users as admin', 'r.admin_id', '=', 'admin.id')
            ->select([
                'r.*',
                'a.UserName',
                'a.Email',
                'a.Status as account_status',
                'admin.username as admin_username'
            ])
            ->where('r.id', $id)
            ->first();

        if (!$recharge) {
            return redirect()->route('admin.coin-recharge.index')->withErrors(['error' => 'Không tìm thấy giao dịch.']);
        }

        // Check if this is a bulk transaction
        if ($recharge->type === 'bulk_manual') {
            return $this->showBulkTransaction($recharge, $admin);
        }

        // Get current user diamonds
        $userCoins = DB::table('user_coins')->where('account_id', $recharge->account_id)->first();

        // Get account info for the view
        $account = DB::table('t_account')->where('ID', $recharge->account_id)->first();

        // Get money info
        $gameUserId = 'ZT' . str_pad($account->ID, 4, '0', STR_PAD_LEFT);
        $money = DB::connection('game_mysql')
            ->table('t_money')
            ->where('userid', $gameUserId)
            ->first();

        // Create default money object if not exists
        if (!$money) {
            $money = (object) [
                'userid' => $gameUserId,
                'YuanBao' => 0,
                'Money' => 0,
                'CreateTime' => null,
                'UpdateTime' => null
            ];
        }

        // Phone field doesn't exist in t_account table
        $account->masked_phone = 'Không có thông tin';

        return view('admin.coin-recharge.show', compact('admin', 'recharge', 'userCoins', 'account', 'money'));
    }

    /**
     * Show bulk transaction details
     */
    private function showBulkTransaction($recharge, $admin)
    {
        // Extract bulk info from note
        preg_match('/Bulk recharge: (.+)/', $recharge->note, $matches);
        $bulkPattern = $matches[1] ?? 'Unknown';

        // Get all transactions from the same bulk operation
        $bulkTransactions = DB::table('coin_recharge_logs as r')
            ->leftJoin('t_account as a', 'r.account_id', '=', 'a.ID')
            ->select([
                'r.*',
                'a.UserName',
                'a.Email',
                'a.Status as account_status'
            ])
            ->where('r.type', 'bulk_manual')
            ->where('r.admin_id', $recharge->admin_id)
            ->where('r.note', 'like', '%' . $bulkPattern . '%')
            ->whereDate('r.created_at', date('Y-m-d', strtotime($recharge->created_at)))
            ->orderBy('r.username')
            ->get();

        // Calculate bulk summary
        $bulkSummary = [
            'total_accounts' => $bulkTransactions->count(),
            'total_coins' => $bulkTransactions->sum('coins_added'),
            'total_amount' => $bulkTransactions->sum('amount_vnd'),
            'pattern' => $bulkPattern,
            'admin_username' => $recharge->admin_username,
            'created_at' => $recharge->created_at,
            'recharge_category' => $recharge->recharge_category
        ];

        return view('admin.coin-recharge.bulk-show', compact('admin', 'recharge', 'bulkTransactions', 'bulkSummary'));
    }

    /**
     * Mask phone number - show first 3 digits, hide rest with *
     */
    private function maskPhoneNumber($phone)
    {
        if (!$phone || strlen($phone) < 3) {
            return 'Chưa cập nhật';
        }

        $phone = preg_replace('/[^0-9]/', '', $phone); // Remove non-numeric characters

        if (strlen($phone) < 3) {
            return 'Chưa cập nhật';
        }

        $firstThree = substr($phone, 0, 3);
        $remaining = str_repeat('*', strlen($phone) - 3);

        return $firstThree . $remaining;
    }

    public function searchAccount(Request $request)
    {
        $username = $request->get('username');



        if (!$username || strlen($username) < 2) {
            return response()->json([
                'success' => false,
                'message' => 'Vui lòng nhập ít nhất 2 ký tự'
            ]);
        }

        // Search multiple accounts with LIKE
        $accounts = DB::table('t_account as a')
            ->leftJoin('user_coins as uc', 'a.ID', '=', 'uc.account_id')
            ->select([
                'a.ID as id',
                'a.UserName as username',
                'a.Email as email',
                'a.Status as status',
                'uc.coins as current_coins',
                'uc.total_recharged',
                'uc.total_spent',
                'a.CreateTime as created_at'
            ])
            ->where(function($query) use ($username) {
                $query->where('a.UserName', 'like', "%{$username}%")
                      ->orWhere('a.Email', 'like', "%{$username}%");
            })
            ->limit(10)
            ->get();



        if ($accounts->isEmpty()) {
            return response()->json([
                'success' => false,
                'message' => 'Không tìm thấy tài khoản nào'
            ]);
        }

        // Format accounts data
        $formattedAccounts = $accounts->map(function($account) {
            return [
                'id' => $account->id,
                'username' => $account->username,
                'email' => $account->email,
                'status' => $account->status,
                'current_coins' => $account->current_coins ?? 0,
                'total_recharged' => $account->total_recharged ?? 0,
                'total_spent' => $account->total_spent ?? 0,
                'created_at' => $account->created_at ?? 'N/A'
            ];
        });

        return response()->json([
            'success' => true,
            'accounts' => $formattedAccounts
        ]);
    }

    /**
     * Get characters for selected account (for withdraw form)
     */
    public function getCharacters(Request $request)
    {
        $username = $request->get('username');

        if (!$username) {
            return response()->json([
                'success' => false,
                'message' => 'Username is required'
            ]);
        }

        // Get account
        $account = DB::table('t_account')->where('UserName', $username)->first();
        if (!$account) {
            return response()->json([
                'success' => false,
                'message' => 'Account not found'
            ]);
        }

        // Get game user ID
        $gameUserId = 'ZT' . str_pad($account->ID, 4, '0', STR_PAD_LEFT);

        // Get characters from game database
        $characters = DB::connection('game_mysql')
            ->table('t_roles')
            ->where('userid', $gameUserId)
            ->where('isdel', 0) // Only active characters
            ->select('rid', 'rname', 'userid')
            ->get();

        // Get current game money
        $gameMoney = DB::connection('game_mysql')
            ->table('t_money')
            ->where('userid', $gameUserId)
            ->first();

        $currentGameMoney = $gameMoney ? $gameMoney->money : 0;

        return response()->json([
            'success' => true,
            'characters' => $characters,
            'current_game_money' => $currentGameMoney,
            'game_user_id' => $gameUserId
        ]);
    }

    public function getStatistics(Request $request)
    {
        $period = $request->get('period', 'today'); // today, week, month, year

        $query = DB::table('coin_recharge_logs')->where('status', 'completed');

        switch ($period) {
            case 'today':
                $query->whereDate('created_at', today());
                break;
            case 'week':
                $query->whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()]);
                break;
            case 'month':
                $query->whereMonth('created_at', now()->month)
                    ->whereYear('created_at', now()->year);
                break;
            case 'year':
                $query->whereYear('created_at', now()->year);
                break;
        }

        $stats = [
            'total_amount' => $query->sum('amount_vnd'),
            'total_coins' => $query->sum('coins_added'),
            'total_transactions' => $query->count(),
            'avg_amount' => $query->avg('amount_vnd'),
        ];

        // Get category breakdown
        $customerStats = (clone $query)->where('recharge_category', 'customer');
        $founderStats = (clone $query)->where('recharge_category', 'founder_team');

        $stats['customer_amount'] = $customerStats->sum('amount_vnd');
        $stats['customer_transactions'] = $customerStats->count();
        $stats['founder_amount'] = $founderStats->sum('amount_vnd');
        $stats['founder_transactions'] = $founderStats->count();

        // Get top recharge users
        $topUsers = DB::table('coin_recharge_logs')
            ->select('username', DB::raw('SUM(amount_vnd) as total_amount'), DB::raw('COUNT(*) as transaction_count'))
            ->where('status', 'completed')
            ->groupBy('username')
            ->orderBy('total_amount', 'desc')
            ->limit(10)
            ->get();

        return response()->json([
            'success' => true,
            'stats' => $stats,
            'top_users' => $topUsers
        ]);
    }

    public function revenueReport(Request $request)
    {
        $admin = Session::get('admin_user');
        $period = $request->get('period', 'month');
        $startDate = $request->get('start_date');
        $endDate = $request->get('end_date');

        // Build base query
        $query = DB::table('coin_recharge_logs')->where('status', 'completed');

        // Apply date filters
        if ($startDate && $endDate) {
            $query->whereBetween('created_at', [$startDate . ' 00:00:00', $endDate . ' 23:59:59']);
        } else {
            switch ($period) {
                case 'today':
                    $query->whereDate('created_at', today());
                    break;
                case 'week':
                    $query->whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()]);
                    break;
                case 'month':
                    $query->whereMonth('created_at', now()->month)
                        ->whereYear('created_at', now()->year);
                    break;
                case 'year':
                    $query->whereYear('created_at', now()->year);
                    break;
            }
        }

        // Get overall statistics
        $totalStats = [
            'total_amount' => $query->sum('amount_vnd'),
            'total_transactions' => $query->count(),
            'avg_amount' => $query->avg('amount_vnd'),
        ];

        // Get customer statistics
        $customerStats = [
            'amount' => (clone $query)->where('recharge_category', 'customer')->sum('amount_vnd'),
            'transactions' => (clone $query)->where('recharge_category', 'customer')->count(),
            'avg_amount' => (clone $query)->where('recharge_category', 'customer')->avg('amount_vnd'),
        ];

        // Get founder team statistics
        $founderStats = [
            'amount' => (clone $query)->where('recharge_category', 'founder_team')->sum('amount_vnd'),
            'transactions' => (clone $query)->where('recharge_category', 'founder_team')->count(),
            'avg_amount' => (clone $query)->where('recharge_category', 'founder_team')->avg('amount_vnd'),
        ];

        // Get daily breakdown for charts
        $dailyBreakdown = DB::table('coin_recharge_logs')
            ->select(
                DB::raw('DATE(created_at) as date'),
                DB::raw('SUM(CASE WHEN recharge_category = "customer" THEN amount_vnd ELSE 0 END) as customer_amount'),
                DB::raw('SUM(CASE WHEN recharge_category = "founder_team" THEN amount_vnd ELSE 0 END) as founder_amount'),
                DB::raw('COUNT(CASE WHEN recharge_category = "customer" THEN 1 END) as customer_count'),
                DB::raw('COUNT(CASE WHEN recharge_category = "founder_team" THEN 1 END) as founder_count')
            )
            ->where('status', 'completed')
            ->when($startDate && $endDate, function($q) use ($startDate, $endDate) {
                return $q->whereBetween('created_at', [$startDate . ' 00:00:00', $endDate . ' 23:59:59']);
            })
            ->when(!$startDate || !$endDate, function($q) use ($period) {
                switch ($period) {
                    case 'today':
                        return $q->whereDate('created_at', today());
                    case 'week':
                        return $q->whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()]);
                    case 'month':
                        return $q->whereMonth('created_at', now()->month)
                            ->whereYear('created_at', now()->year);
                    case 'year':
                        return $q->whereYear('created_at', now()->year);
                }
            })
            ->groupBy(DB::raw('DATE(created_at)'))
            ->orderBy('date', 'desc')
            ->limit(30)
            ->get();

        return view('admin.coin-recharge.revenue-report', compact(
            'admin', 'totalStats', 'customerStats', 'founderStats',
            'dailyBreakdown', 'period', 'startDate', 'endDate'
        ));
    }

    /**
     * Show deduct coin form
     */
    public function deductForm()
    {
        $admin = Session::get('admin_user');
        return view('admin.coin-recharge.deduct', compact('admin'));
    }



    /**
     * Process coin deduction
     */
    public function deduct(Request $request)
    {
        $admin = Session::get('admin_user');

        $request->validate([
            'username' => 'required|string|max:50',
            'coins' => 'required|integer|min:1|max:1000000',
            'reason' => 'required|string|max:500',
        ]);

        // Check if account exists
        $account = DB::table('t_account')->where('UserName', $request->username)->first();
        if (!$account) {
            return redirect()->back()->withErrors(['username' => 'Không tìm thấy tài khoản với username này.']);
        }

        // Create operation lock to prevent concurrent diamond operations
        $lockToken = null;
        try {
            $lockToken = $this->activityService->createOperationLock(
                'user',
                $account->ID,
                'coin_deduct',
                $admin['id'],
                $admin['username'],
                "Trừ {$request->coins} kim cương từ {$request->username}"
            );
        } catch (\Exception $e) {
            return redirect()->back()->withErrors(['error' => $e->getMessage()])->withInput();
        }

        try {
            // Get user diamonds record
            $userCoins = DB::table('user_coins')->where('account_id', $account->ID)->first();
            if (!$userCoins) {
                throw new \Exception('Tài khoản này chưa có kim cương để trừ.');
            }

            $oldCoins = $userCoins->coins;

            // Check if user has enough diamonds
            if ($oldCoins < $request->coins) {
                throw new \Exception("Tài khoản chỉ có {$oldCoins} kim cương, không thể trừ {$request->coins} kim cương.");
            }

            $newCoins = $oldCoins - $request->coins;

            // Start database transaction
            DB::beginTransaction();

            // Generate transaction ID
            $transactionId = 'DEDUCT_' . time() . '_' . rand(1000, 9999);

            // Update user diamonds
            DB::table('user_coins')
                ->where('account_id', $account->ID)
                ->update([
                    'coins' => $newCoins,
                    'updated_at' => now()
                ]);

            // Log the deduction in coin_recharge_logs (with negative amount)
            $rechargeId = DB::table('coin_recharge_logs')->insertGetId([
                'account_id' => $account->ID,
                'username' => $account->UserName,
                'transaction_id' => $transactionId,
                'amount_vnd' => 0, // No VND involved in deduction
                'coins_added' => -$request->coins, // Negative for deduction
                'type' => 'manual_deduct',
                'status' => 'completed',
                'note' => $request->reason,
                'payment_method' => 'admin_deduct',
                'admin_id' => $admin['id'],
                'admin_username' => $admin['username'],
                'recharge_category' => 'admin_action',
                'ip_address' => $request->ip(),
                'user_agent' => $request->header('User-Agent'),
                'created_at' => now(),
                'updated_at' => now()
            ]);

            // Log admin action
            $this->logAdminAction(
                $admin,
                'manual_coin_deduct',
                'coin_deduct',
                $rechargeId,
                $account->UserName,
                ['coins' => $oldCoins],
                ['coins' => $newCoins, 'coins_deducted' => $request->coins],
                $request->reason,
                $request->ip()
            );

            // Commit transaction
            DB::commit();

            // Release lock
            if ($lockToken) {
                $this->activityService->releaseLock($lockToken);
            }

            return redirect()->route('admin.coin-recharge.index')
                ->with('success', "Đã trừ thành công {$request->coins} kim cương từ tài khoản {$request->username}. Mã giao dịch: {$transactionId}");

        } catch (\Exception $e) {
            // Rollback transaction
            DB::rollback();

            // Release lock
            if ($lockToken) {
                $this->activityService->releaseLock($lockToken);
            }

            Log::error('Diamond deduction failed: ' . $e->getMessage(), [
                'admin_id' => $admin['id'],
                'username' => $request->username,
                'diamonds_deducted' => $request->coins
            ]);

            return redirect()->back()
                ->withErrors(['error' => 'Có lỗi xảy ra khi trừ kim cương: ' . $e->getMessage()])
                ->withInput();
        }
    }



    /**
     * Show bulk recharge form
     */
    public function bulkForm()
    {
        $admin = Session::get('admin_user');
        return view('admin.coin-recharge.bulk', compact('admin'));
    }

    /**
     * Preview bulk recharge accounts
     */
    public function bulkPreview(Request $request)
    {
        $request->validate([
            'recharge_mode' => 'required|in:pattern,single',
            'coins_per_account' => 'required|integer|min:1|max:1000000',
        ]);

        $usernames = [];

        if ($request->recharge_mode === 'pattern') {
            $request->validate([
                'prefix' => 'required|string|max:20',
                'suffix_start' => 'required|integer|min:1|max:9999',
                'suffix_end' => 'required|integer|min:1|max:9999',
            ]);

            $prefix = $request->prefix;
            $start = $request->suffix_start;
            $end = $request->suffix_end;

            if ($start > $end) {
                return response()->json([
                    'success' => false,
                    'message' => 'Hậu tố bắt đầu phải nhỏ hơn hoặc bằng hậu tố kết thúc'
                ]);
            }

            $totalAccounts = $end - $start + 1;
            if ($totalAccounts > 100) {
                return response()->json([
                    'success' => false,
                    'message' => 'Không thể nạp cho quá 100 tài khoản cùng lúc'
                ]);
            }

            // Generate usernames for pattern mode
            for ($i = $start; $i <= $end; $i++) {
                $usernames[] = $prefix . $i;
            }
        } else {
            // Single mode
            $request->validate([
                'single_username' => 'required|string|max:50',
            ]);

            $usernames[] = $request->single_username;
        }

        // Check which accounts exist
        $existingAccounts = DB::table('t_account')
            ->whereIn('UserName', $usernames)
            ->select('UserName', 'ID', 'Email', 'Status')
            ->get()
            ->keyBy('UserName');

        // Get current coins for existing accounts
        $accountIds = $existingAccounts->pluck('ID')->toArray();
        $currentCoins = DB::table('user_coins')
            ->whereIn('account_id', $accountIds)
            ->select('account_id', 'coins')
            ->get()
            ->keyBy('account_id');

        // Prepare preview data
        $previewData = [];
        $validCount = 0;
        $totalCoins = 0;

        foreach ($usernames as $username) {
            $account = $existingAccounts->get($username);
            if ($account) {
                $coins = $currentCoins->get($account->ID);
                $previewData[] = [
                    'username' => $username,
                    'exists' => true,
                    'account_id' => $account->ID,
                    'email' => $account->Email,
                    'status' => $account->Status,
                    'current_coins' => $coins ? $coins->coins : 0,
                    'coins_to_add' => $request->coins_per_account,
                    'new_total' => ($coins ? $coins->coins : 0) + $request->coins_per_account
                ];
                $validCount++;
                $totalCoins += $request->coins_per_account;
            } else {
                $previewData[] = [
                    'username' => $username,
                    'exists' => false,
                    'account_id' => null,
                    'email' => null,
                    'status' => null,
                    'current_coins' => 0,
                    'coins_to_add' => 0,
                    'new_total' => 0
                ];
            }
        }

        return response()->json([
            'success' => true,
            'preview_data' => $previewData,
            'summary' => [
                'total_accounts' => count($usernames),
                'valid_accounts' => $validCount,
                'invalid_accounts' => count($usernames) - $validCount,
                'total_coins' => $totalCoins,
                'coins_per_account' => $request->coins_per_account
            ]
        ]);
    }

    /**
     * Process bulk recharge
     */
    public function bulkRecharge(Request $request)
    {
        $admin = Session::get('admin_user');

        $request->validate([
            'recharge_mode' => 'required|in:pattern,single',
            'coins_per_account' => 'required|integer|min:1|max:1000000',
            'amount_vnd_per_account' => 'required|numeric|min:0|max:********',
            'note' => 'required|string|max:255',
            'recharge_category' => 'required|in:customer,founder_team',
        ]);

        $usernames = [];
        $pattern = '';

        if ($request->recharge_mode === 'pattern') {
            $request->validate([
                'prefix' => 'required|string|max:20',
                'suffix_start' => 'required|integer|min:1|max:9999',
                'suffix_end' => 'required|integer|min:1|max:9999',
            ]);

            $prefix = $request->prefix;
            $start = $request->suffix_start;
            $end = $request->suffix_end;
            $pattern = "{$prefix}[{$start}-{$end}]";

            if ($start > $end) {
                return redirect()->back()->withErrors(['error' => 'Hậu tố bắt đầu phải nhỏ hơn hoặc bằng hậu tố kết thúc.']);
            }

            for ($i = $start; $i <= $end; $i++) {
                $usernames[] = $prefix . $i;
            }
        } else { // single mode
            $request->validate([
                'usernames' => 'required|string',
            ]);
            $usernames = preg_split('/[\s,]+/', $request->usernames, -1, PREG_SPLIT_NO_EMPTY);
            $pattern = 'list';
        }

        if (empty($usernames)) {
            return redirect()->back()->withErrors(['error' => 'Không có tài khoản nào được chỉ định để nạp.']);
        }

        // Get all existing accounts from the list
        $accountsToRecharge = DB::table('t_account')
            ->whereIn('UserName', $usernames)
            ->select('ID', 'UserName')
            ->get();

        if ($accountsToRecharge->isEmpty()) {
            return redirect()->back()->withErrors(['error' => 'Không tìm thấy tài khoản nào hợp lệ trong danh sách.']);
        }

        $coinsToAdd = (int)$request->coins_per_account;
        $amountVnd = (float)$request->amount_vnd_per_account;
        $note = $request->note;
        $category = $request->recharge_category;
        $adminId = $admin['id'];
        $adminUsername = $admin['username'];
        $ip = $request->ip();
        $userAgent = $request->header('User-Agent');
        $now = now();

        $accountIds = $accountsToRecharge->pluck('ID')->all();

        DB::beginTransaction();
        try {
            // 1. Separate existing and new user_coins records
            $existingUserCoinsIds = DB::table('user_coins')->whereIn('account_id', $accountIds)->pluck('account_id')->all();
            $newUserAccounts = $accountsToRecharge->whereNotIn('ID', $existingUserCoinsIds);

            // 2. Bulk UPDATE for existing user_coins records
            if (!empty($existingUserCoinsIds)) {
                DB::table('user_coins')
                    ->whereIn('account_id', $existingUserCoinsIds)
                    ->update([
                        'coins' => DB::raw("coins + {$coinsToAdd}"),
                        'total_recharged' => DB::raw("total_recharged + {$amountVnd}"),
                        'updated_at' => $now
                    ]);
            }

            // 3. Bulk INSERT for new user_coins records
            $newUserCoinsData = [];
            foreach ($newUserAccounts as $account) {
                $newUserCoinsData[] = [
                    'account_id' => $account->ID,
                    'username' => $account->UserName,
                    'coins' => $coinsToAdd,
                    'total_recharged' => $amountVnd,
                    'total_spent' => 0,
                    'created_at' => $now,
                    'updated_at' => $now
                ];
            }

            if (!empty($newUserCoinsData)) {
                DB::table('user_coins')->insert($newUserCoinsData);
            }

            // 4. Prepare and bulk INSERT recharge logs
            $rechargeLogs = [];
            foreach ($accountsToRecharge as $account) {
                 $rechargeLogs[] = [
                    'account_id' => $account->ID,
                    'username' => $account->UserName,
                    'transaction_id' => 'BULK_' . time() . '_' . $account->ID,
                    'amount_vnd' => $amountVnd,
                    'coins_added' => $coinsToAdd,
                    'type' => 'bulk_manual',
                    'status' => 'completed',
                    'recharge_category' => $category,
                    'note' => "Bulk recharge: {$pattern} - {$note}",
                    'admin_id' => $adminId,
                    'admin_username' => $adminUsername,
                    'ip_address' => $ip,
                    'user_agent' => $userAgent,
                    'completed_at' => $now,
                    'created_at' => $now,
                    'updated_at' => $now
                ];
            }

            if (!empty($rechargeLogs)) {
                DB::table('coin_recharge_logs')->insert($rechargeLogs);
            }

            // 5. Log admin action (single log for the whole bulk operation)
            $this->logAdminAction(
                $admin,
                'bulk_coin_recharge',
                'bulk_recharge',
                0, // Use 0 for bulk operations
                "Bulk recharge: {$pattern}",
                ['accounts_count' => $accountsToRecharge->count()],
                [
                    'accounts_count' => $accountsToRecharge->count(),
                    'coins_per_account' => $coinsToAdd,
                    'total_coins' => $accountsToRecharge->count() * $coinsToAdd,
                    'recharge_category' => $category
                ],
                $note,
                $ip
            );

            DB::commit();

            return redirect()->route('admin.coin-recharge.index')
                ->with('success', "Đã nạp thành công cho " . $accountsToRecharge->count() . " tài khoản. " .
                                  "Mỗi tài khoản nhận được {$coinsToAdd} kim cương.");

        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Bulk diamond recharge failed: ' . $e->getMessage(), [
                'admin_id' => $adminId,
                'pattern' => $pattern,
                'usernames' => $usernames,
            ]);
            return redirect()->back()
                ->withErrors(['error' => 'Có lỗi nghiêm trọng xảy ra khi nạp hàng loạt: ' . $e->getMessage()])
                ->withInput();
        }
    }

    private function logAdminAction($admin, $action, $targetType, $targetId, $targetName, $oldData, $newData, $reason, $ip)
    {
        DB::table('admin_action_logs')->insert([
            'admin_id' => $admin['id'],
            'admin_username' => $admin['username'],
            'action' => $action,
            'target_type' => $targetType,
            'target_id' => $targetId,
            'target_name' => $targetName,
            'old_data' => json_encode($oldData),
            'new_data' => json_encode($newData),
            'reason' => $reason,
            'ip_address' => $ip,
            'user_agent' => request()->header('User-Agent'),
            'created_at' => now(),
            'updated_at' => now()
        ]);
    }
}
