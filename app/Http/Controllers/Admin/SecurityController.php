<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\AdminUser;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Session;

class SecurityController extends Controller
{
    public function index()
    {
        $admin = Session::get('admin_user');
        
        // Only super admin can access security dashboard
        if ($admin['role'] !== 'super_admin') {
            return redirect()->route('admin.dashboard')
                ->withErrors(['error' => 'Bạn không có quyền truy cập trang bảo mật.']);
        }

        // Get security statistics
        $stats = $this->getSecurityStats();
        
        // Get recent login attempts
        $recentLogins = DB::table('admin_action_logs')
            ->where('action', 'login')
            ->orderBy('created_at', 'desc')
            ->limit(20)
            ->get();

        return view('admin.security.index', compact('admin', 'stats', 'recentLogins'));
    }

    private function getSecurityStats()
    {
        $today = now()->startOfDay();
        
        return [
            'active_admins' => AdminUser::where('is_active', true)->count(),
            'total_admins' => AdminUser::count(),
            'successful_logins_today' => DB::table('admin_action_logs')
                ->where('action', 'login')
                ->where('created_at', '>=', $today)
                ->count(),
            'failed_logins_today' => DB::table('admin_action_logs')
                ->where('action', 'login_failed')
                ->where('created_at', '>=', $today)
                ->count(),
            'blocked_ips' => 0, // Placeholder for IP blocking feature
            'password_expired_count' => AdminUser::where('is_active', true)
                ->where('updated_at', '<', now()->subDays(90))
                ->count(),
        ];
    }

    public function clearFailedLogins(Request $request)
    {
        $admin = Session::get('admin_user');
        
        if ($admin['role'] !== 'super_admin') {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        // Clear failed login logs older than 7 days
        $deleted = DB::table('admin_action_logs')
            ->where('action', 'login_failed')
            ->where('created_at', '<', now()->subDays(7))
            ->delete();

        // Log this action
        DB::table('admin_action_logs')->insert([
            'admin_id' => $admin['id'],
            'admin_username' => $admin['username'],
            'action' => 'clear_failed_logins',
            'target_type' => 'system',
            'target_id' => 0,
            'target_name' => 'failed_login_logs',
            'old_data' => json_encode(['count' => $deleted]),
            'new_data' => json_encode([]),
            'reason' => 'Xóa log đăng nhập thất bại',
            'ip_address' => $request->ip(),
            'user_agent' => $request->header('User-Agent'),
            'created_at' => now(),
            'updated_at' => now()
        ]);

        return response()->json([
            'success' => true,
            'message' => "Đã xóa {$deleted} bản ghi đăng nhập thất bại"
        ]);
    }

    public function forceLogoutAll(Request $request)
    {
        $admin = Session::get('admin_user');
        
        if ($admin['role'] !== 'super_admin') {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        // This would require implementing a session management system
        // For now, we'll just log the action
        
        // Log this action
        DB::table('admin_action_logs')->insert([
            'admin_id' => $admin['id'],
            'admin_username' => $admin['username'],
            'action' => 'force_logout_all',
            'target_type' => 'system',
            'target_id' => 0,
            'target_name' => 'all_admin_sessions',
            'old_data' => json_encode([]),
            'new_data' => json_encode([]),
            'reason' => 'Đăng xuất tất cả admin',
            'ip_address' => $request->ip(),
            'user_agent' => $request->header('User-Agent'),
            'created_at' => now(),
            'updated_at' => now()
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Đã gửi lệnh đăng xuất tất cả admin (chức năng sẽ được triển khai đầy đủ trong phiên bản tiếp theo)'
        ]);
    }
}
