<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Session;

class LanguageController extends Controller
{
    /**
     * Switch language
     *
     * @param string $locale
     * @return \Illuminate\Http\RedirectResponse
     */
    public function switchLanguage($locale)
    {
        // Validate locale
        $supportedLocales = ['en', 'vi'];
        
        if (!in_array($locale, $supportedLocales)) {
            return redirect()->back()->with('error', 'Ngôn ngữ không được hỗ trợ.');
        }

        // Set application locale
        App::setLocale($locale);
        
        // Store locale in session
        Session::put('locale', $locale);
        
        // Get success message based on locale
        $message = $locale === 'vi' 
            ? '<PERSON><PERSON> chuyển sang tiếng Việt thành công!' 
            : 'Language switched to English successfully!';
        
        return redirect()->back()->with('success', $message);
    }

    /**
     * Get current locale
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCurrentLocale()
    {
        return response()->json([
            'locale' => App::getLocale(),
            'available_locales' => [
                'en' => 'English',
                'vi' => 'Tiếng Việt'
            ]
        ]);
    }
}
