<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Services\LogoutService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\Account;
use App\Models\UserAccount;
use App\Models\UserCoinBalance;
use App\Services\AuthService;

class AuthController extends Controller
{
    protected $authService;

    public function __construct(AuthService $authService)
    {
        $this->authService = $authService;
    }
    public function showLogin()
    {
        // If already logged in, redirect to dashboard
        if (Session::has('user_account')) {
            return redirect('/user/dashboard');
        }

        return view('user.auth.login');
    }

    public function login(Request $request)
    {
        $request->validate([
            'username' => 'required|string',
            'password' => 'required|string',
        ]);

        // Use AuthService for validation
        $result = $this->authService->validateCredentials($request->username, $request->password);

        if (!$result['success']) {
            $this->authService->logAuthEvent('login_failed', [
                'username' => $request->username,
                'ip' => $request->ip(),
                'message' => $result['message']
            ]);
            return back()->withErrors(['login' => $result['message']])->withInput();
        }

        $user = $result['user'];

        // Update last login time asynchronously (non-blocking)
        dispatch(function () use ($user) {
            $user->update(['LastLoginTime' => now()]);
        })->afterResponse();

        // Create session
        Session::put('user_account', $this->authService->createUserSession($user));

        $this->authService->logAuthEvent('login_success', [
            'user_id' => $user->ID,
            'username' => $user->UserName,
            'ip' => $request->ip()
        ]);

        return redirect('/user/dashboard')->with('success', 'Đăng nhập thành công!');
    }

    public function showRegister()
    {
        if (Session::has('user_account')) {
            return redirect('/user/dashboard');
        }

        return view('user.auth.register');
    }

    public function register(Request $request)
    {
        $request->validate([
            'username' => 'required|string|min:4|max:20|alpha_num',
            'email' => 'required|email|max:255',
            'password' => 'required|string|min:4|max:32|confirmed',
            'phone' => 'nullable|string|max:20',
        ]);

        // Check username and email existence using cache
        if ($this->authService->usernameExists($request->username)) {
            return back()->withErrors(['username' => 'Tên đăng nhập đã tồn tại.'])->withInput();
        }

        if ($this->authService->emailExists($request->email)) {
            return back()->withErrors(['email' => 'Email đã được sử dụng.'])->withInput();
        }

        try {
            // Create account in t_account table
            $account = Account::create([
                'UserName' => $request->username,
                'Password' => strtoupper(md5($request->password)),
                'Email' => $request->email,
                'CreateTime' => now(),
                'LastLoginTime' => now(),
                'Status' => 1, // Active
                'DeviceID' => '',
                'Session' => '',
            ]);

            // Auto login after registration
            Session::put('user_account', $this->authService->createUserSession($account));

            Log::info('User registered', [
                'user_id' => $account->ID,
                'username' => $account->UserName,
                'ip' => $request->ip()
            ]);

            return redirect('/user/dashboard')->with('success', 'Đăng ký thành công! Chào mừng bạn đến với MU Game Portal.');
        } catch (\Exception $e) {
            Log::error('Registration failed', [
                'username' => $request->username,
                'email' => $request->email,
                'error' => $e->getMessage()
            ]);
            return back()->withErrors([
                'register' => 'Có lỗi xảy ra trong quá trình đăng ký. Vui lòng thử lại.',
            ])->withInput();
        }
    }

    public function logout(Request $request = null)
    {
        // Use LogoutService for safe logout
        $success = LogoutService::logoutUser($request);

        if ($success) {
            return redirect('/user/login')->with('success', 'Đã đăng xuất thành công!');
        } else {
            return redirect('/user/login')->with('warning', 'Đã đăng xuất nhưng có một số lỗi nhỏ.');
        }
    }

    public function showForgotPassword()
    {
        return view('user.auth.forgot-password');
    }

    public function forgotPassword(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
        ]);

        // Check if email exists in t_account table
        $account = Account::where('Email', $request->email)->first();
        if (!$account) {
            return back()->withErrors([
                'email' => 'Email không tồn tại trong hệ thống.',
            ])->withInput();
        }

        // TODO: Implement password reset functionality
        // For now, just show a message
        return back()->with('success', 'Chức năng đặt lại mật khẩu sẽ được cập nhật sớm. Vui lòng liên hệ admin để được hỗ trợ.');
    }
}
