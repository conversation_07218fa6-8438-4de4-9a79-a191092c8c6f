<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Services\CacheService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\DB;
use App\Models\Account;

class DashboardController extends Controller
{
    public function index()
    {
        $userSession = Session::get('user_account');

        if (!$userSession) {
            return redirect('/user/login')->withErrors(['login' => 'Vui lòng đăng nhập để tiếp tục.']);
        }

        // Get user from cache first, then database
        $user = CacheService::getUserAccount($userSession['id']);

        if (!$user) {
            return redirect('/user/login')->withErrors(['login' => 'Tài khoản không tồn tại.']);
        }

        // Get user coins info
        $userCoins = DB::table('user_coins')->where('account_id', $user->ID)->first();
        if (!$userCoins) {
            // Create initial coin record if not exists
            DB::table('user_coins')->insert([
                'account_id' => $user->ID,
                'username' => $user->UserName,
                'coins' => 0,
                'total_recharged' => 0,
                'total_spent' => 0,
                'created_at' => now(),
                'updated_at' => now()
            ]);
            $userCoins = (object) [
                'coins' => 0,
                'total_recharged' => 0,
                'total_spent' => 0
            ];
        }

        // Get detailed statistics
        $stats = $this->getUserStatistics($user, $userCoins);

        return view('user.dashboard', compact('user', 'stats', 'userCoins'));
    }

    /**
     * Get comprehensive user statistics
     */
    private function getUserStatistics($user, $userCoins)
    {
        try {
            // Get recharge statistics
            $rechargeStats = DB::table('coin_recharge_logs')
                ->where('account_id', $user->ID)
                ->selectRaw('
                    COUNT(*) as total_transactions,
                    COALESCE(SUM(CASE WHEN status = "completed" THEN amount_vnd ELSE 0 END), 0) as total_recharged_vnd,
                    COALESCE(SUM(CASE WHEN status = "completed" THEN coins_added ELSE 0 END), 0) as total_coins_received,
                    COUNT(CASE WHEN status = "pending" THEN 1 END) as pending_transactions,
                    COUNT(CASE WHEN status = "completed" THEN 1 END) as completed_transactions
                ')
                ->first();

            // Get withdraw statistics
            $withdrawStats = DB::connection('game_mysql')->table('coin_withdraw_logs')
                ->where('account_id', $user->ID)
                ->selectRaw('
                    COUNT(*) as total_withdraws,
                    COALESCE(SUM(amount), 0) as total_withdrawn
                ')
                ->first();

            // Get giftcode usage statistics
            $giftcodeStats = DB::table('giftcode_usage_logs')
                ->where('user_id', $user->ID)
                ->selectRaw('
                    COUNT(*) as total_giftcodes_used,
                    COUNT(CASE WHEN status = "success" THEN 1 END) as successful_giftcodes
                ')
                ->first();

            // Get game character statistics
            $characterStats = DB::connection('game_mysql')->table('t_roles')
                ->where('userid', 'ZT' . str_pad($user->ID, 4, '0', STR_PAD_LEFT))
                ->where('isdel', 0)
                ->selectRaw('
                    COUNT(*) as total_characters,
                    MAX(rlevel) as highest_level,
                    COUNT(CASE WHEN rlevel >= 400 THEN 1 END) as high_level_chars
                ')
                ->first();

            // Calculate current coins from user_coins table
            $currentCoins = $userCoins->coins ?? 0;

            return [
                'username' => $user->UserName,
                'email' => $user->Email,
                'status' => $user->getStatusText(),
                'created_at' => $user->CreateTime,
                'last_login' => $user->LastLoginTime,

                // Coin statistics
                'coins' => $currentCoins,
                'total_recharged_vnd' => $rechargeStats->total_recharged_vnd ?? 0,
                'total_coins_received' => $rechargeStats->total_coins_received ?? 0,
                'total_withdrawn' => $withdrawStats->total_withdrawn ?? 0,

                // Transaction statistics
                'total_transactions' => $rechargeStats->total_transactions ?? 0,
                'completed_transactions' => $rechargeStats->completed_transactions ?? 0,
                'pending_transactions' => $rechargeStats->pending_transactions ?? 0,
                'total_withdraws' => $withdrawStats->total_withdraws ?? 0,

                // Giftcode statistics
                'total_giftcodes_used' => $giftcodeStats->total_giftcodes_used ?? 0,
                'successful_giftcodes' => $giftcodeStats->successful_giftcodes ?? 0,

                // Game statistics
                'total_characters' => $characterStats->total_characters ?? 0,
                'highest_level' => $characterStats->highest_level ?? 0,
                'high_level_chars' => $characterStats->high_level_chars ?? 0,
            ];

        } catch (\Exception $e) {
            // Return basic stats if there's an error
            return [
                'username' => $user->UserName,
                'email' => $user->Email,
                'status' => $user->getStatusText(),
                'created_at' => $user->CreateTime,
                'last_login' => $user->LastLoginTime,
                'coins' => $userCoins->coins ?? 0,
                'total_recharged_vnd' => 0,
                'total_transactions' => 0,
                'total_giftcodes_used' => 0,
                'total_characters' => 0,
                'highest_level' => 0,
            ];
        }
    }
}
