<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\DB;
use App\Models\UserAccount;
use App\Models\GiftcodeUsageLog;
use App\Models\UserTransactionLog;
use App\Models\Account;
use App\Models\Giftcode;
use App\Models\GiftcodeLog;
use App\Services\GameCacheService;
use App\Services\GiftcodeService;
use Carbon\Carbon;

class GiftcodeController extends Controller
{
    protected $gameCacheService;
    protected $giftcodeService;

    public function __construct(GameCacheService $gameCacheService, GiftcodeService $giftcodeService)
    {
        $this->gameCacheService = $gameCacheService;
        $this->giftcodeService = $giftcodeService;
    }
    public function index()
    {
        $userSession = Session::get('user_account');
        if (!$userSession) {
            return redirect()->route('user.login');
        }

        $user = Account::find($userSession['id']);

        // Get user coins
        $userCoins = DB::table('user_coins')->where('account_id', $user->ID)->first();
        if (!$userCoins) {
            $userCoins = (object) ['coins' => 0, 'total_recharged' => 0, 'total_spent' => 0];
        }

        // Get user's characters
        $gameUserId = 'ZT' . str_pad($user->ID, 4, '0', STR_PAD_LEFT);
        $characters = DB::connection('game_mysql')
            ->table('t_roles')
            ->where('userid', $gameUserId)
            ->where('isdel', 0)
            ->orderBy('lasttime', 'desc')
            ->select('rid', 'rname', 'level', 'occupation', 'lasttime')
            ->get();

        // Get available giftcodes from t_giftcode
        $availableGiftcodes = Giftcode::where(function($query) {
                $query->where('period', 0)
                      ->orWhereRaw('DATE_ADD(created_at, INTERVAL period DAY) > NOW()');
            })
            ->where(function($query) {
                $query->where('limit', 0)
                      ->orWhereRaw('(SELECT COUNT(*) FROM t_giftcode_log WHERE groupid = t_giftcode.id) < t_giftcode.limit');
            })
            ->select('id', 'content', 'type', 'period', 'limit', 'created_at', 'code', 'items')
            ->orderBy('created_at', 'desc')
            ->take(10)
            ->get();

        // Add usage statistics for giftcodes
        foreach ($availableGiftcodes as $giftcode) {
            $giftcode->used_count = $giftcode->getUsageCount();
            $giftcode->remaining_uses = $giftcode->limit > 0 ? max(0, $giftcode->limit - $giftcode->used_count) : 'Không giới hạn';
            $giftcode->codes_display = implode(', ', array_slice($giftcode->getCodesArray(), 0, 3)); // Show first 3 codes
            $giftcode->items_display = $this->formatItemsForDisplay($giftcode->getItemsArray());
        }

        // Get giftcode usage history
        $usageHistory = GiftcodeLog::where('uid', $user->ID)
            ->orderBy('created_at', 'desc')
            ->take(10)
            ->get();

        return view('user.giftcode.index', compact(
            'user',
            'userCoins',
            'usageHistory',
            'characters',
            'availableGiftcodes'
        ));
    }

    public function redeem(Request $request)
    {
        $request->validate([
            'giftcode' => 'required|string|min:4|max:20',
            'character_id' => 'required|integer'
        ]);

        $userSession = Session::get('user_account');
        if (!$userSession) {
            return response()->json(['success' => false, 'message' => 'Vui lòng đăng nhập']);
        }

        $user = Account::find($userSession['id']);
        $code = strtoupper(trim($request->giftcode));
        $characterId = $request->character_id;
        $zoneId = 1; // Default zone

        // Use the new GiftcodeService for processing
        $result = $this->giftcodeService->useGiftcode(
            $code,
            $user->ID,
            $characterId,
            $zoneId
        );

        // Log the result for compatibility with old system
        if ($result['success']) {
            GiftcodeUsageLog::logSuccess(
                $user->ID,
                $code,
                'Giftcode used successfully',
                $result['items'] ?? [],
                null
            );
        } else {
            GiftcodeUsageLog::logFailure(
                $user->ID,
                $code,
                $result['message'],
                'failed'
            );
        }

        return response()->json($result);
    }

    public function history(Request $request)
    {
        try {
            $userSession = Session::get('user_account');
            if (!$userSession) {
                return response()->json([
                    'success' => false,
                    'message' => 'Bạn cần đăng nhập!'
                ], 401);
            }

            $user = Account::find($userSession['id']);
            $userId = $user->ID;

            $history = GiftcodeLog::where('uid', $userId)
                ->orderBy('created_at', 'desc')
                ->take(20)
                ->get();

            return response()->json([
                'success' => true,
                'data' => $history
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi tải lịch sử!'
            ], 500);
        }
    }

    public function getActiveGiftcodes()
    {
        try {
            // Get available giftcodes from t_giftcode
            $giftcodes = Giftcode::where(function($query) {
                    $query->where('period', 0)
                          ->orWhereRaw('DATE_ADD(created_at, INTERVAL period DAY) > NOW()');
                })
                ->where(function($query) {
                    $query->where('limit', 0)
                          ->orWhereRaw('(SELECT COUNT(*) FROM t_giftcode_log WHERE groupid = t_giftcode.id) < t_giftcode.limit');
                })
                ->select('id', 'content', 'type', 'period', 'limit', 'created_at', 'code', 'items')
                ->orderBy('created_at', 'desc')
                ->take(10)
                ->get();

            // Add usage statistics for giftcodes
            foreach ($giftcodes as $giftcode) {
                $giftcode->used_count = $giftcode->getUsageCount();
                $giftcode->remaining_uses = $giftcode->limit > 0 ? max(0, $giftcode->limit - $giftcode->used_count) : 'Không giới hạn';
                $giftcode->codes_display = implode(', ', array_slice($giftcode->getCodesArray(), 0, 3));
                $giftcode->items_display = $this->formatItemsForDisplay($giftcode->getItemsArray());
            }

            return response()->json([
                'success' => true,
                'data' => $giftcodes
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi tải danh sách giftcode!'
            ], 500);
        }
    }

    // Mock validation method - replace with real giftcode validation
    private function validateGiftcode($giftcode, $userId)
    {
        // Mock giftcode database
        $validGiftcodes = [
            'WELCOME2025' => [
                'id' => 1,
                'name' => 'Giftcode chào mừng năm mới',
                'rewards' => ['1000 Coin', '5 Jewel of Bless', '10 Jewel of Soul'],
                'expires_at' => '2025-02-28',
                'max_uses' => 1000,
                'used_count' => 234,
                'type' => 'public'
            ],
            'VIPONLY123' => [
                'id' => 2,
                'name' => 'Giftcode dành cho VIP',
                'rewards' => ['5000 Coin', '1 Jewel of Life', '20 Jewel of Chaos'],
                'expires_at' => '2025-01-31',
                'max_uses' => 100,
                'used_count' => 67,
                'type' => 'vip'
            ],
            'EVENT2025' => [
                'id' => 3,
                'name' => 'Sự kiện Tết Nguyên Đán',
                'rewards' => ['2000 Coin', '10 Jewel of Bless', '1 Box of Luck'],
                'expires_at' => '2025-02-15',
                'max_uses' => 500,
                'used_count' => 123,
                'type' => 'event'
            ],
            'TESTCODE' => [
                'id' => 4,
                'name' => 'Test Giftcode',
                'rewards' => ['500 Coin'],
                'expires_at' => '2025-12-31',
                'max_uses' => 10,
                'used_count' => 2,
                'type' => 'public'
            ]
        ];

        // Check if giftcode exists
        if (!isset($validGiftcodes[$giftcode])) {
            return [
                'valid' => false,
                'error' => 'Mã giftcode không hợp lệ.',
                'status' => 'invalid'
            ];
        }

        $giftcodeData = $validGiftcodes[$giftcode];

        // Check if expired
        if (strtotime($giftcodeData['expires_at']) < time()) {
            return [
                'valid' => false,
                'error' => 'Giftcode đã hết hạn.',
                'status' => 'expired'
            ];
        }

        // Check if max uses reached
        if ($giftcodeData['used_count'] >= $giftcodeData['max_uses']) {
            return [
                'valid' => false,
                'error' => 'Giftcode đã hết lượt sử dụng.',
                'status' => 'used'
            ];
        }

        // Check if user already used this giftcode
        $alreadyUsed = GiftcodeUsageLog::where('user_id', $userId)
            ->where('giftcode', $giftcode)
            ->where('status', 'success')
            ->exists();

        if ($alreadyUsed) {
            return [
                'valid' => false,
                'error' => 'Bạn đã sử dụng giftcode này rồi.',
                'status' => 'used'
            ];
        }

        return [
            'valid' => true,
            'id' => $giftcodeData['id'],
            'name' => $giftcodeData['name'],
            'rewards' => $giftcodeData['rewards']
        ];
    }

    /**
     * Send rewards to game mail system (following old giftcode logic)
     */
    private function sendRewardsToGame($character, $rewards, $giftcodeName)
    {
        // Create mail (following old code structure)
        $mailid = DB::connection('game_mysql')->table('t_mail')->insertGetId([
            'senderrid' => 0,
            'senderrname' => 'GM',
            'sendtime' => now(),
            'receiverrid' => $character->rid,
            'reveiverrname' => $character->rname,
            'subject' => 'Phần thưởng Giftcode: ' . $giftcodeName,
            'content' => 'Bạn đã nhận được phần thưởng từ giftcode: ' . $giftcodeName
        ]);

        // Add items to mail (following old code format)
        $mailItems = [];

        foreach ($rewards as $reward) {
            if ($reward['type'] === 'item') {
                // Parse item string format from old code: "goodsid,count,binding,forge_level,appendproplev,lucky,excellenceinfo"
                $itemData = $reward['data'];
                if (is_string($itemData)) {
                    $parts = explode(',', $itemData);
                    if (count($parts) >= 7) {
                        $mailItems[] = [
                            'mailid' => $mailid,
                            'goodsid' => $parts[0],
                            'gcount' => $parts[1],
                            'binding' => $parts[2],
                            'forge_level' => $parts[3],
                            'appendproplev' => $parts[4],
                            'lucky' => $parts[5],
                            'excellenceinfo' => $parts[6],
                        ];
                    }
                } elseif (is_array($itemData)) {
                    $mailItems[] = [
                        'mailid' => $mailid,
                        'goodsid' => $itemData['goodsid'] ?? 0,
                        'gcount' => $itemData['count'] ?? 1,
                        'binding' => $itemData['binding'] ?? 1,
                        'forge_level' => $itemData['forge_level'] ?? 0,
                        'appendproplev' => $itemData['appendproplev'] ?? 0,
                        'lucky' => $itemData['lucky'] ?? 0,
                        'excellenceinfo' => $itemData['excellenceinfo'] ?? 0,
                    ];
                }
            } elseif ($reward['type'] === 'coins') {
                // Add coins to user account
                $this->addCoinsToUser($character->userid, $reward['amount']);
            }
        }

        if (!empty($mailItems)) {
            DB::connection('game_mysql')->table('t_mailgoods')->insert($mailItems);
        }
    }

    /**
     * Add coins to user account
     */
    private function addCoinsToUser($gameUserId, $amount)
    {
        // Extract account ID from game user ID (remove ZT prefix and leading zeros)
        $accountId = (int) substr($gameUserId, 2);

        $userCoins = DB::table('user_coins')->where('account_id', $accountId)->first();

        if ($userCoins) {
            DB::table('user_coins')
                ->where('account_id', $accountId)
                ->update([
                    'coins' => DB::raw('coins + ' . $amount),
                    'updated_at' => now()
                ]);
        } else {
            $account = DB::table('t_account')->where('ID', $accountId)->first();
            if ($account) {
                DB::table('user_coins')->insert([
                    'account_id' => $accountId,
                    'username' => $account->UserName,
                    'coins' => $amount,
                    'total_recharged' => 0,
                    'total_spent' => 0,
                    'created_at' => now(),
                    'updated_at' => now()
                ]);
            }
        }
    }

    /**
     * Format rewards for log storage
     */
    private function formatRewardsForLog($rewards)
    {
        $formatted = [];
        foreach ($rewards as $reward) {
            if ($reward['type'] === 'item') {
                $formatted[] = $reward['name'] ?? 'Item';
            } elseif ($reward['type'] === 'coins') {
                $formatted[] = $reward['amount'] . ' coins';
            }
        }
        return $formatted;
    }

    /**
     * Format items for display
     */
    private function formatItemsForDisplay($items)
    {
        if (!is_array($items)) {
            return [];
        }

        $formatted = [];
        foreach ($items as $item) {
            $parts = explode(',', $item);
            if (count($parts) >= 2) {
                $formatted[] = [
                    'id' => $parts[0],
                    'name' => $this->getItemName($parts[0]),
                    'quantity' => $parts[1],
                    'binding' => $parts[2] ?? 0
                ];
            }
        }
        return $formatted;
    }

    /**
     * Get item name by ID
     */
    private function getItemName($itemId)
    {
        // This should be implemented based on your item database structure
        // For now, return a generic name
        return "Item #{$itemId}";
    }

    /**
     * Get user's characters for dropdown
     */
    public function getCharacters()
    {
        try {
            $userSession = Session::get('user_account');
            if (!$userSession) {
                return response()->json([
                    'success' => false,
                    'message' => 'Bạn cần đăng nhập!'
                ], 401);
            }

            $user = Account::find($userSession['id']);
            $gameUserId = 'ZT' . str_pad($user->ID, 4, '0', STR_PAD_LEFT);

            $characters = DB::connection('game_mysql')
                ->table('t_roles')
                ->where('userid', $gameUserId)
                ->where('isdel', 0)
                ->orderBy('lasttime', 'desc')
                ->select('rid', 'rname', 'level', 'occupation', 'lasttime')
                ->get();

            return response()->json([
                'success' => true,
                'data' => $characters
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi tải danh sách nhân vật!'
            ], 500);
        }
    }

    private function markGiftcodeAsUsed($giftcode, $userId)
    {
        // In real implementation, increment used_count in giftcodes table
        // For now, just log that it was marked as used
        \Log::info("Giftcode {$giftcode} marked as used by user {$userId}");
    }
}
