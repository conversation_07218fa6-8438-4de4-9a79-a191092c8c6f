<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use App\Models\Account;

class ProfileController extends Controller
{
    /**
     * Show user profile
     */
    public function index()
    {
        $userSession = Session::get('user_account');
        
        if (!$userSession) {
            return redirect('/user/login')->withErrors(['login' => 'Vui lòng đăng nhập để tiếp tục.']);
        }

        $user = Account::find($userSession['id']);
        
        if (!$user) {
            return redirect('/user/login')->withErrors(['login' => 'Tài khoản không tồn tại.']);
        }

        // Mask email and phone for display
        if ($user) {
            // Mask email
            if (!empty($user->Email)) {
                $emailParts = explode('@', $user->Email);
                if (count($emailParts) === 2) {
                    $localPart = $emailParts[0];
                    $domain = $emailParts[1];
                    if (strlen($localPart) > 4) {
                        $maskedLocalPart = substr($localPart, 0, 4) . str_repeat('*', strlen($localPart) - 4);
                    } else {
                        $maskedLocalPart = $localPart; // Show as is if too short
                    }
                    $user->Email = $maskedLocalPart . '@' . $domain;
                }
            }

            // Mask phone
            if (!empty($user->Phone)) {
                $phone = $user->Phone;
                if (strlen($phone) > 4) {
                    $user->Phone = substr($phone, 0, 4) . str_repeat('*', strlen($phone) - 4);
                }
            }
        }

        // Get user coins info
        $userCoins = DB::table('user_coins')->where('account_id', $user->ID)->first();
        
        // Get game characters
        $gameUserId = 'ZT' . str_pad($user->ID, 4, '0', STR_PAD_LEFT);
        $characters = DB::connection('game_mysql')
            ->table('t_roles')
            ->where('userid', $gameUserId)
            ->where('isdel', 0)
            ->select('rid', 'rname', 'level', 'occupation', 'lasttime')
            ->orderBy('lasttime', 'desc')
            ->get();

        // Get recent activities
        $recentActivities = $this->getRecentActivities($user->ID);

        return view('user.profile.index', compact('user', 'userCoins', 'characters', 'recentActivities'));
    }

    /**
     * Update user profile information
     */
    public function update(Request $request)
    {
        return response()->json([
            'success' => false,
            'message' => 'Chức năng tự cập nhật thông tin đã bị tắt. Vui lòng liên hệ Ban Quản Trị để được hỗ trợ.'
        ]);
    }

    /**
     * Change password
     */
    public function changePassword(Request $request)
    {
        return response()->json([
            'success' => false,
            'message' => 'Chức năng tự đổi mật khẩu đã bị tắt. Vui lòng liên hệ Ban Quản Trị để được hỗ trợ.'
        ]);
    }

    /**
     * Get user's recent activities
     */
    private function getRecentActivities($accountId)
    {
        try {
            $activities = [];

            // Get recent recharge activities
            $recharges = DB::table('coin_recharge_logs')
                ->where('account_id', $accountId)
                ->select('created_at', 'amount_vnd as amount', 'status', 'type')
                ->orderBy('created_at', 'desc')
                ->limit(5)
                ->get();

            foreach ($recharges as $recharge) {
                $activities[] = [
                    'type' => 'recharge',
                    'description' => "Nạp {$recharge->amount} VNĐ",
                    'status' => $recharge->status,
                    'created_at' => $recharge->created_at,
                    'icon' => 'fas fa-plus-circle',
                    'color' => $recharge->status === 'completed' ? 'success' : 'warning'
                ];
            }

            // Get recent giftcode usage
            $giftcodes = DB::table('giftcode_usage_logs')
                ->where('user_id', $accountId)
                ->select('created_at', 'giftcode_id', 'status')
                ->orderBy('created_at', 'desc')
                ->limit(3)
                ->get();

            foreach ($giftcodes as $giftcode) {
                $activities[] = [
                    'type' => 'giftcode',
                    'description' => "Sử dụng giftcode #{$giftcode->giftcode_id}",
                    'status' => $giftcode->status,
                    'created_at' => $giftcode->created_at,
                    'icon' => 'fas fa-gift',
                    'color' => $giftcode->status === 'success' ? 'success' : 'danger'
                ];
            }

            // Sort by created_at desc
            usort($activities, function($a, $b) {
                return strtotime($b['created_at']) - strtotime($a['created_at']);
            });

            return array_slice($activities, 0, 10);

        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * Get account statistics for profile
     */
    public function getStats()
    {
        $userSession = Session::get('user_account');
        $user = Account::find($userSession['id']);

        try {
            // Get coin balance
            $userCoins = DB::table('user_coins')->where('account_id', $user->ID)->first();

            // Get recharge stats
            $rechargeStats = DB::table('coin_recharge_logs')
                ->where('account_id', $user->ID)
                ->where('status', 'completed')
                ->selectRaw('
                    COUNT(*) as total_transactions,
                    SUM(amount_vnd) as total_recharged
                ')
                ->first();

            // Get giftcode stats
            $giftcodeStats = DB::table('giftcode_usage_logs')
                ->where('user_id', $user->ID)
                ->where('status', 'success')
                ->count();

            return response()->json([
                'success' => true,
                'stats' => [
                    'coins' => $userCoins->coins ?? 0,
                    'total_recharged' => $rechargeStats->total_recharged ?? 0,
                    'transactions' => $rechargeStats->total_transactions ?? 0,
                    'giftcodes_used' => $giftcodeStats
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Không thể tải thống kê'
            ]);
        }
    }
}
