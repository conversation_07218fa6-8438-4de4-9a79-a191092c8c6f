<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\DB;
use App\Models\Account;
use Carbon\Carbon;

class WithdrawController extends Controller
{
    public function index()
    {
        $userSession = Session::get('user_account');
        $user = Account::find($userSession['id']);

        if (!$user) {
            return redirect('/user/login')->withErrors(['login' => 'Tài khoản không tồn tại.']);
        }

        // Get user's diamond balances from user_coins table
        $userCoins = DB::table('user_coins')->where('account_id', $user->ID)->first();
        if (!$userCoins) {
            // Create initial diamond balance record
            DB::table('user_coins')->insert([
                'account_id' => $user->ID,
                'username' => $user->UserName,
                'coins' => 0,
                'total_recharged' => 0,
                'total_spent' => 0,
                'created_at' => now(),
                'updated_at' => now()
            ]);
            $userCoins = (object) [
                'coins' => 0,
                'total_recharged' => 0,
                'total_spent' => 0
            ];
        }

        // Get game coins and characters
        $gameMoney = $user->getGameMoney();
        $gameCharacters = $user->getGameCharacters();

        // Get recent withdraw transactions from coin_spend_logs
        $recentWithdraws = DB::table('coin_spend_logs')
            ->where('account_id', $user->ID)
            ->where('item_type', 'withdraw')
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        // Get withdraw statistics
        $stats = $this->getWithdrawStats($user->ID);

        return view('user.withdraw.index', compact(
            'user',
            'userCoins',
            'gameMoney',
            'gameCharacters',
            'recentWithdraws',
            'stats'
        ));
    }

    public function withdraw(Request $request)
    {
        $request->validate([
            'amount' => 'required|integer|min:1',
            'character_id' => 'required|integer',
        ], [
            'amount.required' => 'Vui lòng nhập số kim cương muốn rút',
            'amount.integer' => 'Số kim cương phải là số nguyên',
            'amount.min' => 'Số kim cương tối thiểu là 1',
            'character_id.required' => 'Vui lòng chọn nhân vật',
            'character_id.integer' => 'ID nhân vật không hợp lệ',
        ]);

        $userSession = Session::get('user_account');
        $user = Account::find($userSession['id']);
        $amount = $request->amount;
        $characterId = $request->character_id;

        // Get user coins balance
        $userCoins = DB::table('user_coins')->where('account_id', $user->ID)->first();
        if (!$userCoins || $userCoins->coins < $amount) {
            return back()->withErrors(['error' => "Không đủ kim cương. Bạn có " . number_format($userCoins->coins ?? 0) . " kim cương."]);
        }

        // Check if character belongs to user
        $character = $user->getGameCharacters()->where('rid', $characterId)->first();
        if (!$character) {
            return back()->withErrors(['error' => 'Nhân vật không tồn tại hoặc không thuộc về bạn.']);
        }

        // Check daily withdraw limit (500,000 coins per day)
        $dailyLimit = 500000;
        $todayWithdraws = DB::table('coin_spend_logs')
            ->where('account_id', $user->ID)
            ->where('item_type', 'withdraw')
            ->whereDate('created_at', today())
            ->sum('coins_spent');

        if (($todayWithdraws + $amount) > $dailyLimit) {
            return back()->withErrors(['error' => "Vượt quá giới hạn rút kim cương hàng ngày (" . number_format($dailyLimit) . " kim cương). Hôm nay bạn đã rút " . number_format($todayWithdraws) . " kim cương."]);
        }

        try {
            DB::beginTransaction();

            // Get current balances
            $coinBalanceBefore = $userCoins->coins;
            $gameMoney = $user->getGameMoney();
            $gameBalanceBefore = $gameMoney ? $gameMoney->realmoney : 0;

            // Deduct coins from user_coins balance
            DB::table('user_coins')
                ->where('account_id', $user->ID)
                ->update([
                    'coins' => DB::raw('coins - ' . $amount),
                    'total_spent' => DB::raw('total_spent + ' . $amount),
                    'updated_at' => now()
                ]);

            // Calculate conversion rate: 1 web coin = 1 game diamonds (realmoney)
            // Game server automatically multiplies by 10, so we use 1:1 ratio to get final 1:10
            $gameCoins = $amount * 1;

            // Use t_tempmoney for game server to process the transfer
            $this->addGameMoney($user, $gameCoins);

            // Get updated balances
            $coinBalanceAfter = $coinBalanceBefore - $amount;
            $gameBalanceAfter = $gameBalanceBefore + $gameCoins;

            // Generate transaction ID
            $transactionId = 'WITHDRAW_' . time() . '_' . rand(1000, 9999);

            // Create transaction record in coin_spend_logs
            $spendId = DB::table('coin_spend_logs')->insertGetId([
                'account_id' => $user->ID,
                'username' => $user->UserName,
                'transaction_id' => $transactionId,
                'coins_spent' => $amount,
                'item_type' => 'withdraw',
                'item_name' => "Rút coin vào game - Nhân vật {$character->rname}",
                'item_data' => json_encode([
                    'character_id' => $characterId,
                    'character_name' => $character->rname,
                    'game_userid' => $user->getGameUserId(),
                    'coin_balance_before' => $coinBalanceBefore,
                    'coin_balance_after' => $coinBalanceAfter,
                    'game_balance_before' => $gameBalanceBefore,
                    'game_balance_after' => $gameBalanceAfter
                ]),
                'description' => "Rút {$amount} kim cương web (sẽ nhận " . number_format($amount * 10) . " kim cương game) vào nhân vật {$character->rname} trong game",
                'ip_address' => $request->ip(),
                'created_at' => now(),
                'updated_at' => now()
            ]);

            DB::commit();

            return back()->with(
                'success',
                "Rút " . number_format($amount) . " kim cương web thành công! " .
                    "Bạn sẽ nhận " . number_format($amount * 10) . " kim cương game vào nhân vật '{$character->rname}' trong game. " .
                    "Số dư kim cương web hiện tại: " . number_format($coinBalanceAfter) . " kim cương."
            );
        } catch (\Exception $e) {
            DB::rollback();
            return back()->withErrors(['error' => 'Có lỗi xảy ra: ' . $e->getMessage()]);
        }
    }

    public function history(Request $request)
    {
        $userSession = Session::get('user_account');
        $user = Account::find($userSession['id']);

        $query = DB::table('coin_spend_logs')
            ->where('account_id', $user->ID)
            ->where('item_type', 'withdraw');

        // Filter by date range
        if ($request->has('date_from') && !empty($request->date_from)) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->has('date_to') && !empty($request->date_to)) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $withdraws = $query->orderBy('created_at', 'desc')->paginate(20);

        return view('user.withdraw.history', compact('withdraws'));
    }

    public function show($id)
    {
        $userSession = Session::get('user_account');
        $user = Account::find($userSession['id']);

        $withdraw = DB::table('coin_spend_logs')
            ->where('account_id', $user->ID)
            ->where('item_type', 'withdraw')
            ->where('id', $id)
            ->first();

        if (!$withdraw) {
            abort(404);
        }

        return view('user.withdraw.show', compact('withdraw'));
    }

    /**
     * Add money to game database using t_tempmoney (similar to monthly card logic)
     */
    private function addGameMoney($user, $amount)
    {
        $gameUserId = $user->getGameUserId();

        // Create temp money record for game server to process
        // Only use fields that exist in t_tempmoney table
        DB::connection('game_mysql')->table('t_tempmoney')->insert([
            'uid' => $gameUserId,
            'addmoney' => $amount
        ]);
    }





    private function getWithdrawStats($userId)
    {
        $totalWithdraws = DB::table('coin_spend_logs')
            ->where('account_id', $userId)
            ->where('item_type', 'withdraw')
            ->count();

        $totalAmount = DB::table('coin_spend_logs')
            ->where('account_id', $userId)
            ->where('item_type', 'withdraw')
            ->sum('coins_spent');

        $todayAmount = DB::table('coin_spend_logs')
            ->where('account_id', $userId)
            ->where('item_type', 'withdraw')
            ->whereDate('created_at', today())
            ->sum('coins_spent');

        $dailyLimit = 500000;

        return [
            'total_withdraws' => $totalWithdraws,
            'completed_withdraws' => $totalWithdraws, // All withdraws are completed immediately
            'total_amount' => $totalAmount,
            'today_amount' => $todayAmount,
            'daily_limit' => $dailyLimit,
            'remaining_today' => max(0, $dailyLimit - $todayAmount)
        ];
    }
}
