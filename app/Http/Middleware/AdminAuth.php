<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class AdminAuth
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // Check if admin is logged in
        if (!Session::has('admin_user')) {
            return redirect('/admin/login')->withErrors(['login' => 'Vui lòng đăng nhập để tiếp tục.']);
        }

        $admin = Session::get('admin_user');

        // Check session timeout (4 hours)
        $loginTime = $admin['login_time'] ?? 0;
        if (now()->timestamp - $loginTime > 14400) { // 4 hours
            Session::forget('admin_user');
            Log::info('Admin session expired', ['admin_id' => $admin['id'] ?? null]);
            return redirect('/admin/login')->withErrors(['login' => 'Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại.']);
        }

        // Verify user is still active and exists
        $currentUser = DB::table('admin_users')
            ->where('id', $admin['id'])
            ->where('is_active', true)
            ->first();
            
        if (!$currentUser) {
            Session::forget('admin_user');
            Log::warning('Admin account disabled or deleted during session', ['admin_id' => $admin['id'] ?? null]);
            return redirect('/admin/login')->withErrors(['login' => 'Tài khoản đã bị vô hiệu hóa. Vui lòng liên hệ quản trị viên.']);
        }

        // Check for suspicious activity (IP change)
        if (isset($admin['login_ip']) && $admin['login_ip'] !== $request->ip()) {
            Log::warning('Admin IP address changed during session', [
                'admin_id' => $admin['id'],
                'original_ip' => $admin['login_ip'],
                'current_ip' => $request->ip(),
                'user_agent' => $request->userAgent()
            ]);
            
            // For high security, you might want to force re-login
            // Session::forget('admin_user');
            // return redirect('/admin/login')->withErrors(['login' => 'Phát hiện thay đổi địa chỉ IP. Vui lòng đăng nhập lại.']);
        }

        // Update session with fresh data if needed
        if ($currentUser->updated_at > ($admin['last_updated'] ?? '1970-01-01')) {
            Session::put('admin_user', array_merge($admin, [
                'email' => $currentUser->email,
                'full_name' => $currentUser->full_name,
                'role' => $currentUser->role,
                'permissions' => json_decode($currentUser->permissions ?? '[]', true),
                'last_updated' => $currentUser->updated_at,
            ]));
        }

        return $next($request);
    }
}
