<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Log;

class AdminPermission
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @param  string  $permission
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next, string $permission)
    {
        $admin = Session::get('admin_user');
        
        if (!$admin) {
            return redirect('/admin/login')->withErrors(['login' => 'Vui lòng đăng nhập để tiếp tục.']);
        }

        // Super admin has all permissions
        if ($admin['role'] === 'super_admin') {
            return $next($request);
        }

        // Check if user has the required permission
        $userPermissions = $admin['permissions'] ?? [];
        
        if (!in_array($permission, $userPermissions)) {
            Log::warning('Admin permission denied', [
                'admin_id' => $admin['id'],
                'username' => $admin['username'],
                'required_permission' => $permission,
                'user_permissions' => $userPermissions,
                'route' => $request->route()->getName(),
                'ip' => $request->ip()
            ]);
            
            return redirect()->back()->withErrors([
                'permission' => 'Bạn không có quyền truy cập chức năng này.'
            ]);
        }

        return $next($request);
    }
}
