<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Services\GameCacheService;
use App\Services\AuthService;

class CacheInvalidation
{
    protected $gameCacheService;
    protected $authService;

    public function __construct(GameCacheService $gameCacheService, AuthService $authService)
    {
        $this->gameCacheService = $gameCacheService;
        $this->authService = $authService;
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $response = $next($request);

        // Clear relevant caches based on the route and action
        $this->handleCacheInvalidation($request, $response);

        return $response;
    }

    private function handleCacheInvalidation(Request $request, $response)
    {
        $route = $request->route();
        if (!$route) return;

        $routeName = $route->getName();
        $method = $request->method();

        // Only clear cache for successful operations
        if (!$this->isSuccessfulResponse($response)) {
            return;
        }

        // Clear giftcode cache when giftcodes are modified
        if ($this->shouldClearGiftcodeCache($routeName, $method)) {
            $this->gameCacheService->clearGiftcodeCache();
        }

        // Clear monthly card cache when packages are modified
        if ($this->shouldClearMonthlyCardCache($routeName, $method)) {
            $this->gameCacheService->clearMonthlyCardCache();
        }

        // Clear user-specific cache when user data is modified
        if ($this->shouldClearUserCache($routeName, $method)) {
            $userId = $this->getUserIdFromRequest($request);
            if ($userId) {
                $this->gameCacheService->clearUserCache($userId);
            }
        }

        // Clear auth cache when user credentials are modified
        if ($this->shouldClearAuthCache($routeName, $method)) {
            $username = $this->getUsernameFromRequest($request);
            if ($username) {
                $this->authService->clearUserCache($username);
            }
        }
    }

    private function isSuccessfulResponse($response)
    {
        $statusCode = $response->getStatusCode();
        return $statusCode >= 200 && $statusCode < 300;
    }

    private function shouldClearGiftcodeCache($routeName, $method)
    {
        $giftcodeRoutes = [
            'admin.giftcode.store',
            'admin.giftcode.update',
            'admin.giftcode.destroy',
            'user.giftcode.redeem'
        ];

        return in_array($routeName, $giftcodeRoutes) && in_array($method, ['POST', 'PUT', 'PATCH', 'DELETE']);
    }

    private function shouldClearMonthlyCardCache($routeName, $method)
    {
        $monthlyCardRoutes = [
            'user.monthly-card.purchase',
            'user.monthly-card.claim-daily'
        ];

        return in_array($routeName, $monthlyCardRoutes) && in_array($method, ['POST', 'PUT', 'PATCH']);
    }

    private function shouldClearUserCache($routeName, $method)
    {
        $userDataRoutes = [
            'user.monthly-card.purchase',
            'user.monthly-card.claim-daily',
            'user.giftcode.redeem',
            'admin.coin-recharge.process'
        ];

        return in_array($routeName, $userDataRoutes) && in_array($method, ['POST', 'PUT', 'PATCH']);
    }

    private function shouldClearAuthCache($routeName, $method)
    {
        $authRoutes = [
            'user.register',
            'admin.users.update',
            'admin.users.destroy'
        ];

        return in_array($routeName, $authRoutes) && in_array($method, ['POST', 'PUT', 'PATCH', 'DELETE']);
    }

    private function getUserIdFromRequest(Request $request)
    {
        // Try to get user ID from session
        $userSession = $request->session()->get('user_account');
        if ($userSession && isset($userSession['id'])) {
            return $userSession['id'];
        }

        // Try to get from route parameters
        if ($request->route('user_id')) {
            return $request->route('user_id');
        }

        // Try to get from request data
        if ($request->has('user_id')) {
            return $request->input('user_id');
        }

        return null;
    }

    private function getUsernameFromRequest(Request $request)
    {
        // Try to get username from session
        $userSession = $request->session()->get('user_account');
        if ($userSession && isset($userSession['username'])) {
            return $userSession['username'];
        }

        // Try to get from request data
        if ($request->has('username')) {
            return $request->input('username');
        }

        return null;
    }
}
