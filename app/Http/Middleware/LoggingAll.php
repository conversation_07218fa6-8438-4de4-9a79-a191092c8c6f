<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class LoggingAll
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // Log request details
        Log::info('API Request', [
            'url' => $request->fullUrl(),
            'method' => $request->method(),
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'input' => $request->except(['password', 'password_confirmation']),
            'timestamp' => now()->toDateTimeString()
        ]);

        $response = $next($request);

        // Log response details
        Log::info('API Response', [
            'url' => $request->fullUrl(),
            'status' => $response->getStatusCode(),
            'timestamp' => now()->toDateTimeString()
        ]);

        return $response;
    }
}
