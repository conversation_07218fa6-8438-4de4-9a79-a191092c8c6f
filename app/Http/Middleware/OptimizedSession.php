<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Cache;

class OptimizedSession
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // Skip session optimization for API routes
        if ($request->is('api/*')) {
            return $next($request);
        }

        // Optimize session handling for authenticated users
        if (Session::has('admin_user') || Session::has('user_account')) {
            // Extend session lifetime for active users
            config(['session.lifetime' => 240]); // 4 hours for active users
        }

        $response = $next($request);

        // Cache frequently accessed session data
        $this->cacheSessionData($request);

        return $response;
    }

    /**
     * <PERSON><PERSON> frequently accessed session data
     */
    private function cacheSessionData(Request $request)
    {
        // Cache admin session data
        if (Session::has('admin_user')) {
            $adminData = Session::get('admin_user');
            $cacheKey = 'session:admin:' . $adminData['id'];
            Cache::put($cacheKey, $adminData, 3600); // 1 hour
        }

        // Cache user session data
        if (Session::has('user_account')) {
            $userData = Session::get('user_account');
            $cacheKey = 'session:user:' . $userData['id'];
            Cache::put($cacheKey, $userData, 3600); // 1 hour
        }
    }
}
