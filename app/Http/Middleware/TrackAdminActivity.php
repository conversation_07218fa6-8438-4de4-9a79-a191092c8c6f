<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use App\Services\AdminActivityService;

class TrackAdminActivity
{
    protected $activityService;

    public function __construct(AdminActivityService $activityService)
    {
        $this->activityService = $activityService;
    }

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next)
    {
        $admin = Session::get('admin_user');
        
        if ($admin) {
            $sessionId = Session::getId();
            $ipAddress = $request->ip();
            $userAgent = $request->header('User-Agent');

            // Đăng ký/cập nhật admin online
            $this->activityService->registerAdminOnline($admin, $sessionId, $ipAddress, $userAgent);

            // Phân tích current action từ route
            $currentActions = $this->analyzeCurrentAction($request);
            
            // Cập nhật hoạt động hiện tại
            $this->activityService->updateAdminActivity($admin['id'], $sessionId, $currentActions);
        }

        return $next($request);
    }

    /**
     * Phân tích action hiện tại từ request
     */
    private function analyzeCurrentAction(Request $request)
    {
        $route = $request->route();
        if (!$route) return [];

        $routeName = $route->getName();
        $actions = [];

        // Phân tích theo route name
        if (str_contains($routeName, 'coin')) {
            $actions[] = [
                'type' => 'coin_management',
                'route' => $routeName,
                'resource_type' => 'user',
                'resource_id' => $request->get('username') ?? $request->get('account_id') ?? 'unknown',
                'operation_type' => $this->getOperationTypeFromRoute($routeName),
                'timestamp' => now()->toISOString()
            ];
        } elseif (str_contains($routeName, 'card-month') || str_contains($routeName, 'monthly-card')) {
            $actions[] = [
                'type' => 'monthly_card_management',
                'route' => $routeName,
                'resource_type' => 'monthly_card',
                'resource_id' => $request->get('username') ?? $request->get('id') ?? 'unknown',
                'operation_type' => $this->getOperationTypeFromRoute($routeName),
                'timestamp' => now()->toISOString()
            ];
        } elseif (str_contains($routeName, 'giftcode')) {
            $actions[] = [
                'type' => 'giftcode_management',
                'route' => $routeName,
                'resource_type' => 'giftcode',
                'resource_id' => $request->get('code') ?? $request->get('id') ?? 'unknown',
                'operation_type' => $this->getOperationTypeFromRoute($routeName),
                'timestamp' => now()->toISOString()
            ];
        } elseif (str_contains($routeName, 'character')) {
            $actions[] = [
                'type' => 'character_management',
                'route' => $routeName,
                'resource_type' => 'character',
                'resource_id' => $request->get('id') ?? 'unknown',
                'operation_type' => $this->getOperationTypeFromRoute($routeName),
                'timestamp' => now()->toISOString()
            ];
        }

        return $actions;
    }

    /**
     * Xác định operation type từ route name
     */
    private function getOperationTypeFromRoute($routeName)
    {
        if (str_contains($routeName, 'create') || str_contains($routeName, 'store')) {
            return 'create';
        } elseif (str_contains($routeName, 'edit') || str_contains($routeName, 'update')) {
            return 'update';
        } elseif (str_contains($routeName, 'delete') || str_contains($routeName, 'destroy')) {
            return 'delete';
        } elseif (str_contains($routeName, 'recharge') || str_contains($routeName, 'add')) {
            return 'add';
        } elseif (str_contains($routeName, 'deduct') || str_contains($routeName, 'subtract')) {
            return 'deduct';
        } elseif (str_contains($routeName, 'cancel')) {
            return 'cancel';
        } elseif (str_contains($routeName, 'extend')) {
            return 'extend';
        } elseif (str_contains($routeName, 'show') || str_contains($routeName, 'view')) {
            return 'view';
        } elseif (str_contains($routeName, 'index') || str_contains($routeName, 'list')) {
            return 'list';
        }

        return 'unknown';
    }
}
