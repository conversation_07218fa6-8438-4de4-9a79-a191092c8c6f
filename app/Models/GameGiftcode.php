<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class GameGiftcode extends Model
{
    use HasFactory;

    protected $connection = 'game_mysql'; // Game server database
    protected $table = 'z_giftcode';

    protected $fillable = [
        'cc',
        'code',
        'mail',
        'count',
        'maxcount',
        'userid',
        'itemlist'
    ];

    protected $casts = [
        'mail' => 'integer',
        'count' => 'integer',
        'maxcount' => 'integer'
    ];

    // Get items array from itemlist string
    public function getItemsArray()
    {
        if (empty($this->itemlist)) {
            return [];
        }
        
        return explode(PHP_EOL, trim($this->itemlist));
    }

    // Set itemlist from array
    public function setItemsArray($items)
    {
        $this->itemlist = is_array($items) ? implode(PHP_EOL, $items) : $items;
    }

    // Check if giftcode can be used
    public function canBeUsed()
    {
        return $this->count < $this->maxcount;
    }

    // Increment usage count
    public function incrementUsage()
    {
        $this->increment('count');
    }

    // Get usage records
    public function records()
    {
        return $this->hasMany(GameGiftcodeRecord::class, 'code', 'code');
    }
}
