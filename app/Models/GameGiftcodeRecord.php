<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class GameGiftcodeRecord extends Model
{
    use HasFactory;

    protected $connection = 'game_mysql'; // Game server database
    protected $table = 'z_giftcoderecord';
    public $timestamps = false; // Only has created_at

    protected $fillable = [
        'userid',
        'code',
        'zoneid',
        'created_at'
    ];

    protected $casts = [
        'zoneid' => 'integer',
        'created_at' => 'datetime'
    ];

    // Get the giftcode record
    public function giftcode()
    {
        return $this->belongsTo(GameGiftcode::class, 'code', 'code');
    }

    // Create a new usage record
    public static function createRecord($userId, $code, $zoneId = 1)
    {
        return self::create([
            'userid' => $userId,
            'code' => $code,
            'zoneid' => $zoneId,
            'created_at' => now()
        ]);
    }
}
