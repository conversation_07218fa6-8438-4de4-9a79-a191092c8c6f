<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class GiftcodeLog extends Model
{
    use HasFactory;

    protected $connection = 'mysql'; // Website database
    protected $table = 't_giftcode_log';

    protected $fillable = [
        'uid',
        'rid',
        'zoneid',
        'giftcode',
        'groupid'
    ];

    protected $casts = [
        'uid' => 'integer',
        'rid' => 'integer',
        'zoneid' => 'integer',
        'groupid' => 'integer'
    ];

    public function giftcode()
    {
        return $this->belongsTo(Giftcode::class, 'groupid');
    }

    public function user()
    {
        return $this->belongsTo(UserAccount::class, 'uid');
    }

    // Static method to create log entry
    public static function createLog($userId, $characterId, $zoneId, $giftcode, $groupId)
    {
        return self::create([
            'uid' => $userId,
            'rid' => $characterId,
            'zoneid' => $zoneId,
            'giftcode' => $giftcode,
            'groupid' => $groupId
        ]);
    }

    // Check if user already used this giftcode
    public static function hasUsed($userId, $groupId, $characterId = null, $type = null)
    {
        $query = self::where('uid', $userId)->where('groupid', $groupId);
        
        // For character-specific giftcodes
        if ($type == Giftcode::TYPE_CHARACTER && $characterId) {
            $query->where('rid', $characterId);
        }
        
        return $query->exists();
    }

    // Get usage statistics
    public static function getUsageStats($groupId)
    {
        return [
            'total_uses' => self::where('groupid', $groupId)->count(),
            'unique_users' => self::where('groupid', $groupId)->distinct('uid')->count(),
            'unique_characters' => self::where('groupid', $groupId)->distinct('rid')->count(),
            'recent_uses' => self::where('groupid', $groupId)
                ->where('created_at', '>=', now()->subDays(7))
                ->count()
        ];
    }
}
