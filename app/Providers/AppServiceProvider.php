<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        // Register date helper for Blade views
        \Blade::directive('safeDate', function ($expression) {
            return "<?php echo \App\Helpers\DateHelper::safeFormat($expression); ?>";
        });

        \Blade::directive('safeDateShort', function ($expression) {
            return "<?php echo \App\Helpers\DateHelper::safeFormat($expression, 'd/m/Y'); ?>";
        });

        \Blade::directive('safeDateHuman', function ($expression) {
            return "<?php echo \App\Helpers\DateHelper::safeDiffForHumans($expression); ?>";
        });
    }
}
