<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;
use Carbon\Carbon;

class AdminActivityService
{
    const LOCK_DURATION_MINUTES = 10; // Lock tự động hết hạn sau 10 phút
    const ACTIVITY_TIMEOUT_MINUTES = 5; // Admin được coi là offline sau 5 phút không hoạt động

    /**
     * Đăng ký admin online
     */
    public function registerAdminOnline($admin, $sessionId, $ipAddress, $userAgent = null)
    {
        DB::table('admin_online_sessions')->updateOrInsert(
            [
                'admin_id' => $admin['id'],
                'session_id' => $sessionId
            ],
            [
                'admin_username' => $admin['username'],
                'ip_address' => $ipAddress,
                'user_agent' => $userAgent,
                'last_activity' => now(),
                'current_actions' => json_encode([]),
                'updated_at' => now()
            ]
        );
    }

    /**
     * Cập nhật hoạt động của admin
     */
    public function updateAdminActivity($adminId, $sessionId, $currentActions = [])
    {
        DB::table('admin_online_sessions')
            ->where('admin_id', $adminId)
            ->where('session_id', $sessionId)
            ->update([
                'last_activity' => now(),
                'current_actions' => json_encode($currentActions),
                'updated_at' => now()
            ]);
    }

    /**
     * Lấy danh sách admin đang online
     */
    public function getOnlineAdmins()
    {
        $timeoutThreshold = now()->subMinutes(self::ACTIVITY_TIMEOUT_MINUTES);
        
        return DB::table('admin_online_sessions')
            ->where('last_activity', '>=', $timeoutThreshold)
            ->orderBy('last_activity', 'desc')
            ->get()
            ->map(function ($session) {
                $session->current_actions = json_decode($session->current_actions, true) ?? [];
                return $session;
            });
    }

    /**
     * Tạo lock cho thao tác
     */
    public function createOperationLock($resourceType, $resourceId, $operationType, $adminId, $adminUsername, $operationDetails = null)
    {
        // Kiểm tra xem đã có lock chưa
        $existingLock = $this->getActiveLock($resourceType, $resourceId, $operationType);
        
        if ($existingLock) {
            throw new \Exception("Resource đang được xử lý bởi {$existingLock->locked_by_admin_username}. Vui lòng thử lại sau.");
        }

        $lockToken = Str::random(32);
        $expiresAt = now()->addMinutes(self::LOCK_DURATION_MINUTES);

        DB::table('admin_operation_locks')->insert([
            'resource_type' => $resourceType,
            'resource_id' => $resourceId,
            'locked_by_admin_id' => $adminId,
            'locked_by_admin_username' => $adminUsername,
            'operation_type' => $operationType,
            'operation_details' => $operationDetails,
            'locked_at' => now(),
            'expires_at' => $expiresAt,
            'lock_token' => $lockToken,
            'created_at' => now(),
            'updated_at' => now()
        ]);

        return $lockToken;
    }

    /**
     * Kiểm tra lock hiện tại
     */
    public function getActiveLock($resourceType, $resourceId, $operationType)
    {
        // Xóa các lock đã hết hạn
        $this->cleanupExpiredLocks();

        return DB::table('admin_operation_locks')
            ->where('resource_type', $resourceType)
            ->where('resource_id', $resourceId)
            ->where('operation_type', $operationType)
            ->where('expires_at', '>', now())
            ->first();
    }

    /**
     * Giải phóng lock
     */
    public function releaseLock($lockToken)
    {
        return DB::table('admin_operation_locks')
            ->where('lock_token', $lockToken)
            ->delete();
    }

    /**
     * Xóa các lock đã hết hạn
     */
    public function cleanupExpiredLocks()
    {
        DB::table('admin_operation_locks')
            ->where('expires_at', '<=', now())
            ->delete();
    }

    /**
     * Phát hiện conflict
     */
    public function detectConflict($resourceType, $resourceId, $operationType, $adminId, $adminUsername, $operationData)
    {
        // Kiểm tra xem có admin khác đang thao tác cùng resource không
        $otherAdminActions = DB::table('admin_online_sessions')
            ->where('admin_id', '!=', $adminId)
            ->where('last_activity', '>=', now()->subMinutes(2))
            ->get();

        foreach ($otherAdminActions as $session) {
            $actions = json_decode($session->current_actions, true) ?? [];
            
            foreach ($actions as $action) {
                if (isset($action['resource_type']) && 
                    isset($action['resource_id']) && 
                    isset($action['operation_type']) &&
                    $action['resource_type'] === $resourceType &&
                    $action['resource_id'] === $resourceId &&
                    $action['operation_type'] === $operationType) {
                    
                    // Phát hiện conflict
                    $this->logConflict(
                        $resourceType, 
                        $resourceId, 
                        $operationType,
                        $adminId, 
                        $adminUsername,
                        $session->admin_id,
                        $session->admin_username,
                        $operationData,
                        $action
                    );
                    
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Ghi log conflict
     */
    private function logConflict($resourceType, $resourceId, $operationType, $admin1Id, $admin1Username, $admin2Id, $admin2Username, $admin1Data, $admin2Data)
    {
        DB::table('admin_operation_conflicts')->insert([
            'resource_type' => $resourceType,
            'resource_id' => $resourceId,
            'operation_type' => $operationType,
            'admin1_id' => $admin1Id,
            'admin1_username' => $admin1Username,
            'admin2_id' => $admin2Id,
            'admin2_username' => $admin2Username,
            'admin1_data' => json_encode($admin1Data),
            'admin2_data' => json_encode($admin2Data),
            'detected_at' => now(),
            'created_at' => now(),
            'updated_at' => now()
        ]);
    }

    /**
     * Thêm thao tác vào queue
     */
    public function queueOperation($operationType, $resourceType, $resourceId, $adminId, $adminUsername, $operationData)
    {
        return DB::table('admin_operation_queue')->insertGetId([
            'operation_type' => $operationType,
            'resource_type' => $resourceType,
            'resource_id' => $resourceId,
            'requested_by_admin_id' => $adminId,
            'requested_by_admin_username' => $adminUsername,
            'operation_data' => json_encode($operationData),
            'requested_at' => now(),
            'created_at' => now(),
            'updated_at' => now()
        ]);
    }

    /**
     * Lấy danh sách conflicts chưa giải quyết
     */
    public function getPendingConflicts()
    {
        return DB::table('admin_operation_conflicts')
            ->where('resolution', 'manual_required')
            ->orderBy('detected_at', 'desc')
            ->get();
    }

    /**
     * Lấy queue operations
     */
    public function getQueuedOperations($status = 'pending')
    {
        return DB::table('admin_operation_queue')
            ->where('status', $status)
            ->orderBy('requested_at', 'asc')
            ->get();
    }
}
