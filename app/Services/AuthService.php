<?php

namespace App\Services;

use App\Models\Account;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Log;

class AuthService
{
    /**
     * Cache duration for user data (in minutes)
     */
    const CACHE_DURATION = 60; // Increased for Redis
    const CACHE_PREFIX = 'auth:';

    /**
     * Get user by username or email with Redis caching
     */
    public function getUserByCredentials($username)
    {
        $cacheKey = self::CACHE_PREFIX . 'user:' . md5($username);

        return Cache::remember($cacheKey, self::CACHE_DURATION, function () use ($username) {
            // Try username first (most common case)
            $user = Account::where('UserName', $username)->first();
            if (!$user) {
                // Try email if username not found
                $user = Account::where('Email', $username)->first();
            }
            return $user;
        });
    }

    /**
     * Validate user credentials
     */
    public function validateCredentials($username, $password)
    {
        $user = $this->getUserByCredentials($username);
        
        if (!$user) {
            return ['success' => false, 'message' => 'Tài khoản không tồn tại.'];
        }

        // Check password
        if ($user->Password !== strtoupper(md5($password))) {
            return ['success' => false, 'message' => 'Mật khẩu không đúng.'];
        }

        // Check if account is active
        if ($user->Status != 1) {
            return ['success' => false, 'message' => 'Tài khoản đã bị khóa. Vui lòng liên hệ admin.'];
        }

        return ['success' => true, 'user' => $user];
    }

    /**
     * Create session data for user
     */
    public function createUserSession($user)
    {
        return [
            'id' => $user->ID,
            'username' => $user->UserName,
            'email' => $user->Email,
            'status' => 'active',
            'login_time' => now()->timestamp
        ];
    }

    /**
     * Clear user cache
     */
    public function clearUserCache($username)
    {
        $cacheKey = self::CACHE_PREFIX . 'user:' . md5($username);
        Cache::forget($cacheKey);

        // Also clear related caches
        Cache::forget(self::CACHE_PREFIX . 'username_exists:' . md5($username));
        Cache::forget(self::CACHE_PREFIX . 'email_exists:' . md5($username));
    }

    /**
     * Check if username exists (for registration)
     */
    public function usernameExists($username)
    {
        $cacheKey = self::CACHE_PREFIX . 'username_exists:' . md5($username);

        return Cache::remember($cacheKey, self::CACHE_DURATION, fn() =>
            Account::where('UserName', $username)->exists()
        );
    }

    /**
     * Check if email exists (for registration)
     */
    public function emailExists($email)
    {
        $cacheKey = self::CACHE_PREFIX . 'email_exists:' . md5($email);

        return Cache::remember($cacheKey, self::CACHE_DURATION, fn() =>
            Account::where('Email', $email)->exists()
        );
    }

    /**
     * Log authentication events
     */
    public function logAuthEvent($type, $data)
    {
        // Only log important events to reduce I/O
        if (in_array($type, ['login_failed', 'account_blocked', 'login_success'])) {
            Log::info("Auth: {$type}", $data);
        }
    }
}
