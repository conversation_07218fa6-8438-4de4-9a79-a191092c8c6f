<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\Account;

class CacheService
{
    // Cache TTL constants (in seconds)
    const AUTH_CACHE_TTL = 3600; // 1 hour
    const DASHBOARD_CACHE_TTL = 300; // 5 minutes
    const GAME_STATS_CACHE_TTL = 600; // 10 minutes
    const USER_CACHE_TTL = 1800; // 30 minutes

    // Cache key prefixes
    const AUTH_PREFIX = 'auth:';
    const DASHBOARD_PREFIX = 'dashboard:';
    const GAME_PREFIX = 'game:';
    const USER_PREFIX = 'user:';

    /**
     * Get cached admin user by username
     */
    public static function getAdminUser($username)
    {
        $cacheKey = self::AUTH_PREFIX . 'admin:' . $username;
        
        return Cache::remember($cacheKey, self::AUTH_CACHE_TTL, function () use ($username) {
            Log::info('Cache miss for admin user', ['username' => $username]);
            return DB::table('admin_users')
                ->where('username', $username)
                ->where('is_active', 1)
                ->first();
        });
    }

    /**
     * Clear admin user cache
     */
    public static function clearAdminUser($username)
    {
        $cacheKey = self::AUTH_PREFIX . 'admin:' . $username;
        Cache::forget($cacheKey);
    }

    /**
     * Get cached dashboard stats
     */
    public static function getDashboardStats()
    {
        $cacheKey = self::DASHBOARD_PREFIX . 'stats';
        
        return Cache::remember($cacheKey, self::DASHBOARD_CACHE_TTL, function () {
            Log::info('Cache miss for dashboard stats');
            
            $stats = [
                'accounts' => 0,
                'characters' => 0,
                'giftcodes' => 0,
                'total_coins' => 0,
            ];

            try {
                // Get accounts count
                $stats['accounts'] = DB::table('t_account')->count();
            } catch (\Exception $e) {
                Log::error('Error getting accounts count', ['error' => $e->getMessage()]);
            }

            try {
                // Get characters count from game database
                $stats['characters'] = DB::connection('game_mysql')
                    ->table('t_roles')
                    ->where('isdel', 0)
                    ->count();
            } catch (\Exception $e) {
                Log::error('Error getting characters count', ['error' => $e->getMessage()]);
            }

            try {
                // Get giftcodes count
                $stats['giftcodes'] = DB::table('giftcodes')->count();
            } catch (\Exception $e) {
                Log::error('Error getting giftcodes count', ['error' => $e->getMessage()]);
            }

            try {
                // Get total coins from game database (realmoney = YuanBao/diamonds)
                $totalCoins = DB::connection('game_mysql')
                    ->table('t_money')
                    ->sum('realmoney');
                $stats['total_coins'] = $totalCoins ?? 0;
            } catch (\Exception $e) {
                Log::error('Error getting total coins', ['error' => $e->getMessage()]);
                $stats['total_coins'] = 0;
            }

            return $stats;
        });
    }

    /**
     * Clear dashboard stats cache
     */
    public static function clearDashboardStats()
    {
        $cacheKey = self::DASHBOARD_PREFIX . 'stats';
        Cache::forget($cacheKey);
    }

    /**
     * Get cached user account data
     */
    public static function getUserAccount($userId)
    {
        $cacheKey = self::USER_PREFIX . 'account:' . $userId;
        
        return Cache::remember($cacheKey, self::USER_CACHE_TTL, function () use ($userId) {
            Log::info('Cache miss for user account', ['user_id' => $userId]);
            return Account::find($userId);
        });
    }

    /**
     * Clear user account cache
     */
    public static function clearUserAccount($userId)
    {
        $cacheKey = self::USER_PREFIX . 'account:' . $userId;
        Cache::forget($cacheKey);
    }

    /**
     * Get cached game characters for user
     */
    public static function getUserCharacters($userId)
    {
        $cacheKey = self::GAME_PREFIX . 'characters:' . $userId;
        
        return Cache::remember($cacheKey, self::GAME_STATS_CACHE_TTL, function () use ($userId) {
            Log::info('Cache miss for user characters', ['user_id' => $userId]);
            
            try {
                return DB::connection('game_mysql')
                    ->table('t_roles')
                    ->where('userid', 'ZT' . str_pad($userId, 4, '0', STR_PAD_LEFT))
                    ->where('isdel', 0)
                    ->get();
            } catch (\Exception $e) {
                Log::error('Error getting user characters', ['user_id' => $userId, 'error' => $e->getMessage()]);
                return collect();
            }
        });
    }

    /**
     * Clear user characters cache
     */
    public static function clearUserCharacters($userId)
    {
        $cacheKey = self::GAME_PREFIX . 'characters:' . $userId;
        Cache::forget($cacheKey);
    }

    /**
     * Warm up cache with frequently accessed data
     */
    public static function warmUpCache()
    {
        Log::info('Starting cache warm up');
        
        // Warm up dashboard stats
        self::getDashboardStats();
        
        // Warm up active admin users
        $activeAdmins = DB::table('admin_users')
            ->where('is_active', 1)
            ->pluck('username');
            
        foreach ($activeAdmins as $username) {
            self::getAdminUser($username);
        }
        
        Log::info('Cache warm up completed');
    }

    /**
     * Clear all application caches
     */
    public static function clearAllCache()
    {
        $patterns = [
            self::AUTH_PREFIX . '*',
            self::DASHBOARD_PREFIX . '*',
            self::GAME_PREFIX . '*',
            self::USER_PREFIX . '*',
        ];

        foreach ($patterns as $pattern) {
            $keys = Cache::getRedis()->keys($pattern);
            if (!empty($keys)) {
                Cache::getRedis()->del($keys);
            }
        }

        Log::info('All application caches cleared');
    }
}
