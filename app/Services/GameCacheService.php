<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;

class GameCacheService
{
    const CACHE_PREFIX = 'game:';
    const CACHE_DURATION = 30; // 30 minutes for game data
    const CACHE_DURATION_LONG = 120; // 2 hours for static data

    /**
     * Get user characters with caching
     */
    public function getUserCharacters($userId)
    {
        $cacheKey = self::CACHE_PREFIX . 'characters:' . $userId;
        
        return Cache::remember($cacheKey, self::CACHE_DURATION, function () use ($userId) {
            $gameUserId = 'ZT' . str_pad($userId, 4, '0', STR_PAD_LEFT);
            
            return DB::connection('game')
                ->table('t_roles')
                ->where('userid', $gameUserId)
                ->where('isdel', 0)
                ->select('rid', 'rname', 'rlevel', 'rclass', 'rmoney')
                ->orderBy('rlevel', 'desc')
                ->get();
        });
    }

    /**
     * Get user coins with caching
     */
    public function getUserCoins($accountId)
    {
        $cacheKey = self::CACHE_PREFIX . 'coins:' . $accountId;
        
        return Cache::remember($cacheKey, 5, function () use ($accountId) { // 5 minutes for coins
            return DB::table('user_coins')
                ->where('account_id', $accountId)
                ->first();
        });
    }

    /**
     * Get active giftcodes with caching
     */
    public function getActiveGiftcodes()
    {
        $cacheKey = self::CACHE_PREFIX . 'active_giftcodes';
        
        return Cache::remember($cacheKey, self::CACHE_DURATION, function () {
            return DB::table('giftcodes')
                ->where('is_active', 1)
                ->where(function($query) {
                    $query->whereNull('expires_at')
                          ->orWhere('expires_at', '>', now());
                })
                ->where(function($query) {
                    $query->where('max_uses', 0)
                          ->orWhereRaw('used_count < max_uses');
                })
                ->orderBy('created_at', 'desc')
                ->get();
        });
    }

    /**
     * Get monthly card packages with caching
     */
    public function getMonthlyCardPackages()
    {
        $cacheKey = self::CACHE_PREFIX . 'monthly_card_packages';
        
        return Cache::remember($cacheKey, self::CACHE_DURATION_LONG, function () {
            return [
                'basic' => [
                    'name' => 'Thẻ Tháng Cơ Bản',
                    'cost_coins' => 50000,
                    'duration_days' => 30,
                    'daily_reward_coins' => 2000,
                    'description' => 'Nhận 2,000 coin mỗi ngày trong 30 ngày',
                    'bonus_items' => [],
                    'daily_items' => []
                ],
                'premium' => [
                    'name' => 'Thẻ Tháng Cao Cấp',
                    'cost_coins' => 100000,
                    'duration_days' => 30,
                    'daily_reward_coins' => 4000,
                    'description' => 'Nhận 4,000 coin mỗi ngày trong 30 ngày + items bonus',
                    'bonus_items' => [
                        '14,10,1,0,0,0,0' // Jewel of Bless x10
                    ],
                    'daily_items' => [
                        '14,1,1,0,0,0,0' // Jewel of Bless x1 daily
                    ]
                ],
                'vip' => [
                    'name' => 'Thẻ Tháng VIP',
                    'cost_coins' => 200000,
                    'duration_days' => 30,
                    'daily_reward_coins' => 8000,
                    'description' => 'Nhận 8,000 coin mỗi ngày trong 30 ngày + items VIP',
                    'bonus_items' => [
                        '14,50,1,0,0,0,0', // Jewel of Bless x50
                        '15,10,1,0,0,0,0'  // Jewel of Soul x10
                    ],
                    'daily_items' => [
                        '14,2,1,0,0,0,0' // Jewel of Bless x2 daily
                    ]
                ]
            ];
        });
    }

    /**
     * Get user's active monthly cards
     */
    public function getUserActiveMonthlyCards($userId)
    {
        $cacheKey = self::CACHE_PREFIX . 'monthly_cards:' . $userId;
        
        return Cache::remember($cacheKey, 10, function () use ($userId) { // 10 minutes
            return DB::table('monthly_card_purchases')
                ->where('user_id', $userId)
                ->where('status', 'active')
                ->where('expires_at', '>', now())
                ->orderBy('expires_at', 'desc')
                ->get();
        });
    }

    /**
     * Clear user-specific caches
     */
    public function clearUserCache($userId)
    {
        $patterns = [
            self::CACHE_PREFIX . 'characters:' . $userId,
            self::CACHE_PREFIX . 'coins:' . $userId,
            self::CACHE_PREFIX . 'monthly_cards:' . $userId,
        ];

        foreach ($patterns as $pattern) {
            Cache::forget($pattern);
        }
    }

    /**
     * Clear giftcode caches
     */
    public function clearGiftcodeCache()
    {
        Cache::forget(self::CACHE_PREFIX . 'active_giftcodes');
    }

    /**
     * Clear monthly card caches
     */
    public function clearMonthlyCardCache()
    {
        Cache::forget(self::CACHE_PREFIX . 'monthly_card_packages');
    }

    /**
     * Warm up cache for frequently accessed data
     */
    public function warmUpCache()
    {
        // Pre-load active giftcodes
        $this->getActiveGiftcodes();
        
        // Pre-load monthly card packages
        $this->getMonthlyCardPackages();
    }

    /**
     * Get cache statistics
     */
    public function getCacheStats()
    {
        try {
            $redis = Redis::connection('cache');
            $info = $redis->info('memory');
            
            return [
                'memory_used' => $info['used_memory_human'] ?? 'N/A',
                'memory_peak' => $info['used_memory_peak_human'] ?? 'N/A',
                'connected_clients' => $redis->info('clients')['connected_clients'] ?? 0,
                'total_keys' => $redis->dbsize(),
                'cache_hits' => $redis->info('stats')['keyspace_hits'] ?? 0,
                'cache_misses' => $redis->info('stats')['keyspace_misses'] ?? 0,
            ];
        } catch (\Exception $e) {
            return ['error' => 'Redis not available'];
        }
    }

    /**
     * Flush all game cache
     */
    public function flushGameCache()
    {
        try {
            $redis = Redis::connection('cache');
            $keys = $redis->keys(self::CACHE_PREFIX . '*');
            
            if (!empty($keys)) {
                $redis->del($keys);
            }
            
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
}
