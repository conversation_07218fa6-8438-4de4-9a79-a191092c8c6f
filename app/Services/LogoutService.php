<?php

namespace App\Services;

use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class LogoutService
{
    /**
     * Safely logout admin user
     */
    public static function logoutAdmin($request = null)
    {
        try {
            $admin = Session::get('admin_user');
            
            if ($admin) {
                // Clear admin cache
                CacheService::clearAdminUser($admin['username']);
                
                // Clear session cache
                $cacheKey = 'session:admin:' . $admin['id'];
                Cache::forget($cacheKey);
                
                Log::info('Admin logout', [
                    'admin_id' => $admin['id'],
                    'username' => $admin['username'],
                    'ip' => $request ? $request->ip() : 'unknown'
                ]);
            }
            
            // Clear admin session
            Session::forget('admin_user');
            
            // Clear all session data
            Session::flush();
            
            // Regenerate session ID for security
            Session::regenerate();
            
            return true;
            
        } catch (\Exception $e) {
            Log::error('Admin logout error', ['error' => $e->getMessage()]);
            
            // Force clear session even if error
            Session::flush();
            Session::regenerate();
            
            return false;
        }
    }
    
    /**
     * Safely logout user
     */
    public static function logoutUser($request = null)
    {
        try {
            $user = Session::get('user_account');
            
            if ($user) {
                // Clear user cache
                CacheService::clearUserAccount($user['id']);
                CacheService::clearUserCharacters($user['id']);
                
                // Clear session cache
                $cacheKey = 'session:user:' . $user['id'];
                Cache::forget($cacheKey);
                
                Log::info('User logout', [
                    'user_id' => $user['id'],
                    'username' => $user['username'],
                    'ip' => $request ? $request->ip() : 'unknown'
                ]);
            }
            
            // Clear user session
            Session::forget('user_account');
            
            // Clear all session data
            Session::flush();
            
            // Regenerate session ID for security
            Session::regenerate();
            
            return true;
            
        } catch (\Exception $e) {
            Log::error('User logout error', ['error' => $e->getMessage()]);
            
            // Force clear session even if error
            Session::flush();
            Session::regenerate();
            
            return false;
        }
    }
    
    /**
     * Emergency logout - clear everything
     */
    public static function emergencyLogout($request = null)
    {
        try {
            // Clear all sessions
            Session::flush();
            Session::regenerate();
            
            Log::warning('Emergency logout performed', [
                'ip' => $request ? $request->ip() : 'unknown'
            ]);
            
            return true;
            
        } catch (\Exception $e) {
            Log::error('Emergency logout failed', ['error' => $e->getMessage()]);
            return false;
        }
    }
}
