<?php

require_once 'vendor/autoload.php';

use Illuminate\Database\Capsule\Manager as DB;

// Setup database connections
$capsule = new DB;

// Website database
$capsule->addConnection([
    'driver' => 'mysql',
    'host' => '**************',
    'port' => '3321',
    'database' => 'zythe_platform_sdk1',
    'username' => 'root',
    'password' => '123456',
    'charset' => 'utf8',
    'collation' => 'utf8_unicode_ci',
    'prefix' => '',
], 'default');

// Game database
$capsule->addConnection([
    'driver' => 'mysql',
    'host' => '**************',
    'port' => '3321',
    'database' => 'mu_game_1',
    'username' => 'root',
    'password' => '123456',
    'charset' => 'utf8',
    'collation' => 'utf8_unicode_ci',
    'prefix' => '',
], 'game_mysql');

$capsule->setAsGlobal();
$capsule->bootEloquent();

echo "=== KIỂM TRA NHÂN VẬT TX512 ===\n";

try {
    // Tìm nhân vật TX512
    $character = DB::connection('game_mysql')
        ->table('t_roles')
        ->where('rname', 'TX512')
        ->first();
    
    if ($character) {
        echo "Tìm thấy nhân vật TX512:\n";
        echo "- ID: {$character->rid}\n";
        echo "- Tên: {$character->rname}\n";
        echo "- User ID: {$character->userid}\n";
        echo "- Level: {$character->level}\n";
        echo "- Occupation: {$character->occupation}\n";
        echo "- Money: {$character->money}\n";
        echo "- Experience: {$character->experience}\n";
        echo "- Last time: {$character->lasttime}\n";
        echo "- Is deleted: {$character->isdel}\n\n";
    } else {
        echo "Không tìm thấy nhân vật TX512. Tìm kiếm tương tự...\n";
        
        $similarCharacters = DB::connection('game_mysql')
            ->table('t_roles')
            ->where('rname', 'like', '%TX%')
            ->orWhere('rname', 'like', '%512%')
            ->limit(10)
            ->get();
        
        if ($similarCharacters->count() > 0) {
            echo "Các nhân vật tương tự:\n";
            foreach ($similarCharacters as $char) {
                echo "- {$char->rname} (ID: {$char->rid}, User: {$char->userid})\n";
            }
        } else {
            echo "Không tìm thấy nhân vật nào tương tự.\n";
        }
        echo "\n";
    }
    
    // Kiểm tra cấu trúc bảng t_roles
    echo "=== CẤU TRÚC BẢNG T_ROLES ===\n";
    $columns = DB::connection('game_mysql')->select("DESCRIBE t_roles");
    foreach ($columns as $column) {
        echo "- {$column->Field}: {$column->Type} ({$column->Null}, {$column->Key})\n";
    }
    echo "\n";
    
    // Kiểm tra bảng giftcode trong game database
    echo "=== KIỂM TRA BẢNG GIFTCODE TRONG GAME DATABASE ===\n";
    
    // Kiểm tra bảng z_giftcode
    $giftcodeTables = DB::connection('game_mysql')->select("SHOW TABLES LIKE '%gift%'");
    if (count($giftcodeTables) > 0) {
        echo "Các bảng giftcode trong game database:\n";
        foreach ($giftcodeTables as $table) {
            $tableName = array_values((array)$table)[0];
            echo "- {$tableName}\n";
            
            // Lấy cấu trúc bảng
            $columns = DB::connection('game_mysql')->select("DESCRIBE {$tableName}");
            foreach ($columns as $column) {
                echo "  + {$column->Field}: {$column->Type}\n";
            }
            echo "\n";
        }
    } else {
        echo "Không tìm thấy bảng giftcode trong game database.\n";
    }
    
    // Kiểm tra bảng vật phẩm
    echo "=== KIỂM TRA BẢNG VẬT PHẨM ===\n";
    $itemTables = DB::connection('game_mysql')->select("SHOW TABLES LIKE '%item%'");
    $goodsTables = DB::connection('game_mysql')->select("SHOW TABLES LIKE '%goods%'");
    
    $allItemTables = array_merge($itemTables, $goodsTables);
    
    if (count($allItemTables) > 0) {
        echo "Các bảng vật phẩm trong game database:\n";
        foreach ($allItemTables as $table) {
            $tableName = array_values((array)$table)[0];
            echo "- {$tableName}\n";
        }
    } else {
        echo "Không tìm thấy bảng vật phẩm trong game database.\n";
    }
    
    // Kiểm tra bảng money
    echo "\n=== KIỂM TRA BẢNG MONEY ===\n";
    $moneyTables = DB::connection('game_mysql')->select("SHOW TABLES LIKE '%money%'");
    if (count($moneyTables) > 0) {
        foreach ($moneyTables as $table) {
            $tableName = array_values((array)$table)[0];
            echo "Bảng: {$tableName}\n";
            $columns = DB::connection('game_mysql')->select("DESCRIBE {$tableName}");
            foreach ($columns as $column) {
                echo "  + {$column->Field}: {$column->Type}\n";
            }
            echo "\n";
        }
    }
    
} catch (Exception $e) {
    echo "Lỗi: " . $e->getMessage() . "\n";
}

echo "=== HOÀN THÀNH KIỂM TRA ===\n";
