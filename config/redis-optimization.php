<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Redis Performance Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains Redis optimization settings for the MU Game Admin Panel.
    | These settings are designed to improve performance for authentication,
    | caching, and session management.
    |
    */

    'auth_cache' => [
        'ttl' => env('AUTH_CACHE_TTL', 3600), // 1 hour
        'prefix' => 'auth:',
        'enabled' => env('AUTH_CACHE_ENABLED', true),
    ],

    'game_cache' => [
        'ttl' => env('GAME_CACHE_TTL', 1800), // 30 minutes
        'ttl_long' => env('GAME_CACHE_TTL_LONG', 7200), // 2 hours
        'prefix' => 'game:',
        'enabled' => env('GAME_CACHE_ENABLED', true),
    ],

    'session_optimization' => [
        'gc_probability' => env('SESSION_GC_PROBABILITY', 1),
        'gc_divisor' => env('SESSION_GC_DIVISOR', 100),
        'gc_maxlifetime' => env('SESSION_GC_MAXLIFETIME', 7200),
    ],

    'performance' => [
        // Enable Redis pipelining for bulk operations
        'pipeline_enabled' => env('REDIS_PIPELINE_ENABLED', true),
        
        // Connection pool settings
        'connection_pool_size' => env('REDIS_CONNECTION_POOL_SIZE', 10),
        
        // Timeout settings (in seconds)
        'connect_timeout' => env('REDIS_CONNECT_TIMEOUT', 2),
        'read_timeout' => env('REDIS_READ_TIMEOUT', 2),
        
        // Retry settings
        'retry_attempts' => env('REDIS_RETRY_ATTEMPTS', 3),
        'retry_delay' => env('REDIS_RETRY_DELAY', 100), // milliseconds
    ],

    'monitoring' => [
        // Enable cache hit/miss logging
        'log_cache_stats' => env('REDIS_LOG_CACHE_STATS', false),
        
        // Log slow queries (in milliseconds)
        'slow_query_threshold' => env('REDIS_SLOW_QUERY_THRESHOLD', 100),
        
        // Memory usage alerts
        'memory_alert_threshold' => env('REDIS_MEMORY_ALERT_THRESHOLD', 80), // percentage
    ],

    'cleanup' => [
        // Auto cleanup expired keys
        'auto_cleanup_enabled' => env('REDIS_AUTO_CLEANUP_ENABLED', true),
        
        // Cleanup interval (in minutes)
        'cleanup_interval' => env('REDIS_CLEANUP_INTERVAL', 60),
        
        // Batch size for cleanup operations
        'cleanup_batch_size' => env('REDIS_CLEANUP_BATCH_SIZE', 1000),
    ],

    /*
    |--------------------------------------------------------------------------
    | Cache Tags Configuration
    |--------------------------------------------------------------------------
    |
    | Define cache tags for different types of data to enable
    | selective cache invalidation.
    |
    */
    'tags' => [
        'user_data' => 'user_data',
        'game_data' => 'game_data',
        'giftcodes' => 'giftcodes',
        'monthly_cards' => 'monthly_cards',
        'characters' => 'characters',
        'coins' => 'coins',
    ],

    /*
    |--------------------------------------------------------------------------
    | Recommended Redis Configuration
    |--------------------------------------------------------------------------
    |
    | Add these settings to your redis.conf file for optimal performance:
    |
    | # Memory optimization
    | maxmemory 256mb
    | maxmemory-policy allkeys-lru
    | 
    | # Persistence (for session data)
    | save 900 1
    | save 300 10
    | save 60 10000
    | 
    | # Network optimization
    | tcp-keepalive 300
    | timeout 0
    | 
    | # Performance tuning
    | hash-max-ziplist-entries 512
    | hash-max-ziplist-value 64
    | list-max-ziplist-size -2
    | set-max-intset-entries 512
    | zset-max-ziplist-entries 128
    | zset-max-ziplist-value 64
    | 
    | # Logging
    | loglevel notice
    | logfile /var/log/redis/redis-server.log
    |
    */
];
