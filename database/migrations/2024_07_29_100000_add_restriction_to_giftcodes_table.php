<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('giftcodes', function (Blueprint $table) {
            $table->string('restriction_type')->default('none')->after('expires_at');
            $table->text('allowed_usernames')->nullable()->after('restriction_type');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('giftcodes', function (Blueprint $table) {
            $table->dropColumn('restriction_type');
            $table->dropColumn('allowed_usernames');
        });
    }
};
