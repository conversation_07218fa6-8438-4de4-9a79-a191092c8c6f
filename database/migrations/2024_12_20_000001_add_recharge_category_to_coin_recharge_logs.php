<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('coin_recharge_logs', function (Blueprint $table) {
            $table->enum('recharge_category', ['customer', 'founder_team'])->default('customer')->after('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('coin_recharge_logs', function (Blueprint $table) {
            $table->dropColumn('recharge_category');
        });
    }
};
