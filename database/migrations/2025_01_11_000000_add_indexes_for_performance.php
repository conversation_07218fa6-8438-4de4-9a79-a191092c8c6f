<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Add indexes for better performance
        try {
            // Giftcodes table indexes
            if (Schema::hasTable('giftcodes')) {
                Schema::table('giftcodes', function (Blueprint $table) {
                    if (!$this->indexExists('giftcodes', 'idx_giftcodes_code')) {
                        $table->index('code', 'idx_giftcodes_code');
                    }
                    if (!$this->indexExists('giftcodes', 'idx_giftcodes_status')) {
                        $table->index('status', 'idx_giftcodes_status');
                    }
                    if (!$this->indexExists('giftcodes', 'idx_giftcodes_expires_at')) {
                        $table->index('expires_at', 'idx_giftcodes_expires_at');
                    }
                });
            }

            // Giftcode logs table indexes
            if (Schema::hasTable('giftcode_logs')) {
                Schema::table('giftcode_logs', function (Blueprint $table) {
                    if (!$this->indexExists('giftcode_logs', 'idx_giftcode_logs_user_id')) {
                        $table->index('user_id', 'idx_giftcode_logs_user_id');
                    }
                    if (!$this->indexExists('giftcode_logs', 'idx_giftcode_logs_giftcode_id')) {
                        $table->index('giftcode_id', 'idx_giftcode_logs_giftcode_id');
                    }
                    if (!$this->indexExists('giftcode_logs', 'idx_giftcode_logs_created_at')) {
                        $table->index('created_at', 'idx_giftcode_logs_created_at');
                    }
                });
            }

            // Admin users table indexes
            if (Schema::hasTable('admin_users')) {
                Schema::table('admin_users', function (Blueprint $table) {
                    if (!$this->indexExists('admin_users', 'idx_admin_users_username')) {
                        $table->index('username', 'idx_admin_users_username');
                    }
                    if (!$this->indexExists('admin_users', 'idx_admin_users_email')) {
                        $table->index('email', 'idx_admin_users_email');
                    }
                    if (!$this->indexExists('admin_users', 'idx_admin_users_is_active')) {
                        $table->index('is_active', 'idx_admin_users_is_active');
                    }
                });
            }

        } catch (\Exception $e) {
            // Log error but don't fail migration
            \Log::warning('Index creation failed: ' . $e->getMessage());
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        try {
            // Drop indexes
            if (Schema::hasTable('giftcodes')) {
                Schema::table('giftcodes', function (Blueprint $table) {
                    $table->dropIndex('idx_giftcodes_code');
                    $table->dropIndex('idx_giftcodes_status');
                    $table->dropIndex('idx_giftcodes_expires_at');
                });
            }

            if (Schema::hasTable('giftcode_logs')) {
                Schema::table('giftcode_logs', function (Blueprint $table) {
                    $table->dropIndex('idx_giftcode_logs_user_id');
                    $table->dropIndex('idx_giftcode_logs_giftcode_id');
                    $table->dropIndex('idx_giftcode_logs_created_at');
                });
            }

            if (Schema::hasTable('admin_users')) {
                Schema::table('admin_users', function (Blueprint $table) {
                    $table->dropIndex('idx_admin_users_username');
                    $table->dropIndex('idx_admin_users_email');
                    $table->dropIndex('idx_admin_users_is_active');
                });
            }

        } catch (\Exception $e) {
            \Log::warning('Index dropping failed: ' . $e->getMessage());
        }
    }

    /**
     * Check if index exists
     */
    private function indexExists($table, $index)
    {
        $indexes = DB::select("SHOW INDEX FROM {$table}");
        foreach ($indexes as $idx) {
            if ($idx->Key_name === $index) {
                return true;
            }
        }
        return false;
    }
};
