<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateGiftcodesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Drop existing giftcodes table if exists and recreate with proper structure
        Schema::dropIfExists('giftcodes');

        // Create t_giftcode table (main giftcode table on website database)
        Schema::create('t_giftcode', function (Blueprint $table) {
            $table->id();
            $table->tinyInteger('type')->default(1); // 1=public, 2=private, 0=character
            $table->text('accounts')->nullable(); // Comma-separated usernames for private codes
            $table->boolean('multiple')->default(false); // 0=single code, 1=multiple codes
            $table->text('code'); // Array of giftcode strings (JSON stored as text)
            $table->text('items'); // Array of items: goodsid,count,binding,forge_level,appendproplev,lucky,excellenceinfo (JSON stored as text)
            $table->string('content'); // Description/message for the giftcode
            $table->integer('limit')->default(0); // Usage limit (0 = unlimited)
            $table->integer('period')->default(0); // Expiry period in days (0 = no expiry)
            $table->integer('zoneid')->default(0); // Server/Zone ID (0 = all servers)
            $table->timestamps();

            // Indexes for performance
            $table->index(['type', 'zoneid']);
            $table->index('created_at');
        });

        // Keep giftcodes table for backward compatibility but update structure
        Schema::create('giftcodes', function (Blueprint $table) {
            $table->id();
            $table->string('code')->unique();
            $table->string('name');
            $table->text('description')->nullable();
            $table->text('rewards'); // JSON array of rewards stored as text
            $table->integer('max_uses')->default(1);
            $table->integer('used_count')->default(0);
            $table->boolean('is_active')->default(true);
            $table->timestamp('expires_at')->nullable();
            $table->unsignedBigInteger('created_by');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('t_giftcode');
        Schema::dropIfExists('giftcodes');
    }
}
