<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddGiftcodeFields extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('giftcodes', function (Blueprint $table) {
            // Add new columns for enhanced giftcode system
            $table->tinyInteger('type')->default(1)->after('id'); // 1=public, 2=private, 0=character
            $table->boolean('multiple')->default(false)->after('type'); // Can generate multiple codes
            $table->text('items')->nullable()->after('description'); // Game items in format: goodsid,count,binding,forge_level,appendproplev,lucky,excellenceinfo
            $table->string('content')->nullable()->after('items'); // Description/message for the giftcode
            $table->integer('limit')->default(1)->after('content'); // Usage limit (0 = unlimited)
            $table->text('accounts')->nullable()->after('limit'); // Comma-separated usernames for private codes
            $table->integer('period')->default(0)->after('accounts'); // Expiry period in days (0 = no expiry)
            $table->integer('zoneid')->default(1)->after('period'); // Server/Zone ID (0 = all servers)
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('giftcodes', function (Blueprint $table) {
            $table->dropColumn([
                'type',
                'multiple', 
                'items',
                'content',
                'limit',
                'accounts',
                'period',
                'zoneid'
            ]);
        });
    }
}
