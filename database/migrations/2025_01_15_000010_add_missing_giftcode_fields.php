<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class AddMissingGiftcodeFields extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Check which columns exist and add only missing ones
        $columns = DB::select("SHOW COLUMNS FROM giftcodes");
        $existingColumns = array_column($columns, 'Field');

        Schema::table('giftcodes', function (Blueprint $table) use ($existingColumns) {
            if (!in_array('multiple', $existingColumns)) {
                $table->boolean('multiple')->default(false);
            }
            if (!in_array('items', $existingColumns)) {
                $table->text('items')->nullable();
            }
            if (!in_array('content', $existingColumns)) {
                $table->string('content')->nullable();
            }
            if (!in_array('limit', $existingColumns)) {
                $table->integer('limit')->default(1);
            }
            if (!in_array('accounts', $existingColumns)) {
                $table->text('accounts')->nullable();
            }
            if (!in_array('period', $existingColumns)) {
                $table->integer('period')->default(0);
            }
            if (!in_array('zoneid', $existingColumns)) {
                $table->integer('zoneid')->default(1);
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('giftcodes', function (Blueprint $table) {
            $columns = ['multiple', 'items', 'content', 'limit', 'accounts', 'period', 'zoneid'];
            foreach ($columns as $column) {
                if (Schema::hasColumn('giftcodes', $column)) {
                    $table->dropColumn($column);
                }
            }
        });
    }
}
