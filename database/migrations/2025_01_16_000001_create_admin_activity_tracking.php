<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Bảng theo dõi admin đang online và hoạt động
        if (!Schema::hasTable('admin_online_sessions')) {
            Schema::create('admin_online_sessions', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('admin_id');
            $table->string('admin_username', 50);
            $table->string('session_id', 100);
            $table->string('ip_address', 45);
            $table->string('user_agent', 500)->nullable();
            $table->timestamp('last_activity');
            $table->text('current_actions')->nullable(); // Các thao tác đang thực hiện (JSON string)
            $table->timestamps();

            $table->index(['admin_id', 'session_id']);
            $table->index('last_activity');
        });
        }

        // Bảng lock tạm thời cho các thao tác
        if (!Schema::hasTable('admin_operation_locks')) {
            Schema::create('admin_operation_locks', function (Blueprint $table) {
            $table->id();
            $table->string('resource_type', 50); // user, giftcode, monthly_card, etc.
            $table->string('resource_id', 100); // ID của resource
            $table->unsignedBigInteger('locked_by_admin_id');
            $table->string('locked_by_admin_username', 50);
            $table->string('operation_type', 50); // coin_recharge, coin_deduct, create_card, etc.
            $table->text('operation_details')->nullable();
            $table->timestamp('locked_at');
            $table->timestamp('expires_at'); // Auto-unlock sau thời gian nhất định
            $table->string('lock_token', 100); // Unique token để verify lock
            $table->timestamps();

            $table->unique(['resource_type', 'resource_id', 'operation_type'], 'admin_locks_unique');
            $table->index(['locked_by_admin_id']);
            $table->index(['expires_at']);
        });
        }

        // Bảng queue cho các thao tác quan trọng
        if (!Schema::hasTable('admin_operation_queue')) {
            Schema::create('admin_operation_queue', function (Blueprint $table) {
            $table->id();
            $table->string('operation_type', 50);
            $table->string('resource_type', 50);
            $table->string('resource_id', 100);
            $table->unsignedBigInteger('requested_by_admin_id');
            $table->string('requested_by_admin_username', 50);
            $table->text('operation_data'); // Dữ liệu thao tác (JSON string)
            $table->enum('status', ['pending', 'processing', 'completed', 'failed', 'cancelled'])->default('pending');
            $table->text('result_message')->nullable();
            $table->text('result_data')->nullable(); // JSON string
            $table->timestamp('requested_at');
            $table->timestamp('processed_at')->nullable();
            $table->integer('retry_count')->default(0);
            $table->integer('max_retries')->default(3);
            $table->timestamps();

            $table->index(['status', 'requested_at']);
            $table->index(['requested_by_admin_id']);
            $table->index(['resource_type', 'resource_id']);
        });
        }

        // Bảng conflict detection
        if (!Schema::hasTable('admin_operation_conflicts')) {
            Schema::create('admin_operation_conflicts', function (Blueprint $table) {
            $table->id();
            $table->string('resource_type', 50);
            $table->string('resource_id', 100);
            $table->string('operation_type', 50);
            $table->unsignedBigInteger('admin1_id');
            $table->string('admin1_username', 50);
            $table->unsignedBigInteger('admin2_id');
            $table->string('admin2_username', 50);
            $table->text('admin1_data'); // JSON string
            $table->text('admin2_data'); // JSON string
            $table->enum('resolution', ['auto_resolved', 'manual_required', 'ignored'])->default('manual_required');
            $table->text('resolution_notes')->nullable();
            $table->timestamp('detected_at');
            $table->timestamp('resolved_at')->nullable();
            $table->timestamps();

            $table->index(['resource_type', 'resource_id']);
            $table->index(['detected_at']);
            $table->index(['resolution']);
        });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('admin_operation_conflicts');
        Schema::dropIfExists('admin_operation_queue');
        Schema::dropIfExists('admin_operation_locks');
        Schema::dropIfExists('admin_online_sessions');
    }
};
