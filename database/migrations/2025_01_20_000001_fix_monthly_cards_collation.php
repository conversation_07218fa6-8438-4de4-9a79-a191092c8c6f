<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Fix collation mismatch between monthly_cards.username and t_account.UserName
        try {
            // Change monthly_cards.username collation to match t_account.UserName
            DB::statement('ALTER TABLE monthly_cards MODIFY username VARCHAR(50) CHARACTER SET utf8 COLLATE utf8_general_ci');
            
            // Also fix user_coins.username if it exists
            if (Schema::hasTable('user_coins')) {
                DB::statement('ALTER TABLE user_coins MODIFY username VARCHAR(50) CHARACTER SET utf8 COLLATE utf8_general_ci');
            }
            
            // Fix coin_recharge_logs.username if it exists
            if (Schema::hasTable('coin_recharge_logs')) {
                DB::statement('ALTER TABLE coin_recharge_logs MODIFY username VA<PERSON>HAR(50) CHARACTER SET utf8 COLLATE utf8_general_ci');
            }
            
            // Fix giftcode_usage.username if it exists
            if (Schema::hasTable('giftcode_usage')) {
                DB::statement('ALTER TABLE giftcode_usage MODIFY username VARCHAR(50) CHARACTER SET utf8 COLLATE utf8_general_ci');
            }
            
            echo "✅ Fixed collation for monthly_cards and related tables\n";
            
        } catch (\Exception $e) {
            echo "⚠️ Warning: Could not fix collation - " . $e->getMessage() . "\n";
            // Don't fail the migration, just log the warning
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert back to utf8_unicode_ci if needed
        try {
            DB::statement('ALTER TABLE monthly_cards MODIFY username VARCHAR(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci');
            
            if (Schema::hasTable('user_coins')) {
                DB::statement('ALTER TABLE user_coins MODIFY username VARCHAR(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci');
            }
            
            if (Schema::hasTable('coin_recharge_logs')) {
                DB::statement('ALTER TABLE coin_recharge_logs MODIFY username VARCHAR(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci');
            }
            
            if (Schema::hasTable('giftcode_usage')) {
                DB::statement('ALTER TABLE giftcode_usage MODIFY username VARCHAR(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci');
            }
            
        } catch (\Exception $e) {
            // Ignore errors on rollback
        }
    }
};
