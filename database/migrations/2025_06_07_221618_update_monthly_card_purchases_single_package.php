<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateMonthlyCardPurchasesSinglePackage extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Update existing records to use 'premium' type
        DB::table('monthly_card_purchases')
            ->whereIn('package_type', ['basic', 'vip'])
            ->update(['package_type' => 'premium']);

        // Update the enum to only allow 'premium'
        DB::statement("ALTER TABLE monthly_card_purchases MODIFY COLUMN package_type ENUM('premium') NOT NULL");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Revert back to original enum values
        DB::statement("ALTER TABLE monthly_card_purchases MODIFY COLUMN package_type ENUM('basic', 'premium', 'vip') NOT NULL");
    }
}
