<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateRegistrationWhitelistTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('registration_whitelist', function (Blueprint $table) {
            $table->id();
            $table->string('email')->unique()->comment('Email được phép đăng ký');
            $table->string('username')->nullable()->comment('Username được phép đăng ký (tùy chọn)');
            $table->boolean('is_used')->default(false)->comment('Đã được sử dụng để đăng ký chưa');
            $table->timestamp('used_at')->nullable()->comment('Thời gian sử dụng');
            $table->integer('used_by_account_id')->nullable()->comment('ID tài khoản đã sử dụng');
            $table->string('notes')->nullable()->comment('Ghi chú');
            $table->boolean('is_active')->default(true)->comment('Trạng thái hoạt động');
            $table->timestamps();

            // Indexes
            $table->index(['email', 'is_active']);
            $table->index(['username', 'is_active']);
            $table->index('is_used');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('registration_whitelist');
    }
}
