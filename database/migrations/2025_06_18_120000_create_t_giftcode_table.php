<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTGiftcodeTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Create t_giftcode table (main giftcode table on website database)
        if (!Schema::hasTable('t_giftcode')) {
            Schema::create('t_giftcode', function (Blueprint $table) {
                $table->id();
                $table->tinyInteger('type')->default(1); // 1=public, 2=private, 0=character
                $table->text('accounts')->nullable(); // Comma-separated usernames for private codes
                $table->boolean('multiple')->default(false); // 0=single code, 1=multiple codes
                $table->text('code'); // Array of giftcode strings (JSON stored as text)
                $table->text('items'); // Array of items: goodsid,count,binding,forge_level,appendproplev,lucky,excellenceinfo (JSON stored as text)
                $table->string('content'); // Description/message for the giftcode
                $table->integer('limit')->default(0); // Usage limit (0 = unlimited)
                $table->integer('period')->default(0); // Expiry period in days (0 = no expiry)
                $table->integer('zoneid')->default(0); // Server/Zone ID (0 = all servers)
                $table->timestamps();
                
                // Indexes for performance
                $table->index(['type', 'zoneid']);
                $table->index('created_at');
            });
        }
        
        // Create t_giftcode_log table (main log table on website database)
        if (!Schema::hasTable('t_giftcode_log')) {
            Schema::create('t_giftcode_log', function (Blueprint $table) {
                $table->id();
                $table->unsignedBigInteger('uid'); // User ID from t_account
                $table->unsignedBigInteger('rid')->nullable(); // Character ID from t_roles
                $table->integer('zoneid')->default(1); // Server/Zone ID
                $table->string('giftcode'); // The actual gift code used
                $table->unsignedBigInteger('groupid'); // Reference to t_giftcode table
                $table->timestamps();
                
                // Indexes for performance
                $table->index(['uid', 'groupid']);
                $table->index(['rid', 'groupid']);
                $table->index('zoneid');
                $table->index('created_at');
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('t_giftcode_log');
        Schema::dropIfExists('t_giftcode');
    }
}
