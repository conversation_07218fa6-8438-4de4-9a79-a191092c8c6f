<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTHistoryTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Create t_history table for transaction logs
        if (!Schema::hasTable('t_history')) {
            Schema::create('t_history', function (Blueprint $table) {
                $table->id();
                $table->unsignedBigInteger('uid'); // User ID from t_account
                $table->unsignedBigInteger('rid')->nullable(); // Role ID (character ID)
                $table->integer('zoneid')->default(1); // Server/Zone ID
                $table->tinyInteger('type'); // Transaction type: 4 = monthly card, 1 = recharge, 2 = withdraw, etc.
                $table->bigInteger('balance'); // Balance after transaction
                $table->text('content'); // JSON content with transaction details
                $table->timestamps();
                
                // Indexes for performance
                $table->index(['uid', 'type']);
                $table->index(['rid', 'type']);
                $table->index('zoneid');
                $table->index('created_at');
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('t_history');
    }
}
