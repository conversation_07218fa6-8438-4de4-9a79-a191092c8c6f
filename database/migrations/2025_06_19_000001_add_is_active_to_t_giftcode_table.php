<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddIsActiveToTGiftcodeTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('t_giftcode', function (Blueprint $table) {
            $table->boolean('is_active')->default(true)->after('zoneid');
            $table->string('name')->nullable()->after('content'); // Add name field for better management
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('t_giftcode', function (Blueprint $table) {
            $table->dropColumn(['is_active', 'name']);
        });
    }
}
