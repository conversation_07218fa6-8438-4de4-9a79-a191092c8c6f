<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddCharacterFieldsToMonthlyCardPurchases extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('monthly_card_purchases', function (Blueprint $table) {
            $table->unsignedBigInteger('character_id')->nullable()->after('user_id');
            $table->string('character_name', 50)->nullable()->after('character_id');
            $table->tinyInteger('zone_id')->default(1)->after('character_name');
            
            // Add indexes for better performance
            $table->index(['character_id']);
            $table->index(['user_id', 'character_id']);
            $table->index(['character_id', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('monthly_card_purchases', function (Blueprint $table) {
            $table->dropIndex(['character_id']);
            $table->dropIndex(['user_id', 'character_id']);
            $table->dropIndex(['character_id', 'status']);
            
            $table->dropColumn(['character_id', 'character_name', 'zone_id']);
        });
    }
}
