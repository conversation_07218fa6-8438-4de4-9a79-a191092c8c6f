<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class GiftcodeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $giftcodes = [
            [
                'code' => 'WELCOME2025',
                'name' => 'Giftcode chào mừng năm mới',
                'description' => 'Phần thưởng chào mừng năm mới 2025',
                'type' => 'public',
                'rewards' => json_encode([
                    [
                        'type' => 'coins',
                        'amount' => 1000
                    ],
                    [
                        'type' => 'item',
                        'name' => 'Jewel of Bless',
                        'data' => '14,5,1,0,0,0,0',
                        'quantity' => 5
                    ],
                    [
                        'type' => 'item',
                        'name' => 'Jewel of Soul',
                        'data' => '15,10,1,0,0,0,0',
                        'quantity' => 10
                    ]
                ]),
                'max_uses' => 1000,
                'used_count' => 234,
                'is_active' => 1,
                'expires_at' => Carbon::now()->addMonths(2),
                'created_by' => 1,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'code' => 'VIPONLY123',
                'name' => 'Giftcode dành cho VIP',
                'description' => 'Phần thưởng đặc biệt cho thành viên VIP',
                'type' => 'vip',
                'rewards' => json_encode([
                    [
                        'type' => 'coins',
                        'amount' => 5000
                    ],
                    [
                        'type' => 'item',
                        'name' => 'Jewel of Life',
                        'data' => '16,1,1,0,0,0,0',
                        'quantity' => 1
                    ],
                    [
                        'type' => 'item',
                        'name' => 'Jewel of Chaos',
                        'data' => '12,20,1,0,0,0,0',
                        'quantity' => 20
                    ]
                ]),
                'max_uses' => 100,
                'used_count' => 67,
                'is_active' => 1,
                'expires_at' => Carbon::now()->addMonth(),
                'created_by' => 1,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'code' => 'EVENT2025',
                'name' => 'Sự kiện Tết Nguyên Đán',
                'description' => 'Phần thưởng sự kiện Tết Nguyên Đán 2025',
                'type' => 'event',
                'rewards' => json_encode([
                    [
                        'type' => 'coins',
                        'amount' => 2000
                    ],
                    [
                        'type' => 'item',
                        'name' => 'Jewel of Bless',
                        'data' => '14,10,1,0,0,0,0',
                        'quantity' => 10
                    ],
                    [
                        'type' => 'item',
                        'name' => 'Box of Luck',
                        'data' => '30,1,1,0,0,0,0',
                        'quantity' => 1
                    ]
                ]),
                'max_uses' => 500,
                'used_count' => 123,
                'is_active' => 1,
                'expires_at' => Carbon::now()->addWeeks(3),
                'created_by' => 1,
                'created_at' => now(),
                'updated_at' => now()
            ],

        ];

        foreach ($giftcodes as $giftcode) {
            DB::table('giftcodes')->insert($giftcode);
        }
    }
}
