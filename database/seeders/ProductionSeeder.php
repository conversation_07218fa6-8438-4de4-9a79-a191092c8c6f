<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;

class ProductionSeeder extends Seeder
{
    /**
     * Run the database seeds for production environment.
     */
    public function run(): void
    {
        $this->command->info('🌱 Seeding production data...');

        // Create default admin user
        $this->createDefaultAdmin();
        
        // Create default giftcodes
        $this->createDefaultGiftcodes();
        
        // Create IP management entries
        $this->createIPManagement();

        $this->command->info('✅ Production seeding completed!');
    }

    /**
     * Create default admin user
     */
    private function createDefaultAdmin()
    {
        $this->command->info('👨‍💼 Creating default admin user...');

        // Check if admin already exists
        $existingAdmin = DB::table('admin_users')->where('username', 'admin')->first();
        
        if (!$existingAdmin) {
            DB::table('admin_users')->insert([
                'username' => 'admin',
                'email' => '<EMAIL>',
                'password' => Hash::make('admin123'),
                'role' => 'super_admin',
                'permissions' => json_encode([
                    'admin_users' => ['view', 'create', 'edit', 'delete'],
                    'accounts' => ['view', 'create', 'edit', 'delete'],
                    'characters' => ['view', 'create', 'edit', 'delete'],
                    'coin_recharge' => ['view', 'create', 'edit'],
                    'giftcodes' => ['view', 'create', 'edit', 'delete'],
                    'analytics' => ['view'],
                    'ip_management' => ['view', 'create', 'edit', 'delete'],
                    'admin_logs' => ['view'],
                    'monthly_cards' => ['view', 'create', 'edit', 'delete']
                ]),
                'is_active' => 1,
                'last_login' => null,
                'last_ip' => null,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now()
            ]);

            $this->command->info('✅ Default admin created: admin / admin123');
        } else {
            $this->command->info('ℹ️  Admin user already exists');
        }
    }

    /**
     * Create default giftcodes
     */
    private function createDefaultGiftcodes()
    {
        $this->command->info('🎁 Creating default giftcodes...');

        $defaultGiftcodes = [
            [
                'code' => 'WELCOME2024',
                'type' => 'mixed',
                'reward_coins' => 10000,
                'reward_items' => "14,5,1,0,0,0,0\n15,3,1,0,0,0,0",
                'usage_limit' => 100,
                'used_count' => 0,
                'expires_at' => Carbon::now()->addMonths(3),
                'is_active' => 1,
                'description' => 'Welcome gift for new players'
            ],
            [
                'code' => 'NEWBIE100',
                'type' => 'coins',
                'reward_coins' => 5000,
                'reward_items' => null,
                'usage_limit' => 500,
                'used_count' => 0,
                'expires_at' => Carbon::now()->addMonths(6),
                'is_active' => 1,
                'description' => 'Starter coins for beginners'
            ],
            [
                'code' => 'JEWELS2024',
                'type' => 'items',
                'reward_coins' => 0,
                'reward_items' => "13,2,1,0,0,0,0\n14,3,1,0,0,0,0\n15,2,1,0,0,0,0",
                'usage_limit' => 200,
                'used_count' => 0,
                'expires_at' => Carbon::now()->addMonths(2),
                'is_active' => 1,
                'description' => 'Jewel package for upgrading'
            ]
        ];

        foreach ($defaultGiftcodes as $giftcode) {
            $existing = DB::table('giftcodes')->where('code', $giftcode['code'])->first();
            
            if (!$existing) {
                DB::table('giftcodes')->insert(array_merge($giftcode, [
                    'created_by' => 1, // Default admin ID
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now()
                ]));
                
                $this->command->info("✅ Created giftcode: {$giftcode['code']}");
            }
        }
    }

    /**
     * Create IP management entries
     */
    private function createIPManagement()
    {
        $this->command->info('🛡️ Creating IP management entries...');

        // Create default allowed IPs (localhost and common admin IPs)
        $defaultIPs = [
            [
                'ip_address' => '127.0.0.1',
                'type' => 'whitelist',
                'reason' => 'Localhost access',
                'is_active' => 1
            ],
            [
                'ip_address' => '::1',
                'type' => 'whitelist', 
                'reason' => 'IPv6 localhost access',
                'is_active' => 1
            ]
        ];

        foreach ($defaultIPs as $ip) {
            $existing = DB::table('ip_management')->where('ip_address', $ip['ip_address'])->first();
            
            if (!$existing) {
                DB::table('ip_management')->insert(array_merge($ip, [
                    'created_by' => 1,
                    'expires_at' => null,
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now()
                ]));
                
                $this->command->info("✅ Added IP: {$ip['ip_address']} ({$ip['type']})");
            }
        }
    }
}
