#!/bin/bash

# MU Game Admin Panel - Ubuntu VPS Deploy
# Server: ************** (Port 80)

set -e

echo "🚀 MU Game Admin Panel - Ubuntu VPS Deploy"
echo "==========================================="
echo "Server: **************"
echo "Admin: http://**************/admin"
echo "User: http://**************/user"
echo "Port: 80 (Standard HTTP)"
echo ""

# Check if running as root or with sudo
if [[ $EUID -eq 0 ]]; then
    echo "⚠️  Running as root - this is fine for VPS setup"
    SUDO=""
else
    echo "🔐 Running with sudo privileges"
    SUDO="sudo"
fi

# Update system packages
echo "📦 Updating Ubuntu packages..."
export DEBIAN_FRONTEND=noninteractive
$SUDO apt update && $SUDO apt upgrade -y -o Dpkg::Options::="--force-confdef" -o Dpkg::Options::="--force-confold"

# Install Docker if needed
if ! command -v docker &> /dev/null; then
    echo "🐳 Installing Docker on Ubuntu..."
    $SUDO apt install -y apt-transport-https ca-certificates curl gnupg lsb-release
    curl -fsSL https://download.docker.com/linux/ubuntu/gpg | $SUDO gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
    echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | $SUDO tee /etc/apt/sources.list.d/docker.list > /dev/null
    $SUDO apt update
    $SUDO apt install -y docker-ce docker-ce-cli containerd.io
    $SUDO usermod -aG docker $USER
    echo "✅ Docker installed. You may need to logout/login for group changes."
fi

# Install Docker Compose if needed
if ! command -v docker-compose &> /dev/null; then
    echo "🔧 Installing Docker Compose..."
    $SUDO curl -L "https://github.com/docker/compose/releases/download/v2.24.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    $SUDO chmod +x /usr/local/bin/docker-compose
    # Create symlink for compatibility
    $SUDO ln -sf /usr/local/bin/docker-compose /usr/bin/docker-compose
fi

# Install additional tools
echo "🛠️  Installing additional tools..."
$SUDO apt install -y curl wget htop ufw git

# Configure firewall
echo "🔥 Configuring UFW firewall..."
$SUDO ufw --force enable
$SUDO ufw allow ssh
$SUDO ufw allow 80/tcp
$SUDO ufw allow 443/tcp
echo "✅ Firewall configured (SSH, HTTP, HTTPS allowed)"

# Setup directories
echo "📁 Setting up directories..."
mkdir -p storage/{logs,framework/{cache,sessions,views}} bootstrap/cache public/{uploads,images/qr}
chmod -R 755 storage bootstrap/cache public/uploads public/images

# Stop any existing containers
echo "🛑 Stopping existing containers..."
docker-compose down 2>/dev/null || true

# Build and deploy
echo "🚀 Building and deploying application..."
docker-compose up -d --build

# Wait for services to start
echo "⏳ Waiting for services to start (90 seconds)..."
sleep 90

# Laravel setup
echo "⚙️ Setting up Laravel..."
docker-compose exec -T app php artisan key:generate --force

# Check database connection
echo "🗃️ Checking database connection..."
if docker-compose exec -T app php artisan migrate:status | grep -q "Pending"; then
    echo "Running new migrations..."
    docker-compose exec -T app php artisan migrate --force
else
    echo "✅ Database is up to date (external MySQL server)"
fi

# Create storage link
docker-compose exec -T app php artisan storage:link

# Performance optimizations
echo "⚡ Applying performance optimizations..."

# Laravel optimizations
docker-compose exec -T app php artisan config:cache
docker-compose exec -T app php artisan route:cache
docker-compose exec -T app php artisan view:cache
docker-compose exec -T app php artisan optimize

# Composer optimization
docker-compose exec -T app composer dump-autoload --optimize --no-dev

# Asset optimization
echo "🗜️ Optimizing assets..."
docker-compose exec -T app sh -c "
cd /var/www/html/public
find . -type f \( -name '*.css' -o -name '*.js' -o -name '*.html' \) -exec gzip -k -6 {} \; 2>/dev/null || true
"

# Set final permissions
docker-compose exec -T app chown -R www-data:www-data /var/www/html/storage
docker-compose exec -T app chown -R www-data:www-data /var/www/html/bootstrap/cache

# Restart to apply all optimizations
echo "🔄 Restarting services..."
docker-compose restart app
sleep 30

# Test the deployment
echo "🧪 Testing deployment..."
response_time=$(curl -w "%{time_total}" -o /dev/null -s http://localhost:80 2>/dev/null || echo "N/A")

echo ""
echo "🎉 Ubuntu VPS Deploy Complete!"
echo "==============================="
echo ""
echo "🌐 Your MU Game Admin Panel:"
echo "   Main: http://**************"
echo "   Admin: http://**************/admin"
echo "   User: http://**************/user"
echo ""
echo "📊 Performance: Response time ${response_time}s"
echo ""
echo "📋 Default Login:"
echo "   Username: admin"
echo "   Password: admin123"
echo ""
echo "⚡ Optimizations Applied:"
echo "   ✅ Ubuntu packages updated"
echo "   ✅ Docker & Docker Compose installed"
echo "   ✅ UFW firewall configured"
echo "   ✅ Port 80 opened"
echo "   ✅ Gzip compression enabled"
echo "   ✅ Laravel optimizations"
echo "   ✅ PHP OPcache enabled"
echo "   ✅ Redis caching ready"
echo ""
echo "⚠️  Important Next Steps:"
echo "1. Change admin password immediately"
echo "2. Test all functionality"
echo "3. Setup SSL certificate (optional): certbot --nginx"
echo "4. Monitor logs: docker-compose logs -f"
echo ""
echo "🛠️  Management Commands:"
echo "   View logs: docker-compose logs -f"
echo "   Stop: docker-compose down"
echo "   Restart: docker-compose restart"
echo "   Update: git pull && docker-compose up -d --build"
echo ""

# Show final status
docker-compose ps
echo ""
echo "🎯 Access your panel: http://**************"
echo "🔧 Server IP: **************"
echo "🚪 Port: 80 (Standard HTTP)"
