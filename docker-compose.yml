version: '3.8'

services:
  # <PERSON>vel MU Game Admin Panel
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: mu_admin_app
    restart: unless-stopped
    ports:
      - "80:80"
    volumes:
      - ./storage/logs:/var/www/html/storage/logs
      - ./public/uploads:/var/www/html/public/uploads
      - ./public/images:/var/www/html/public/images
      - app_cache:/var/www/html/bootstrap/cache
    environment:
      - APP_ENV=production
      - APP_DEBUG=false
      - APP_KEY=${APP_KEY}
      - APP_URL=${APP_URL}
      - DB_HOST=${DB_HOST}
      - DB_PORT=${DB_PORT}
      - DB_DATABASE=${DB_DATABASE}
      - DB_USERNAME=${DB_USERNAME}
      - DB_PASSWORD=${DB_PASSWORD}
      - GAME_DB_HOST=${GAME_DB_HOST}
      - GAME_DB_PORT=${GAME_DB_PORT}
      - GAME_DB_DATABASE=${GAME_DB_DATABASE}
      - GAME_DB_USERNAME=${GAME_DB_USERNAME}
      - GAME_DB_PASSWORD=${GAME_DB_PASSWORD}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - CACHE_DRIVER=redis
      - SESSION_DRIVER=redis
      - QUEUE_CONNECTION=sync
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - mu_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis Cache & Session
  redis:
    image: redis:7-alpine
    container_name: mu_admin_redis
    restart: unless-stopped
    volumes:
      - redis_data:/data
      - ./docker/redis/redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf --appendonly yes
    networks:
      - mu_network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3
    sysctls:
      - net.core.somaxconn=65535
    ulimits:
      memlock: -1

volumes:
  redis_data:
    driver: local
  app_cache:
    driver: local

networks:
  mu_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
