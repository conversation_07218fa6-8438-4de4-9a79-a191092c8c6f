user www-data;
worker_processes auto;
pid /run/nginx.pid;

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Logging
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log warn;

    # Performance
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 100M;

    # Optimized Gzip compression for VN users
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        text/json
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        application/x-font-ttf
        application/font-woff
        application/font-woff2
        font/woff
        font/woff2
        image/svg+xml
        image/x-icon
        image/png
        image/jpg
        image/jpeg;

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;

    # Laravel MU Admin Panel
    server {
        listen 80;
        server_name ************** localhost;
        root /var/www/html/public;
        index index.php index.html;

        # Security headers
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header Referrer-Policy "no-referrer-when-downgrade" always;
        add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

        # Laravel routes
        location / {
            try_files $uri $uri/ /index.php?$query_string;
        }

        # Admin login rate limiting
        location ~ ^/(admin|user)/login {
            limit_req zone=login burst=3 nodelay;
            try_files $uri $uri/ /index.php?$query_string;
        }

        # API rate limiting
        location ~ ^/api/ {
            limit_req zone=api burst=20 nodelay;
            try_files $uri $uri/ /index.php?$query_string;
        }

        # PHP-FPM
        location ~ \.php$ {
            fastcgi_pass 127.0.0.1:9000;
            fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
            include fastcgi_params;
            fastcgi_hide_header X-Powered-By;
        }

        # Optimized static files caching for VN users
        location ~* \.(jpg|jpeg|png|gif|ico|webp|bmp)$ {
            expires 30d;
            add_header Cache-Control "public, max-age=2592000";
            add_header Vary "Accept-Encoding";
            access_log off;
            gzip_static on;
        }

        location ~* \.(css|js)$ {
            expires 7d;
            add_header Cache-Control "public, max-age=604800";
            add_header Vary "Accept-Encoding";
            access_log off;
            gzip_static on;
        }

        location ~* \.(woff|woff2|ttf|eot|otf)$ {
            expires 30d;
            add_header Cache-Control "public, max-age=2592000";
            add_header Access-Control-Allow-Origin "*";
            add_header Vary "Accept-Encoding";
            access_log off;
            gzip_static on;
        }

        # Deny access to sensitive files
        location ~ /\. {
            deny all;
            access_log off;
            log_not_found off;
        }

        location ~ /(storage|bootstrap|config|database|resources|routes|tests|vendor)/ {
            deny all;
            access_log off;
            log_not_found off;
        }

        # Health check endpoint
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
    }
}
