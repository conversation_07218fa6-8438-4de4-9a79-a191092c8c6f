[PHP]
; Performance
memory_limit = 256M
max_execution_time = 120
max_input_time = 120
max_input_vars = 3000

; File uploads
file_uploads = On
upload_max_filesize = 100M
max_file_uploads = 20
post_max_size = 100M

; Error handling
display_errors = Off
display_startup_errors = Off
log_errors = On
error_log = /var/log/php_errors.log
error_reporting = E_ALL & ~E_DEPRECATED & ~E_STRICT

; Session
session.save_handler = redis
session.save_path = "tcp://redis:6379"
session.gc_maxlifetime = 7200
session.cookie_lifetime = 0
session.cookie_secure = 0
session.cookie_httponly = 1
session.use_strict_mode = 1

; Security
expose_php = Off
allow_url_fopen = On
allow_url_include = Off

; Date
date.timezone = Asia/Ho_Chi_Minh

; OPcache
opcache.enable = 1
opcache.enable_cli = 1
opcache.memory_consumption = 128
opcache.interned_strings_buffer = 8
opcache.max_accelerated_files = 4000
opcache.revalidate_freq = 2
opcache.fast_shutdown = 1
opcache.validate_timestamps = 0

; Realpath cache
realpath_cache_size = 4096K
realpath_cache_ttl = 600
