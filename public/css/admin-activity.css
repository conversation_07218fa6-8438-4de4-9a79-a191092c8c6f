/**
 * Admin Activity & Concurrency Control CSS
 */

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    max-width: 400px;
}

.toast {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    margin-bottom: 10px;
    overflow: hidden;
    animation: slideIn 0.3s ease-out;
}

.toast-info {
    border-left: 4px solid #17a2b8;
}

.toast-success {
    border-left: 4px solid #28a745;
}

.toast-warning {
    border-left: 4px solid #ffc107;
}

.toast-danger {
    border-left: 4px solid #dc3545;
}

.toast-content {
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.toast-content button {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: #999;
    padding: 0;
    margin-left: 10px;
}

.toast-content button:hover {
    color: #333;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Conflict Indicators */
.conflict-indicator {
    position: relative;
    display: inline-block;
}

.conflict-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #dc3545;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

/* Lock Status Indicators */
.lock-status {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.lock-status.locked {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.lock-status.available {
    background: #d1edff;
    color: #004085;
    border: 1px solid #bee5eb;
}

.lock-status.conflict {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Processing Indicators */
.processing-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9998;
}

.processing-content {
    background: white;
    padding: 30px;
    border-radius: 8px;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.processing-spinner {
    font-size: 24px;
    color: #007bff;
    margin-bottom: 15px;
}

/* Admin Activity Cards */
.admin-activity-card {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    background: #fff;
    transition: all 0.3s ease;
}

.admin-activity-card:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.admin-activity-card.online {
    border-left: 4px solid #28a745;
}

.admin-activity-card.offline {
    border-left: 4px solid #6c757d;
    opacity: 0.7;
}

.admin-activity-card.conflict {
    border-left: 4px solid #dc3545;
    background: #fff5f5;
}

.admin-status {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    font-size: 12px;
    font-weight: 500;
}

.admin-status.online {
    color: #28a745;
}

.admin-status.offline {
    color: #6c757d;
}

.admin-status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: currentColor;
    animation: blink 2s infinite;
}

@keyframes blink {
    0%, 50% {
        opacity: 1;
    }
    51%, 100% {
        opacity: 0.3;
    }
}

/* Activity Timeline */
.activity-timeline {
    position: relative;
    padding-left: 30px;
}

.activity-timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.activity-item {
    position: relative;
    margin-bottom: 20px;
    padding: 10px 15px;
    background: #f8f9fa;
    border-radius: 6px;
}

.activity-item::before {
    content: '';
    position: absolute;
    left: -22px;
    top: 15px;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: #007bff;
    border: 2px solid #fff;
}

.activity-item.success::before {
    background: #28a745;
}

.activity-item.warning::before {
    background: #ffc107;
}

.activity-item.danger::before {
    background: #dc3545;
}

/* Conflict Resolution Modal */
.conflict-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.conflict-modal-content {
    background: white;
    border-radius: 8px;
    padding: 30px;
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.conflict-details {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 15px;
    margin: 15px 0;
}

.conflict-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 20px;
}

/* Real-time Updates */
.real-time-indicator {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    font-size: 12px;
    color: #28a745;
}

.real-time-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: #28a745;
    animation: pulse 1.5s infinite;
}

/* Responsive Design */
@media (max-width: 768px) {
    .toast-container {
        left: 10px;
        right: 10px;
        max-width: none;
    }
    
    .admin-activity-card {
        padding: 10px;
    }
    
    .conflict-modal-content {
        padding: 20px;
        margin: 10px;
    }
    
    .conflict-actions {
        flex-direction: column;
    }
    
    .conflict-actions button {
        width: 100%;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .toast {
        background: #2d3748;
        color: #e2e8f0;
    }
    
    .admin-activity-card {
        background: #2d3748;
        border-color: #4a5568;
        color: #e2e8f0;
    }
    
    .activity-item {
        background: #4a5568;
        color: #e2e8f0;
    }
    
    .conflict-modal-content {
        background: #2d3748;
        color: #e2e8f0;
    }
    
    .conflict-details {
        background: #4a5568;
        border-color: #718096;
    }
}
