/* Optimized Analytics Dashboard Styles */

/* Performance indicators */
.loading-indicator {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 20px;
    border-radius: 8px;
    z-index: 9999;
    display: none;
}

.loading-spinner {
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3498db;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    animation: spin 1s linear infinite;
    margin: 0 auto 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Control panel */
.analytics-controls {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 20px;
    border-radius: 12px;
    margin-bottom: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.controls-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    align-items: center;
}

.control-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.control-group label {
    color: white;
    font-weight: 600;
    font-size: 14px;
}

.control-group select,
.control-group input {
    padding: 10px;
    border: none;
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.9);
    font-size: 14px;
}

.control-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.btn-control {
    padding: 10px 16px;
    border: none;
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.btn-control:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

.btn-control.active {
    background: rgba(255, 255, 255, 0.4);
}

/* Auto-refresh toggle */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.3);
    transition: 0.4s;
    border-radius: 24px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.4s;
    border-radius: 50%;
}

input:checked + .toggle-slider {
    background-color: #4CAF50;
}

input:checked + .toggle-slider:before {
    transform: translateX(26px);
}

/* Updated stat cards */
.stat-card.updated {
    animation: pulse 1s ease-in-out;
    border: 2px solid #4CAF50;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Growth indicators */
.growth-indicator {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
    display: inline-block;
}

.growth-indicator.positive {
    background: rgba(76, 175, 80, 0.2);
    color: #4CAF50;
}

.growth-indicator.negative {
    background: rgba(244, 67, 54, 0.2);
    color: #f44336;
}

.growth-indicator.neutral {
    background: rgba(158, 158, 158, 0.2);
    color: #9e9e9e;
}

/* Performance metrics */
.performance-metrics {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
}

.metric-item {
    text-align: center;
    color: white;
}

.metric-value {
    font-size: 24px;
    font-weight: bold;
    display: block;
}

.metric-label {
    font-size: 12px;
    opacity: 0.8;
}

/* Notifications */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 8px;
    color: white;
    font-weight: 600;
    z-index: 10000;
    animation: slideIn 0.3s ease-out;
    max-width: 300px;
}

.notification-success {
    background: linear-gradient(135deg, #4CAF50, #45a049);
}

.notification-error {
    background: linear-gradient(135deg, #f44336, #d32f2f);
}

.notification-warning {
    background: linear-gradient(135deg, #ff9800, #f57c00);
}

.notification-info {
    background: linear-gradient(135deg, #2196F3, #1976D2);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Real-time indicators */
.real-time-indicator {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 6px 12px;
    background: rgba(76, 175, 80, 0.1);
    border: 1px solid #4CAF50;
    border-radius: 20px;
    font-size: 12px;
    color: #4CAF50;
}

.real-time-dot {
    width: 8px;
    height: 8px;
    background: #4CAF50;
    border-radius: 50%;
    animation: blink 2s infinite;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.3; }
}

/* Chart optimizations */
.chart-container {
    position: relative;
    height: 300px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 10px;
}

.chart-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: rgba(255, 255, 255, 0.7);
}

/* Responsive optimizations */
@media (max-width: 768px) {
    .analytics-controls {
        padding: 15px;
    }
    
    .controls-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .control-buttons {
        justify-content: center;
    }
    
    .btn-control {
        flex: 1;
        min-width: 120px;
    }
    
    .notification {
        right: 10px;
        left: 10px;
        max-width: none;
    }
}

/* Dark mode optimizations */
@media (prefers-color-scheme: dark) {
    .analytics-controls {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    }
    
    .performance-metrics {
        background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
    }
}

/* Print optimizations */
@media print {
    .analytics-controls,
    .notification,
    .loading-indicator {
        display: none !important;
    }
    
    .stat-card,
    .chart-card {
        break-inside: avoid;
        page-break-inside: avoid;
    }
}
