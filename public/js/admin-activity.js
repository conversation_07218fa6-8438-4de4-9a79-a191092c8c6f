/**
 * Admin Activity & Concurrency Control JavaScript
 */

class AdminActivityManager {
    constructor() {
        this.currentLocks = new Map();
        this.conflictCheckInterval = null;
        this.activityUpdateInterval = null;
        this.init();
    }

    init() {
        // Start activity tracking
        this.startActivityTracking();
        
        // Add event listeners for form submissions
        this.attachFormListeners();
        
        // Start conflict checking
        this.startConflictChecking();
    }

    /**
     * Start tracking admin activity
     */
    startActivityTracking() {
        // Update activity every 30 seconds
        this.activityUpdateInterval = setInterval(() => {
            this.updateActivity();
        }, 30000);

        // Update activity on page unload
        window.addEventListener('beforeunload', () => {
            this.releaseAllLocks();
        });
    }

    /**
     * Update current activity
     */
    updateActivity() {
        const currentActions = this.getCurrentActions();
        
        fetch('/admin/activity/update-activity', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                current_actions: currentActions
            })
        }).catch(error => {
            console.error('Failed to update activity:', error);
        });
    }

    /**
     * Get current actions from page context
     */
    getCurrentActions() {
        const actions = [];
        const path = window.location.pathname;
        
        // Detect current page actions
        if (path.includes('/coin-recharge')) {
            actions.push({
                type: 'coin_management',
                page: 'coin_recharge',
                timestamp: new Date().toISOString()
            });
        } else if (path.includes('/card-month')) {
            actions.push({
                type: 'monthly_card_management',
                page: 'card_month',
                timestamp: new Date().toISOString()
            });
        } else if (path.includes('/giftcode')) {
            actions.push({
                type: 'giftcode_management',
                page: 'giftcode',
                timestamp: new Date().toISOString()
            });
        }

        return actions;
    }

    /**
     * Attach form listeners for conflict detection
     */
    attachFormListeners() {
        // Coin recharge forms
        this.attachCoinFormListeners();
        
        // Monthly card forms
        this.attachMonthlyCardFormListeners();
        
        // Giftcode forms
        this.attachGiftcodeFormListeners();
    }

    /**
     * Attach listeners to coin recharge forms
     */
    attachCoinFormListeners() {
        // Coin recharge form
        const rechargeForm = document.querySelector('form[action*="coin-recharge"]');
        if (rechargeForm) {
            rechargeForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleCoinOperation(rechargeForm, 'coin_recharge');
            });
        }

        // Coin deduct form
        const deductForm = document.querySelector('form[action*="coin-recharge/deduct"]');
        if (deductForm) {
            deductForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleCoinOperation(deductForm, 'coin_deduct');
            });
        }
    }

    /**
     * Handle coin operations with conflict checking
     */
    async handleCoinOperation(form, operationType) {
        const formData = new FormData(form);
        const username = formData.get('username');
        
        if (!username) {
            alert('Vui lòng nhập username');
            return;
        }

        try {
            // Check for conflicts
            const conflictCheck = await this.checkConflict('user', username, operationType);
            
            if (conflictCheck.conflict) {
                const proceed = confirm(
                    `⚠️ CẢNH BÁO XUNG ĐỘT!\n\n` +
                    `${conflictCheck.message}\n\n` +
                    `Bạn có muốn tiếp tục không? Điều này có thể gây ra xung đột dữ liệu.`
                );
                
                if (!proceed) {
                    return;
                }
            }

            // Create operation lock
            const lockResult = await this.createLock('user', username, operationType, `${operationType} for ${username}`);
            
            if (!lockResult.success) {
                alert(`Không thể tạo lock: ${lockResult.message}`);
                return;
            }

            // Store lock token
            this.currentLocks.set(`user_${username}_${operationType}`, lockResult.lock_token);

            // Show processing indicator
            this.showProcessingIndicator(form);

            // Submit form
            form.submit();

        } catch (error) {
            console.error('Error in coin operation:', error);
            alert('Có lỗi xảy ra khi kiểm tra xung đột');
        }
    }

    /**
     * Attach listeners to monthly card forms
     */
    attachMonthlyCardFormListeners() {
        const monthlyCardForm = document.querySelector('form[action*="card-month"]');
        if (monthlyCardForm) {
            monthlyCardForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleMonthlyCardOperation(monthlyCardForm);
            });
        }
    }

    /**
     * Handle monthly card operations
     */
    async handleMonthlyCardOperation(form) {
        const formData = new FormData(form);
        const username = formData.get('username');
        
        if (!username) {
            alert('Vui lòng nhập username');
            return;
        }

        try {
            const conflictCheck = await this.checkConflict('user', username, 'monthly_card_create');
            
            if (conflictCheck.conflict) {
                const proceed = confirm(`⚠️ Có admin khác đang thao tác với user này!\n\n${conflictCheck.message}\n\nTiếp tục?`);
                if (!proceed) return;
            }

            const lockResult = await this.createLock('user', username, 'monthly_card_create', `Create monthly card for ${username}`);
            
            if (!lockResult.success) {
                alert(`Không thể tạo lock: ${lockResult.message}`);
                return;
            }

            this.currentLocks.set(`user_${username}_monthly_card_create`, lockResult.lock_token);
            this.showProcessingIndicator(form);
            form.submit();

        } catch (error) {
            console.error('Error in monthly card operation:', error);
            alert('Có lỗi xảy ra khi kiểm tra xung đột');
        }
    }

    /**
     * Attach listeners to giftcode forms
     */
    attachGiftcodeFormListeners() {
        const giftcodeForm = document.querySelector('form[action*="giftcode"]');
        if (giftcodeForm) {
            giftcodeForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleGiftcodeOperation(giftcodeForm);
            });
        }
    }

    /**
     * Handle giftcode operations
     */
    async handleGiftcodeOperation(form) {
        const formData = new FormData(form);
        const code = formData.get('code') || formData.get('giftcode_code');
        
        if (!code) {
            alert('Vui lòng nhập mã giftcode');
            return;
        }

        try {
            const conflictCheck = await this.checkConflict('giftcode', code, 'giftcode_create');
            
            if (conflictCheck.conflict) {
                const proceed = confirm(`⚠️ Có admin khác đang thao tác với giftcode này!\n\n${conflictCheck.message}\n\nTiếp tục?`);
                if (!proceed) return;
            }

            const lockResult = await this.createLock('giftcode', code, 'giftcode_create', `Create giftcode ${code}`);
            
            if (!lockResult.success) {
                alert(`Không thể tạo lock: ${lockResult.message}`);
                return;
            }

            this.currentLocks.set(`giftcode_${code}_giftcode_create`, lockResult.lock_token);
            this.showProcessingIndicator(form);
            form.submit();

        } catch (error) {
            console.error('Error in giftcode operation:', error);
            alert('Có lỗi xảy ra khi kiểm tra xung đột');
        }
    }

    /**
     * Check for operation conflicts
     */
    async checkConflict(resourceType, resourceId, operationType) {
        const response = await fetch('/admin/activity/check-conflict', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                resource_type: resourceType,
                resource_id: resourceId,
                operation_type: operationType
            })
        });

        return await response.json();
    }

    /**
     * Create operation lock
     */
    async createLock(resourceType, resourceId, operationType, details) {
        const response = await fetch('/admin/activity/create-lock', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                resource_type: resourceType,
                resource_id: resourceId,
                operation_type: operationType,
                operation_details: details
            })
        });

        return await response.json();
    }

    /**
     * Release a specific lock
     */
    async releaseLock(lockToken) {
        try {
            const response = await fetch('/admin/activity/release-lock', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    lock_token: lockToken
                })
            });

            return await response.json();
        } catch (error) {
            console.error('Error releasing lock:', error);
            return { success: false };
        }
    }

    /**
     * Release all current locks
     */
    async releaseAllLocks() {
        const promises = [];
        
        for (const [key, lockToken] of this.currentLocks) {
            promises.push(this.releaseLock(lockToken));
        }

        await Promise.all(promises);
        this.currentLocks.clear();
    }

    /**
     * Show processing indicator
     */
    showProcessingIndicator(form) {
        const submitBtn = form.querySelector('button[type="submit"], input[type="submit"]');
        if (submitBtn) {
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Đang xử lý...';
        }
    }

    /**
     * Start periodic conflict checking
     */
    startConflictChecking() {
        // Check for conflicts every 10 seconds
        this.conflictCheckInterval = setInterval(() => {
            this.checkForConflicts();
        }, 10000);
    }

    /**
     * Check for conflicts periodically
     */
    async checkForConflicts() {
        try {
            const response = await fetch('/admin/activity/conflicts');
            const data = await response.json();
            
            if (data.success && data.conflicts.length > 0) {
                this.showConflictNotification(data.conflicts.length);
            }
        } catch (error) {
            console.error('Error checking conflicts:', error);
        }
    }

    /**
     * Show conflict notification
     */
    showConflictNotification(conflictCount) {
        // Update conflict badge if exists
        const conflictBadge = document.querySelector('.conflict-badge');
        if (conflictBadge) {
            conflictBadge.textContent = conflictCount;
            conflictBadge.style.display = conflictCount > 0 ? 'inline' : 'none';
        }

        // Show toast notification
        if (conflictCount > 0) {
            this.showToast(`⚠️ Có ${conflictCount} xung đột cần xử lý!`, 'warning');
        }
    }

    /**
     * Show toast notification
     */
    showToast(message, type = 'info') {
        // Create toast element
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.innerHTML = `
            <div class="toast-content">
                <span>${message}</span>
                <button onclick="this.parentElement.parentElement.remove()">&times;</button>
            </div>
        `;

        // Add to page
        let toastContainer = document.querySelector('.toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.className = 'toast-container';
            document.body.appendChild(toastContainer);
        }

        toastContainer.appendChild(toast);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (toast.parentElement) {
                toast.remove();
            }
        }, 5000);
    }

    /**
     * Cleanup on page unload
     */
    cleanup() {
        if (this.activityUpdateInterval) {
            clearInterval(this.activityUpdateInterval);
        }
        
        if (this.conflictCheckInterval) {
            clearInterval(this.conflictCheckInterval);
        }
        
        this.releaseAllLocks();
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.adminActivityManager = new AdminActivityManager();
});

// Cleanup on page unload
window.addEventListener('beforeunload', function() {
    if (window.adminActivityManager) {
        window.adminActivityManager.cleanup();
    }
});
