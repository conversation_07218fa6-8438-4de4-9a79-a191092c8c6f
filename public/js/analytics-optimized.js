/**
 * Optimized Analytics Dashboard JavaScript
 * Provides real-time updates and performance monitoring
 */

class AnalyticsDashboard {
    constructor() {
        this.refreshInterval = null;
        this.autoRefresh = false;
        this.currentPeriod = '7';
        this.charts = {};
        this.init();
    }

    init() {
        this.bindEvents();
        this.initializeCharts();
        this.startPerformanceMonitoring();
    }

    bindEvents() {
        // Period change handler
        document.addEventListener('change', (e) => {
            if (e.target.name === 'period') {
                this.currentPeriod = e.target.value;
                this.refreshData();
            }
        });

        // Auto-refresh toggle
        const autoRefreshToggle = document.getElementById('auto-refresh');
        if (autoRefreshToggle) {
            autoRefreshToggle.addEventListener('change', (e) => {
                this.toggleAutoRefresh(e.target.checked);
            });
        }

        // Manual refresh button
        const refreshBtn = document.getElementById('refresh-data');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.refreshData();
            });
        }

        // Clear cache button
        const clearCacheBtn = document.getElementById('clear-cache');
        if (clearCacheBtn) {
            clearCacheBtn.addEventListener('click', () => {
                this.clearCache();
            });
        }
    }

    async refreshData() {
        try {
            this.showLoading(true);
            
            // Fetch data in parallel
            const [statsData, chartsData, topData] = await Promise.all([
                this.fetchData('stats'),
                this.fetchData('charts'),
                this.fetchData('top')
            ]);

            // Update UI
            this.updateStats(statsData);
            this.updateCharts(chartsData);
            this.updateTopPerformers(topData);

            this.showNotification('Dữ liệu đã được cập nhật', 'success');
        } catch (error) {
            console.error('Error refreshing data:', error);
            this.showNotification('Lỗi khi cập nhật dữ liệu', 'error');
        } finally {
            this.showLoading(false);
        }
    }

    async fetchData(type) {
        const response = await fetch(`/admin/analytics/ajax-data?type=${type}&period=${this.currentPeriod}`, {
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return await response.json();
    }

    updateStats(data) {
        // Update account stats
        this.updateStatCard('total-accounts', data.accounts.total);
        this.updateStatCard('new-accounts', data.accounts.new);
        this.updateStatCard('active-accounts', data.accounts.active);
        this.updateStatCard('banned-accounts', data.accounts.banned);
        this.updateGrowthIndicator('account-growth', data.accounts.growth);

        // Update character stats
        this.updateStatCard('total-characters', data.characters.total);
        this.updateStatCard('new-characters', data.characters.new);
        this.updateStatCard('active-characters', data.characters.active);

        // Update revenue stats
        this.updateStatCard('total-revenue', this.formatCurrency(data.revenue.total));
        this.updateStatCard('period-revenue', this.formatCurrency(data.revenue.period));
        this.updateStatCard('total-transactions', data.revenue.transactions);
        this.updateStatCard('avg-transaction', this.formatCurrency(data.revenue.avg_value));
        this.updateGrowthIndicator('revenue-growth', data.revenue.growth);

        // Update giftcode stats
        this.updateStatCard('total-giftcodes', data.giftcodes.total);
        this.updateStatCard('active-giftcodes', data.giftcodes.active);
        this.updateStatCard('used-giftcodes', data.giftcodes.usage);

        // Update monthly cards stats
        this.updateStatCard('total-monthly-cards', data.monthly_cards.total);
        this.updateStatCard('active-monthly-cards', data.monthly_cards.active);
        this.updateStatCard('monthly-cards-revenue', this.formatCurrency(data.monthly_cards.revenue));

        // Update battle pass stats
        this.updateStatCard('total-battle-pass', data.battle_pass.total);
        this.updateStatCard('active-battle-pass', data.battle_pass.active);
        this.updateStatCard('battle-pass-revenue', this.formatCurrency(data.battle_pass.revenue));

        // Update revenue breakdown
        this.updateStatCard('coin-recharge-total', this.formatCurrency(data.revenue.breakdown.coin_recharge.total));
        this.updateStatCard('coin-recharge-period', this.formatCurrency(data.revenue.breakdown.coin_recharge.period));
        this.updateStatCard('coin-recharge-transactions', data.revenue.breakdown.coin_recharge.transactions);

        this.updateStatCard('monthly-cards-breakdown-total', this.formatCurrency(data.revenue.breakdown.monthly_cards.total));
        this.updateStatCard('monthly-cards-breakdown-period', this.formatCurrency(data.revenue.breakdown.monthly_cards.period));
        this.updateStatCard('monthly-cards-breakdown-transactions', data.revenue.breakdown.monthly_cards.transactions);

        this.updateStatCard('avg-transaction-breakdown', this.formatCurrency(data.revenue.avg_value));
    }

    updateStatCard(elementId, value) {
        const element = document.getElementById(elementId);
        if (element) {
            // Check if value is already formatted (contains currency symbols)
            if (typeof value === 'string' && (value.includes('đ') || value.includes('VND'))) {
                element.textContent = value;
            } else {
                element.textContent = this.formatNumber(value);
            }
            element.classList.add('updated');
            setTimeout(() => element.classList.remove('updated'), 1000);
        }
    }

    updateGrowthIndicator(elementId, growth) {
        const element = document.getElementById(elementId);
        if (element) {
            element.textContent = `${growth > 0 ? '+' : ''}${growth.toFixed(1)}%`;
            element.className = `growth-indicator ${growth > 0 ? 'positive' : growth < 0 ? 'negative' : 'neutral'}`;
        }
    }

    updateCharts(data) {
        // Update registration chart
        if (this.charts.registration) {
            this.updateChart(this.charts.registration, data.registrations, 'date', 'count');
        }

        // Update revenue chart
        if (this.charts.revenue) {
            this.updateChart(this.charts.revenue, data.revenues, 'date', 'amount');
        }

        // Update level distribution
        if (this.charts.levels) {
            this.updatePieChart(this.charts.levels, data.levels, 'level_range', 'count');
        }
    }

    updateChart(chart, data, labelField, valueField) {
        const labels = data.map(item => {
            const date = new Date(item[labelField]);
            return date.toLocaleDateString('vi-VN', { month: 'short', day: 'numeric' });
        });
        const values = data.map(item => item[valueField]);

        chart.data.labels = labels;
        chart.data.datasets[0].data = values;
        chart.update('none'); // No animation for better performance
    }

    updatePieChart(chart, data, labelField, valueField) {
        const labels = data.map(item => `Level ${item[labelField]}`);
        const values = data.map(item => item[valueField]);

        chart.data.labels = labels;
        chart.data.datasets[0].data = values;
        chart.update('none');
    }

    updateTopPerformers(data) {
        this.updateTopList('top-spenders', data.spenders, (item) => ({
            name: item.username,
            value: this.formatCurrency(item.total_spent),
            detail: `${item.transaction_count} giao dịch`
        }));

        this.updateTopList('top-characters', data.characters, (item) => ({
            name: item.rname,
            value: `Lv.${item.level}`,
            detail: `${item.username} - Server ${item.serverid}`
        }));
    }

    updateTopList(containerId, data, formatter) {
        const container = document.getElementById(containerId);
        if (!container) return;

        const html = data.map(item => {
            const formatted = formatter(item);
            return `
                <div class="performer-item">
                    <div class="performer-info">
                        <div class="performer-name">${formatted.name}</div>
                        <div class="performer-detail">${formatted.detail}</div>
                    </div>
                    <div class="performer-value">${formatted.value}</div>
                </div>
            `;
        }).join('');

        container.innerHTML = html;
    }

    toggleAutoRefresh(enabled) {
        this.autoRefresh = enabled;
        
        if (enabled) {
            this.refreshInterval = setInterval(() => {
                this.refreshData();
            }, 30000); // Refresh every 30 seconds
            this.showNotification('Tự động cập nhật đã bật', 'info');
        } else {
            if (this.refreshInterval) {
                clearInterval(this.refreshInterval);
                this.refreshInterval = null;
            }
            this.showNotification('Tự động cập nhật đã tắt', 'info');
        }
    }

    async clearCache() {
        try {
            const response = await fetch('/admin/analytics/clear-cache', {
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            });

            const result = await response.json();
            
            if (result.success) {
                this.showNotification('Cache đã được xóa', 'success');
                this.refreshData();
            } else {
                throw new Error('Failed to clear cache');
            }
        } catch (error) {
            this.showNotification('Lỗi khi xóa cache', 'error');
        }
    }

    initializeCharts() {
        // Store chart instances for updates
        const registrationCanvas = document.getElementById('registrationChart');
        const revenueCanvas = document.getElementById('revenueChart');
        const levelCanvas = document.getElementById('levelChart');

        if (registrationCanvas) {
            this.charts.registration = Chart.getChart(registrationCanvas);
        }
        if (revenueCanvas) {
            this.charts.revenue = Chart.getChart(revenueCanvas);
        }
        if (levelCanvas) {
            this.charts.levels = Chart.getChart(levelCanvas);
        }
    }

    startPerformanceMonitoring() {
        // Monitor page load performance
        if (window.performance && window.performance.timing) {
            const loadTime = window.performance.timing.loadEventEnd - window.performance.timing.navigationStart;

            if (loadTime > 3000) {
                this.showNotification('Trang tải chậm. Hãy xem xét tối ưu hóa.', 'warning');
            }
        }
    }

    showLoading(show) {
        const loader = document.getElementById('loading-indicator');
        if (loader) {
            loader.style.display = show ? 'block' : 'none';
        }
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        
        // Add to page
        document.body.appendChild(notification);
        
        // Auto remove after 3 seconds
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }

    formatNumber(num) {
        return new Intl.NumberFormat('vi-VN').format(num);
    }

    formatCurrency(amount) {
        return new Intl.NumberFormat('vi-VN', {
            style: 'currency',
            currency: 'VND'
        }).format(amount);
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.analyticsDashboard = new AnalyticsDashboard();
});
