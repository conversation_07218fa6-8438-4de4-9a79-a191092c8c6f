<?php

return [
    // Header Navigation
    'dashboard' => 'Dashboard',
    'recharge' => 'Recharge Diamonds',
    'withdraw' => 'Withdraw Diamonds',
    'giftcode' => 'Giftcode',
    'profile' => 'Profile',
    'monthly_card' => 'Monthly Card',
    'login' => 'Login',
    'register' => 'Register',
    'logout' => 'Logout',
    'transaction_history' => 'Transaction History',

    // Language Switcher
    'language' => 'Language',
    'vietnamese' => 'Tiếng Việt',
    'english' => 'English',

    // Common
    'home' => 'Home',
    'welcome' => 'Welcome',
    'portal' => 'MU Game Portal',
    'game_portal' => '🎮 MU Game Portal',

    // Messages
    'login_required' => 'Please login to continue.',
    'access_denied' => 'Access denied.',
    'success' => 'Success',
    'error' => 'Error',
    'warning' => 'Warning',
    'info' => 'Information',

    // Buttons
    'submit' => 'Submit',
    'cancel' => 'Cancel',
    'save' => 'Save',
    'edit' => 'Edit',
    'delete' => 'Delete',
    'view' => 'View',
    'back' => 'Back',
    'next' => 'Next',
    'previous' => 'Previous',
    'confirm' => 'Confirm',

    // Forms
    'username' => 'Username',
    'password' => 'Password',
    'email' => 'Email',
    'phone' => 'Phone',
    'amount' => 'Amount',
    'description' => 'Description',
    'status' => 'Status',
    'date' => 'Date',
    'time' => 'Time',

    // Status
    'active' => 'Active',
    'inactive' => 'Inactive',
    'pending' => 'Pending',
    'completed' => 'Completed',
    'failed' => 'Failed',
    'cancelled' => 'Cancelled',
    'approved' => 'Approved',
    'rejected' => 'Rejected',

    // Game related
    'character' => 'Character',
    'server' => 'Server',
    'level' => 'Level',
    'class' => 'Class',
    'guild' => 'Guild',
    'diamonds' => 'Diamonds',
    'coins' => 'Coins',
    'items' => 'Items',

    // Dashboard
    'welcome_message' => 'Welcome, :username!',
    'welcome_description' => 'Welcome to MU Game Portal. Manage your account and transactions here.',
    'current_diamonds' => 'Current Diamonds',
    'account_status' => 'Status',
    'total_recharged' => 'Total Recharged',
    'last_login' => 'Last Login',
    'no_info' => 'No information',
    'quick_actions' => 'Quick Actions',
    'recharge_diamonds' => 'Recharge Diamonds',
    'withdraw_diamonds' => 'Withdraw Diamonds',
    'enter_giftcode' => 'Enter Giftcode',
    'history' => 'History',
    'diamond_info' => 'Diamond Information',
    'total_recharged_amount' => 'Total Recharged',
    'transactions' => 'Transactions',
    'game_stats' => 'Game & Giftcode Statistics',
    'giftcodes_used' => 'Giftcodes Used',
    'characters' => 'Characters',
    'highest_level' => 'Highest Level',
    'coins_withdrawn' => 'Coins Withdrawn',
    'account_details' => 'Account Details',
    'basic_info' => 'Basic Information',
    'username_label' => 'Username',
    'email_label' => 'Email',
    'status_label' => 'Status',
    'time_info' => 'Time Information',
    'created_date' => 'Created Date',
    'last_login_time' => 'Last Login',
    'not_updated' => 'Not updated',
    'undefined' => 'Undefined',

    // Auth pages
    'login_title' => 'Login - MU Game Portal',
    'login_subtitle' => 'Login to access your account',
    'username_or_email' => 'Username or Email',
    'username_placeholder' => 'Enter username or email',
    'password_placeholder' => 'Enter password',
    'remember_login' => 'Remember login',
    'forgot_password' => 'Forgot password?',
    'login_button' => 'Login',
    'or' => 'or',
    'no_account' => 'Don\'t have an account?',
    'register_now' => 'Register now',

    // Recharge page
    'recharge_title' => 'Recharge Diamonds - MU Game Portal',
    'recharge_description' => 'Recharge diamonds to buy items and upgrade characters in game',
    'select_package' => 'Select Package',
    'payment_method' => 'Payment Method',
    'bank_transfer' => 'Bank Transfer',
    'momo' => 'MoMo Wallet',
    'zalo_pay' => 'ZaloPay',
    'package_price' => 'Package Price',
    'diamonds_received' => 'Diamonds Received',
    'bonus' => 'Bonus',
    'total_diamonds' => 'Total Diamonds',
    'proceed_payment' => 'Proceed Payment',
    'payment_info' => 'Payment Information',
    'transfer_content' => 'Transfer Content',
    'account_number' => 'Account Number',
    'account_name' => 'Account Name',
    'bank_name' => 'Bank Name',
    'amount_to_transfer' => 'Amount to Transfer',

    // Withdraw page
    'withdraw_title' => 'Withdraw Diamonds - MU Game Portal',
    'withdraw_description' => 'Transfer diamonds from web to game for use',
    'select_character' => 'Select Character',
    'character_name' => 'Character Name',
    'character_level' => 'Level',
    'character_class' => 'Class',
    'withdraw_amount' => 'Diamonds to Withdraw',
    'available_diamonds' => 'Available Diamonds',
    'conversion_rate' => 'Conversion Rate',
    'game_coins_received' => 'Game Coins Received',
    'withdraw_button' => 'Withdraw Diamonds',
    'min_withdraw' => 'Minimum Amount',
    'max_withdraw' => 'Maximum Amount',

    // Giftcode page
    'giftcode_title' => 'Giftcode - MU Game Portal',
    'giftcode_description' => 'Enter giftcode to receive free rewards',
    'enter_giftcode_placeholder' => 'Enter giftcode',
    'use_giftcode' => 'Use Giftcode',
    'giftcode_history' => 'Giftcode History',
    'giftcode_code' => 'Giftcode',
    'used_date' => 'Used Date',
    'rewards' => 'Rewards',
    'no_giftcode_used' => 'No giftcode used yet',

    // Profile page
    'profile_title' => 'Profile - MU Game Portal',
    'profile_description' => 'Manage your personal account information',
    'account_info' => 'Account Information',
    'personal_info' => 'Personal Information',
    'security' => 'Security',
    'change_password' => 'Change Password',
    'current_password' => 'Current Password',
    'new_password' => 'New Password',
    'confirm_password' => 'Confirm Password',
    'update_profile' => 'Update Profile',
    'phone_number' => 'Phone Number',
    'full_name' => 'Full Name',
    'date_of_birth' => 'Date of Birth',
    'gender' => 'Gender',
    'male' => 'Male',
    'female' => 'Female',
    'other' => 'Other',
    'address' => 'Address',

    // Common form elements
    'required_field' => 'Required field',
    'optional' => 'Optional',
    'select_option' => 'Select an option',
    'loading' => 'Loading...',
    'processing' => 'Processing...',
    'please_wait' => 'Please wait...',
    'in_game' => 'in game',
    'website' => 'Website',
    'game' => 'Game',
    'recent_transactions' => 'Recent Transactions',
    'transaction_id' => 'Transaction ID',
    'transaction_method' => 'Method',
    'amount_vnd' => 'Amount',
    'transaction_time' => 'Time',
    'view_all_transactions' => 'View All Transactions',
    'no_transactions' => 'No transactions yet',
    'bank_transfer_method' => 'Bank Transfer',
    'card_method' => 'Card',
    'processing_status' => 'Processing',
    'success_status' => 'Success',
    'rejected_status' => 'Rejected',
    'undefined_status' => 'Undefined',
    'processing_time' => 'Processing Time',
    'minutes' => 'minutes',
    'fee' => 'Fee',
    'free' => 'Free',
    'minimum' => 'Minimum',
    'maximum' => 'Maximum',
    'enter_amount' => 'Enter amount to recharge',
    'upload_proof' => 'Upload payment proof',
    'upload_proof_desc' => 'Upload transaction screenshot for faster processing',
    'send_request' => 'Send transfer request',
    'bank_info' => 'Bank Information',
    'scan_qr' => 'Scan QR Code',
    'copy_info' => 'Copy Information',
    'copied' => 'Copied!',
    'important_note' => 'Important Note',
    'copy_failed' => 'Cannot copy. Please copy manually.',

    // Withdraw specific
    'withdraw_info' => 'Withdraw Information',
    'limit_per_transaction' => 'Limit per transaction',
    'remaining_today' => 'Remaining today',
    'withdraw_to_game' => 'Withdraw coins to game account',
    'withdraw_guide' => 'Withdraw Guide',
    'select_character_receive' => 'Select character to receive coins',
    'coin_transfer_instant' => 'Coins will be transferred instantly to game account',
    'daily_limit' => 'Daily limit',
    'select_character_placeholder' => '-- Select character --',
    'coin_transfer_note' => 'Coins will be transferred to selected character',
    'withdraw_history' => 'Recent withdraw history',
    'withdraw_statistics' => 'Withdraw Statistics',
    'total_transactions' => 'Total transactions',
    'total_withdrawn' => 'Total withdrawn',
    'today' => 'Today',
    'no_withdraw_transactions' => 'No withdraw transactions yet',

    // Giftcode specific
    'giftcode_guide' => 'Giftcode Usage Guide',
    'giftcode_rule_1' => 'Enter giftcode exactly (case sensitive)',
    'giftcode_rule_2' => 'Each giftcode can only be used once',
    'giftcode_rule_3' => 'Check expiration date before entering',
    'giftcode_rule_4' => 'Rewards will be sent to account immediately',
    'select_character_reward' => 'Select character to receive reward',
    'reward_sent_to_character' => 'Rewards will be sent to this character\'s mailbox',
    'enter_giftcode_code' => 'Enter code',
    'giftcode_length_note' => 'Giftcode usually has 8-20 characters',
    'need_link_account' => 'Need to link game account to use giftcode',
    'link_now' => 'Link now',
    'active_giftcodes' => 'Active Giftcodes',
    'loading_giftcodes' => 'Loading giftcode list...',
    'giftcode_usage_history' => 'Usage History',
    'loading_history' => 'Loading history...',
    'need_link_to_view_history' => 'Need to link game account to view history',
    'giftcode_types' => 'Giftcode Types',
    'event_code' => 'Event Code',
    'event_code_desc' => 'Giftcode from special events',
    'vip_code' => 'VIP Code',
    'vip_code_desc' => 'Giftcode for VIP members',
    'public_code' => 'Public Code',
    'public_code_desc' => 'Public giftcode for everyone',
    'giftcode_tips' => 'Giftcode Tips',
    'follow_new_giftcodes' => 'Follow New Giftcodes',
    'use_effectively' => 'Use Effectively',
    'no_characters' => 'No characters',
    'member_since' => 'Member since',
    'link_game_account' => 'Link Game Account',
    'linked_successfully' => 'Linked Successfully',
    'game_account_linked' => 'Your game account has been linked',
    'game_username' => 'Game Username',
    'current_coins' => 'Current Coins',
    'total_recharged_profile' => 'Total Recharged',
    'unlink_account' => 'Unlink Account',
    'not_linked_game' => 'Game account not linked',
    'link_to_use_features' => 'Link to use full features',
    'game_account_name' => 'Game Account Name',
    'enter_game_username' => 'Enter your game account name',
    'enter_exact_username' => 'Enter exact game account name to link',
    'link_account_button' => 'Link Account',
    'account_security' => 'Account Security',
    'update_password' => 'Update Password',
    'update_info' => 'Update Information',
    'enter_phone' => 'Enter phone number',
    'update_info_button' => 'Update Information',
    'account_statistics' => 'Account Statistics',
    'danger_zone' => 'Danger Zone',
    'delete_account' => 'Delete Account',
    'delete_account_warning' => 'Permanently delete account and all data. This action cannot be undone.',
    'delete_account_button' => 'Delete Account',

    // Important notes and tips
    'check_character_before_withdraw' => 'Check character carefully before withdrawing',
    'coin_transfer_instant_irreversible' => 'Coins will be transferred instantly and cannot be reversed',
    'only_withdraw_to_own_character' => 'Only withdraw coins to your own character',
    'contact_admin_if_issues' => 'Contact admin if there are transaction issues',

    // Giftcode tips details
    'follow_facebook_fanpage' => 'Follow official Facebook Fanpage',
    'join_discord_telegram' => 'Join Discord/Telegram group',
    'subscribe_email_notifications' => 'Subscribe to email notifications',
    'check_website_regularly' => 'Check website regularly',
    'enter_giftcode_immediately' => 'Enter giftcode immediately when available',
    'check_expiration_date' => 'Check expiration date',
    'ensure_account_linked' => 'Ensure account is linked',
    'note_usage_conditions' => 'Note usage conditions',

    // Monthly card page
    'monthly_card_title' => 'Monthly Card - MU Game Portal',
    'monthly_card_description' => 'Purchase monthly card to receive special daily benefits',
    'monthly_card_benefits' => 'Monthly Card Benefits',
    'daily_rewards' => 'Daily Rewards',
    'exclusive_items' => 'Exclusive Items',
    'bonus_experience' => 'Bonus Experience',
    'special_privileges' => 'Special Privileges',
    'purchase_monthly_card' => 'Purchase Monthly Card',
    'monthly_card_price' => 'Monthly Card Price',
    'monthly_card_duration' => 'Duration',
    'days' => 'days',
    'activate_for_character' => 'Activate for character',
    'monthly_card_status' => 'Monthly Card Status',
    'not_activated' => 'Not activated',
    'activated' => 'Activated',
    'expired' => 'Expired',
    'remaining_days' => 'Remaining',

    // Transaction history page
    'transaction_history_title' => 'Transaction History - MU Game Portal',
    'transaction_history_description' => 'View all your diamond recharge transactions',
    'filter_transactions' => 'Filter Transactions',
    'all_transactions' => 'All Transactions',
    'filter_by_status' => 'Filter by Status',
    'filter_by_method' => 'Filter by Method',
    'filter_by_date' => 'Filter by Date',
    'from_date' => 'From Date',
    'to_date' => 'To Date',
    'apply_filter' => 'Apply Filter',
    'clear_filter' => 'Clear Filter',
    'export_transactions' => 'Export Data',
    'transaction_details' => 'Transaction Details',
    'payment_proof' => 'Payment Proof',
    'admin_note' => 'Admin Note',
    'no_admin_note' => 'No admin note',
    'view_proof' => 'View Image',
    'no_proof_uploaded' => 'No proof uploaded',
    'total_amount_filtered' => 'Total Amount (Filtered)',
    'total_coins_filtered' => 'Total Coins (Filtered)',
    'showing_results' => 'Showing results',
    'of_total' => 'of total',
    'results' => 'results',
    'claimed' => 'Claimed',
    'claim_reward' => 'Claim Reward',
    'cancel_card' => 'Cancel Card',
    'purchase_history' => 'Purchase History',
    'package_name' => 'Package',
    'purchase_date' => 'Purchase Date',
    'duration' => 'Duration',
    'select_character_activate' => 'Select character to activate monthly card',
    'card_will_activate_for_character' => 'Monthly card will be activated for selected character',
    'loading_characters' => 'Loading character list...',
    'confirm_purchase' => 'Confirm Purchase',
    'no_characters_found' => 'No characters found',
    'create_character_first' => 'You need to create character in game first',
    'error_loading_characters' => 'Error loading character list',
    'select_character_label' => 'Select Character',
    'buy_now' => 'Buy Now',
    'save_amount' => 'Save',
    'total_value' => 'Total Value',
    'unique_package' => 'UNIQUE PACKAGE',
    'daily_diamonds' => 'diamonds daily',
    'bonus_items_on_purchase' => 'Special bonus items on purchase',
    'daily_premium_items' => 'Daily premium items',
    'exclusive_vip_privileges' => 'Exclusive VIP privileges',
    'refresh_page' => 'Refresh',
    'export_function_developing' => 'Export history function is under development!',
    'status_meanings' => 'Status Meanings',
    'processing_times' => 'Processing Times',
    'card_processing_time' => 'Card: 5-10 minutes (automatic)',
    'bank_processing_time' => 'Bank transfer: 10-30 minutes (manual)',
    'paypal_processing_time' => 'PayPal: 5-15 minutes (automatic)',
    'working_hours' => 'Working hours: 8:00 - 22:00 daily',
    'transaction_waiting_admin' => 'Transaction waiting for admin processing',
    'admin_checking_processing' => 'Admin is checking and processing',
    'coins_added_to_account' => 'Coins have been added to account',
    'transaction_rejected_see_note' => 'Transaction rejected (see note)',
    'contact_admin_if_over_1hour' => 'If transaction is not processed after 1 hour, please contact admin.',
    'no_transactions_found' => 'No transactions found',
    'no_matching_filter' => 'No transactions match the filter.',
    'no_transactions_yet' => 'You have not made any transactions yet.',
    'first_recharge' => 'First recharge',
    'recharge_more' => 'Recharge more',
    'export_history' => 'Export History',
    'actions' => 'Actions',
    'view_details' => 'View Details',

    // Transaction detail page
    'transaction_detail_title' => 'Transaction Details - MU Game Portal',
    'transaction_detail_header' => 'Recharge Transaction Details',
    'transaction_detail_description' => 'Detailed information about transaction #:id',
    'waiting_for_processing' => 'Waiting for processing',
    'phone_card' => 'Phone card',
    'manual_recharge_admin' => 'Manual recharge (Admin)',
    'creation_time' => 'Creation time',
    'completion_time' => 'Completion time',
    'team_founder' => 'Team/Founder',
    'customer' => 'Customer',
    'bank_transfer_info' => 'Bank transfer information',
    'processing_info' => 'Processing information',
    'processing_admin' => 'Processing admin',
    'back_to_history' => 'Back to history',
    'cancel_transaction' => 'Cancel transaction',
    'recharge_more_coins' => 'Recharge more coins',
    'print_receipt' => 'Print receipt',
    'copied_to_clipboard' => 'Copied to clipboard!',
];
