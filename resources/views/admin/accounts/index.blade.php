@extends('layouts.admin')

@section('title', '<PERSON><PERSON><PERSON><PERSON> lý t<PERSON> - MU Admin Panel')

@section('styles')
<style>
        .nav-links a.active {
            background: rgba(255, 255, 255, 0.2);
        }
        .page-header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(16px);
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 30px;
            margin-bottom: 30px;
            color: white;
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }
        .header-text {
            flex: 1;
        }
        .header-actions {
            flex: 0 0 auto;
        }
        .page-title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 10px;
        }
        .page-desc {
            opacity: 0.9;
        }
        .search-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(16px);
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 25px;
            margin-bottom: 30px;
        }
        .search-form {
            display: flex;
            gap: 15px;
            align-items: end;
        }
        .form-group {
            flex: 1;
            min-width: 0;
        }
        .form-group.search-type {
            flex: 0 0 200px;
        }
        .form-group.search-input {
            flex: 2;
        }
        .form-group.search-button {
            flex: 0 0 auto;
        }
        .form-group label {
            display: block;
            color: white;
            font-weight: 500;
            margin-bottom: 5px;
        }
        .form-control {
            width: 100%;
            padding: 12px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 14px;
        }
        .form-control::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }
        .form-control:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
        }
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        .btn-primary {
            background: linear-gradient(45deg, #3b82f6, #8b5cf6);
            color: white;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
        }
        .accounts-table {
            background: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(16px);
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
            background: transparent;
        }
        .table th,
        .table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            background: transparent;
        }
        .table th {
            background: rgba(0, 0, 0, 0.4);
            color: #ffffff;
            font-weight: 600;
        }
        .table td {
            color: #ffffff;
            background: transparent;
        }
        .table tr:hover {
            background: rgba(255, 255, 255, 0.1) !important;
        }
        .table tr:hover td {
            background: transparent !important;
        }
        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }
        .status-active {
            background: rgba(16, 185, 129, 0.2);
            color: #10b981;
            border: 1px solid rgba(16, 185, 129, 0.3);
        }
        .status-banned {
            background: rgba(239, 68, 68, 0.2);
            color: #ef4444;
            border: 1px solid rgba(239, 68, 68, 0.3);
        }
        .vip-badge {
            background: linear-gradient(45deg, #fbbf24, #f59e0b);
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 11px;
            font-weight: 600;
        }
        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }
        .btn-info {
            background: linear-gradient(45deg, #06b6d4, #0891b2);
            color: white;
        }
        .btn-secondary {
            background: linear-gradient(45deg, #6b7280, #4b5563);
            color: white;
        }
        .btn-secondary:hover {
            transform: translateY(-2px);
        }
        .btn-warning {
            background: linear-gradient(45deg, #f59e0b, #d97706);
            color: white;
        }
        .btn-warning:hover {
            transform: translateY(-2px);
        }
        .no-results {
            text-align: center;
            padding: 40px;
            color: white;
            opacity: 0.8;
        }
        .success-message {
            background: rgba(16, 185, 129, 0.2);
            border: 1px solid rgba(16, 185, 129, 0.3);
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }
        .alert alert-error {
            background: rgba(239, 68, 68, 0.2);
            border: 1px solid rgba(239, 68, 68, 0.3);
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }

        /* Enhanced Responsive Design */
        .table-responsive {
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
            max-width: 100vw;
        }

        /* Force prevent overflow */
        * {
            box-sizing: border-box;
        }

        .account-row td {
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .table {
            min-width: 600px;
            width: 100%;
            table-layout: fixed;
        }

        .table td, .table th {
            word-wrap: break-word;
            overflow-wrap: break-word;
        }

        .table td:nth-child(1) { width: 60px; }  /* ID */
        .table td:nth-child(2) { width: 180px; } /* Username + mobile info */
        .table td:nth-child(3) { width: 150px; } /* Email */
        .table td:nth-child(4) { width: 100px; } /* Status */
        .table td:nth-child(5) { width: 120px; } /* Web coins */
        .table td:nth-child(6) { width: 120px; } /* Game coins */
        .table td:nth-child(7) { width: 80px; }  /* Characters */
        .table td:nth-child(8) { width: 100px; } /* Date */
        .table td:nth-child(9) { width: 80px; }  /* Actions */

        .account-info {
            min-width: 0;
            max-width: 200px;
            word-wrap: break-word;
            overflow-wrap: break-word;
        }

        .username {
            max-width: 150px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            display: inline-block;
        }

        .mobile-info {
            max-width: 100%;
            overflow: hidden;
        }

        .mobile-info small {
            max-width: 150px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            display: block;
        }

        .account-info .mobile-info {
            margin-top: 5px;
        }

        .mobile-stats {
            margin-top: 5px;
            display: flex;
            flex-wrap: wrap;
            gap: 3px;
            max-width: 100%;
            overflow: hidden;
            width: 100%;
        }

        .mobile-stats .badge {
            margin-right: 0;
            padding: 2px 6px;
            font-size: 10px;
            border-radius: 10px;
            white-space: nowrap;
            flex-shrink: 1;
            max-width: 60px;
            overflow: hidden;
            text-overflow: ellipsis;
            min-width: 0;
            cursor: help;
            transition: transform 0.2s ease;
        }

        .mobile-stats .badge:hover {
            transform: scale(1.05);
        }

        .badge-info {
            background: linear-gradient(45deg, #06b6d4, #0891b2);
            color: white;
        }

        .badge-secondary {
            background: linear-gradient(45deg, #6b7280, #4b5563);
            color: white;
        }

        .badge-success {
            background: linear-gradient(45deg, #10b981, #059669);
            color: white;
        }

        .badge-danger {
            background: linear-gradient(45deg, #ef4444, #dc2626);
            color: white;
        }

        .text-muted {
            opacity: 0.7;
        }

        .account-id {
            font-weight: 600;
            color: #3b82f6;
        }

        .username {
            color: #ffffff !important;
            font-size: 14px;
            font-weight: 600;
        }

        .email {
            color: rgba(255, 255, 255, 0.8) !important;
            font-size: 13px;
        }

        .money-amount, .character-count {
            font-weight: 600;
            color: #10b981 !important;
        }

        .create-date {
            color: rgba(255, 255, 255, 0.9) !important;
        }

        .mobile-info small {
            color: rgba(255, 255, 255, 0.7) !important;
        }

        @media (max-width: 768px) {
            .search-form {
                flex-direction: column;
                align-items: stretch;
            }
            .form-group.search-type,
            .form-group.search-input,
            .form-group.search-button {
                flex: none;
                width: 100%;
            }
            .form-group.search-button {
                margin-top: 10px;
            }
            .page-header {
                padding: 20px;
            }
            .page-title {
                font-size: 24px;
            }
            .search-section {
                padding: 20px;
            }
            .table th,
            .table td {
                padding: 10px 8px;
            }
            .table {
                min-width: 500px;
            }
            .table td:nth-child(2) {
                width: 160px;
                max-width: 160px;
            }
            .username {
                max-width: 120px;
            }
            .mobile-info small {
                max-width: 120px;
            }
        }

        @media (max-width: 576px) {
            .page-header {
                padding: 15px;
            }
            .search-section {
                padding: 15px;
            }
            .btn {
                padding: 10px 16px;
                font-size: 13px;
            }
            .table th,
            .table td {
                padding: 8px 6px;
            }
            .username {
                font-size: 13px;
            }
            .mobile-info small {
                font-size: 11px;
            }
            .mobile-stats .badge {
                font-size: 9px;
                padding: 1px 4px;
            }
            .account-info {
                max-width: 140px;
                overflow: hidden;
            }
            .mobile-info {
                max-width: 140px;
            }
            .username {
                max-width: 100px;
                font-size: 12px;
            }
            .mobile-info small {
                max-width: 100px;
                font-size: 10px;
            }
            .table {
                min-width: 400px;
            }
            .table td:nth-child(2) {
                width: 140px;
                max-width: 140px;
            }
        }

        /* Loading indicator */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            display: none !important;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            backdrop-filter: blur(4px);
        }
        .loading-overlay.show {
            display: flex !important;
        }
        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 5px solid rgba(255, 255, 255, 0.3);
            border-top: 5px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
</style>
@endsection

@section('content')
    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner"></div>
    </div>

    <!-- Immediate script to hide loading -->
    <script>
        // Immediately hide loading overlay when page starts loading
        (function() {
            const overlay = document.getElementById('loadingOverlay');
            if (overlay) {
                overlay.classList.remove('show');
            }
        })();
    </script>
        <!-- Success/Error Messages -->
        @if(session('success'))
            <div class="success-message">
                ✅ {{ session('success') }}
            </div>
        @endif

        @if($errors->any())
            <div class="alert alert-error">
                @foreach($errors->all() as $error)
                    ❌ {{ $error }}
                @endforeach
            </div>
        @endif

        <!-- Page Header -->
        <div class="page-header">
            <div class="header-content">
                <div class="header-text">
                    <h1 class="page-title">👤 Quản lý tài khoản</h1>
                    <p class="page-desc">Tìm kiếm, xem thông tin và quản lý tài khoản người chơi</p>
                </div>
                <div class="header-actions">
                    <button onclick="clearGameCache()" class="btn btn-warning btn-sm" style="margin-right: 10px;">
                        🗑️ Clear Cache
                    </button>
                    <button onclick="testConnections()" class="btn btn-secondary btn-sm">
                        🔧 Test kết nối DB
                    </button>
                </div>
            </div>
        </div>

        <!-- Search Section -->
        <div class="search-section">
            <form class="search-form" method="GET">
                <div class="form-group search-type">
                    <label>Loại tìm kiếm</label>
                    <select name="search_type" class="form-control">
                        <option value="username" {{ $searchType == 'username' ? 'selected' : '' }}>Tên tài khoản</option>
                        <option value="character_name" {{ $searchType == 'character_name' ? 'selected' : '' }}>Tên nhân vật</option>
                        <option value="email" {{ $searchType == 'email' ? 'selected' : '' }}>Email</option>
                        <option value="phone" {{ $searchType == 'phone' ? 'selected' : '' }}>Số điện thoại</option>
                        <option value="full_name" {{ $searchType == 'full_name' ? 'selected' : '' }}>Họ và tên</option>
                    </select>
                </div>
                <div class="form-group search-input">
                    <label>Từ khóa tìm kiếm</label>
                    <input type="text" name="search" class="form-control" placeholder="Nhập từ khóa..." value="{{ $search }}">
                </div>
                <div class="form-group search-button">
                    <button type="submit" class="btn btn-primary">🔍 Tìm kiếm</button>
                </div>
            </form>
        </div>

        <!-- Accounts Table -->
        <div class="accounts-table">
            @if(count($accounts) > 0)
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th class="text-center">ID</th>
                                <th>Tên đăng nhập</th>
                                <th class="d-none d-md-table-cell">Email</th>
                                <th class="text-center d-none d-md-table-cell">Trạng thái</th>
                                <th class="text-center d-none d-lg-table-cell">Kim cương web</th>
                                <th class="text-center d-none d-lg-table-cell">Kim cương game</th>
                                <th class="text-center d-none d-lg-table-cell">Nhân vật</th>
                                <th class="text-center d-none d-xl-table-cell">Đăng ký</th>
                                <th class="text-center">Thao tác</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($accounts as $account)
                                <tr class="account-row" data-account-id="{{ $account->ID }}">
                                    <td class="text-center">
                                        <span class="account-id">#{{ $account->ID }}</span>
                                    </td>
                                    <td>
                                        <div class="account-info">
                                            <strong class="username">{{ $account->UserName }}</strong>
                                            <div class="mobile-info d-md-none">
                                                <small class="text-muted">
                                                    {{ $account->Email ?: 'Chưa có email' }}
                                                </small>
                                                <div class="mobile-stats">
                                                    <span class="badge {{ $account->Status == 1 ? 'badge-success' : 'badge-danger' }}"
                                                          title="{{ $account->Status == 1 ? 'Hoạt động' : 'Bị khóa' }}">
                                                        {{ $account->Status == 1 ? '✅' : '❌' }}
                                                    </span>
                                                    <span class="badge badge-info" title="Kim cương web">{{ number_format($account->web_coins ?? 0) }} 💎W</span>
                                                    <span class="badge badge-info" title="Kim cương game">{{ number_format($account->total_money ?? 0) }} 💎G</span>
                                                    <span class="badge badge-secondary" title="Số nhân vật">{{ $account->characters_count ?? 0 }} NV</span>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="d-none d-md-table-cell">
                                        <span class="email">{{ $account->Email ?: 'Chưa có email' }}</span>
                                    </td>
                                    <td class="text-center d-none d-md-table-cell">
                                        <span class="status-badge {{ $account->Status == 1 ? 'status-active' : 'status-banned' }}">
                                            {{ $account->Status == 1 ? 'Hoạt động' : 'Bị khóa' }}
                                        </span>
                                    </td>
                                    <td class="text-center d-none d-lg-table-cell">
                                        <span class="money-amount">{{ number_format($account->web_coins ?? 0) }}</span>
                                        <small class="text-muted d-block">Kim cương web</small>
                                    </td>
                                    <td class="text-center d-none d-lg-table-cell">
                                        <span class="money-amount">{{ number_format($account->total_money ?? 0) }}</span>
                                        <small class="text-muted d-block">Kim cương game</small>
                                    </td>
                                    <td class="text-center d-none d-lg-table-cell">
                                        <span class="character-count">{{ $account->characters_count ?? 0 }}</span>
                                        <small class="text-muted d-block">nhân vật</small>
                                    </td>
                                    <td class="text-center d-none d-xl-table-cell">
                                        <span class="create-date">
                                            {{ $account->CreateTime ? \Carbon\Carbon::parse($account->CreateTime)->format('d/m/Y') : 'N/A' }}
                                        </span>
                                        <small class="text-muted d-block">
                                            {{ $account->CreateTime ? \Carbon\Carbon::parse($account->CreateTime)->format('H:i') : '' }}
                                        </small>
                                    </td>
                                    <td class="text-center">
                                        <div class="action-buttons">
                                            <a href="/admin/accounts/{{ $account->ID }}"
                                               class="btn btn-info btn-sm"
                                               title="Xem chi tiết">
                                                👁️ <span class="d-none d-sm-inline">Xem</span>
                                            </a>

                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {{ $accounts->appends(request()->query())->links('pagination.custom') }}
            @else
                <div class="no-results">
                    <h3>🔍 Không tìm thấy tài khoản nào</h3>
                    <p>Thử thay đổi từ khóa tìm kiếm hoặc loại tìm kiếm</p>
                </div>
            @endif
        </div>

    <script>
        // Show loading overlay when navigating to account details
        document.addEventListener('DOMContentLoaded', function() {
            const accountLinks = document.querySelectorAll('a[href*="/admin/accounts/"]');
            const loadingOverlay = document.getElementById('loadingOverlay');

            // Functions to show/hide loading
            function showLoading() {
                loadingOverlay.classList.add('show');
            }

            function hideLoading() {
                loadingOverlay.classList.remove('show');
            }

            // Always hide loading overlay first
            hideLoading();

            accountLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    // Only show loading for detail pages, not current page
                    if (this.href.includes('/admin/accounts/') && !this.href.includes('/admin/accounts?')) {
                        showLoading();
                    }
                });
            });

            // Show loading on form submit
            const searchForm = document.querySelector('.search-form');
            if (searchForm) {
                searchForm.addEventListener('submit', function() {
                    showLoading();
                });
            }

            // Hide loading on various events
            window.addEventListener('load', hideLoading);
            window.addEventListener('pageshow', hideLoading);
            window.addEventListener('focus', hideLoading);
            document.addEventListener('visibilitychange', function() {
                if (!document.hidden) {
                    hideLoading();
                }
            });

            // Force hide loading after a timeout as fallback
            setTimeout(hideLoading, 500);

            // Hide loading when user navigates back
            window.addEventListener('popstate', hideLoading);
        });

        // Test database connections
        function testConnections() {
            const loadingOverlay = document.getElementById('loadingOverlay');
            loadingOverlay.classList.add('show');

            fetch('/admin/accounts/test-connections')
                .then(response => response.json())
                .then(data => {
                    loadingOverlay.classList.remove('show');

                    let message = 'Kết quả test kết nối:\n\n';

                    // Main database
                    message += '🗄️ Database chính (Website):\n';
                    message += `Status: ${data.main_db.status}\n`;
                    message += `Message: ${data.main_db.message}\n\n`;

                    // Game database
                    message += '🎮 Database game:\n';
                    message += `Status: ${data.game_db.status}\n`;
                    message += `Message: ${data.game_db.message}\n`;

                    alert(message);
                })
                .catch(error => {
                    loadingOverlay.classList.remove('show');
                    console.error('Error:', error);
                    alert('Lỗi khi test kết nối: ' + error.message);
                });
        }

        // Clear game cache
        function clearGameCache() {
            if (!confirm('Bạn có chắc chắn muốn xóa cache dữ liệu game? Điều này sẽ làm trang load chậm hơn lần đầu.')) {
                return;
            }

            const loadingOverlay = document.getElementById('loadingOverlay');
            loadingOverlay.classList.add('show');

            fetch('/admin/cache/clear-game', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                loadingOverlay.classList.remove('show');
                if (data.success) {
                    alert('✅ Đã xóa cache thành công! Trang sẽ được tải lại.');
                    location.reload();
                } else {
                    alert('❌ Lỗi: ' + data.message);
                }
            })
            .catch(error => {
                loadingOverlay.classList.remove('show');
                console.error('Error:', error);
                alert('Lỗi khi xóa cache: ' + error.message);
            });
        }
    </script>
@endsection
