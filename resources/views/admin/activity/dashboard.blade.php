@extends('layouts.admin')

@section('title', 'Admin Activity Dashboard - MU Admin Panel')

@section('head')
<link rel="stylesheet" href="{{ asset('css/admin-activity.css') }}">
<script src="{{ asset('js/admin-activity.js') }}" defer></script>
<meta name="csrf-token" content="{{ csrf_token() }}">
@endsection

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="page-header">
        <div>
            <h1 class="page-title">🔍 Admin Activity Dashboard</h1>
            <p class="page-subtitle">Theo dõi hoạt động admin và phát hiện xung đột thao tác</p>
        </div>
        <div style="display: flex; gap: 15px; flex-wrap: wrap;">
            <button onclick="refreshData()" class="btn btn-primary">🔄 Refresh</button>
            <button onclick="showConflicts()" class="btn btn-warning">⚠️ Conflicts ({{ $stats['pending_conflicts'] }})</button>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3 class="mb-0">{{ $stats['online_admins'] }}</h3>
                            <p class="mb-0">Admin Online</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3 class="mb-0">{{ $stats['pending_conflicts'] }}</h3>
                            <p class="mb-0">Pending Conflicts</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-exclamation-triangle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3 class="mb-0">{{ $stats['queued_operations'] }}</h3>
                            <p class="mb-0">Queued Operations</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3 class="mb-0">{{ $stats['active_locks'] }}</h3>
                            <p class="mb-0">Active Locks</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-lock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Online Admins -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">👥 Admin đang online</h5>
        </div>
        <div class="card-body">
            <div id="onlineAdmins">
                @if($onlineAdmins->count() > 0)
                    <div class="row">
                        @foreach($onlineAdmins as $admin)
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="card border-success">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="mb-1">{{ $admin->admin_username }}</h6>
                                            <small class="text-muted">{{ $admin->ip_address }}</small>
                                        </div>
                                        <div class="text-success">
                                            <i class="fas fa-circle"></i>
                                        </div>
                                    </div>
                                    <div class="mt-2">
                                        <small class="text-muted">Last activity: {{ \Carbon\Carbon::parse($admin->last_activity)->diffForHumans() }}</small>
                                    </div>
                                    @if(!empty($admin->current_actions))
                                        <div class="mt-2">
                                            <small class="badge badge-info">{{ count($admin->current_actions) }} actions</small>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center text-muted">
                        <i class="fas fa-user-slash fa-3x mb-3"></i>
                        <p>Không có admin nào đang online</p>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Pending Conflicts -->
    @if($pendingConflicts->count() > 0)
    <div class="card mb-4">
        <div class="card-header bg-warning">
            <h5 class="card-title mb-0 text-white">⚠️ Conflicts cần xử lý</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Resource</th>
                            <th>Operation</th>
                            <th>Admin 1</th>
                            <th>Admin 2</th>
                            <th>Detected At</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($pendingConflicts as $conflict)
                        <tr>
                            <td>{{ $conflict->resource_type }}: {{ $conflict->resource_id }}</td>
                            <td><span class="badge badge-warning">{{ $conflict->operation_type }}</span></td>
                            <td>{{ $conflict->admin1_username }}</td>
                            <td>{{ $conflict->admin2_username }}</td>
                            <td>{{ \Carbon\Carbon::parse($conflict->detected_at)->diffForHumans() }}</td>
                            <td>
                                <button onclick="resolveConflict({{ $conflict->id }}, 'auto_resolved')" class="btn btn-sm btn-success">Resolve</button>
                                <button onclick="resolveConflict({{ $conflict->id }}, 'ignored')" class="btn btn-sm btn-secondary">Ignore</button>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    @endif

    <!-- Queued Operations -->
    @if($queuedOperations->count() > 0)
    <div class="card">
        <div class="card-header bg-info">
            <h5 class="card-title mb-0 text-white">⏳ Operations trong queue</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Operation</th>
                            <th>Resource</th>
                            <th>Requested By</th>
                            <th>Status</th>
                            <th>Requested At</th>
                            <th>Retry Count</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($queuedOperations as $operation)
                        <tr>
                            <td><span class="badge badge-info">{{ $operation->operation_type }}</span></td>
                            <td>{{ $operation->resource_type }}: {{ $operation->resource_id }}</td>
                            <td>{{ $operation->requested_by_admin_username }}</td>
                            <td>
                                @if($operation->status === 'pending')
                                    <span class="badge badge-warning">Pending</span>
                                @elseif($operation->status === 'processing')
                                    <span class="badge badge-primary">Processing</span>
                                @elseif($operation->status === 'failed')
                                    <span class="badge badge-danger">Failed</span>
                                @endif
                            </td>
                            <td>{{ \Carbon\Carbon::parse($operation->requested_at)->diffForHumans() }}</td>
                            <td>{{ $operation->retry_count }}/{{ $operation->max_retries }}</td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    @endif
</div>

<script>
// Auto refresh every 30 seconds
setInterval(refreshData, 30000);

function refreshData() {
    location.reload();
}

function showConflicts() {
    // Implementation for showing conflicts modal
    alert('Conflicts modal - to be implemented');
}

function resolveConflict(conflictId, resolution) {
    if (!confirm('Are you sure you want to resolve this conflict?')) {
        return;
    }
    
    fetch(`/admin/activity/resolve-conflict/${conflictId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            resolution: resolution,
            notes: 'Resolved from dashboard'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Conflict resolved successfully');
            location.reload();
        } else {
            alert('Error resolving conflict: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error resolving conflict');
    });
}
</script>
@endsection
