@extends('layouts.admin')

@section('title', '<PERSON><PERSON><PERSON><PERSON> lý Admin Users - MU Admin Panel')

@section('styles')
<style>
    .page-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(16px);
        border-radius: 15px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        padding: 30px;
        margin-bottom: 30px;
        color: white;
    }
    .page-title {
        font-size: 28px;
        font-weight: 700;
        margin-bottom: 10px;
    }
    .page-desc {
        opacity: 0.9;
    }
    .search-section {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(16px);
        border-radius: 15px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        padding: 25px;
        margin-bottom: 30px;
    }
    .search-form {
        display: flex;
        gap: 15px;
        align-items: end;
    }
    .form-group {
        flex: 1;
        min-width: 0;
    }
    .form-group.search-type {
        flex: 0 0 200px;
    }
    .form-group.search-input {
        flex: 2;
    }
    .form-group.search-button {
        flex: 0 0 auto;
    }
    .form-group label {
        display: block;
        color: white;
        font-weight: 500;
        margin-bottom: 5px;
    }
    .form-control {
        width: 100%;
        padding: 12px;
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 8px;
        background: rgba(255, 255, 255, 0.1);
        color: white;
        font-size: 14px;
    }
    .form-control::placeholder {
        color: rgba(255, 255, 255, 0.7);
    }
    .form-control:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
    }
    .btn {
        padding: 12px 24px;
        border: none;
        border-radius: 8px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s;
        text-decoration: none;
        display: inline-block;
        text-align: center;
    }
    .btn-primary {
        background: linear-gradient(45deg, #3b82f6, #8b5cf6);
        color: white;
    }
    .btn-primary:hover {
        transform: translateY(-2px);
    }
    .btn-success {
        background: linear-gradient(45deg, #10b981, #059669);
        color: white;
    }
    .btn-success:hover {
        transform: translateY(-2px);
    }
    .admin-users-table {
        background: rgba(0, 0, 0, 0.3);
        backdrop-filter: blur(16px);
        border-radius: 15px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        overflow: hidden;
    }
    .table {
        width: 100%;
        border-collapse: collapse;
        background: transparent;
    }
    .table th,
    .table td {
        padding: 15px;
        text-align: left;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        background: transparent;
    }
    .table th {
        background: rgba(0, 0, 0, 0.4);
        color: #ffffff;
        font-weight: 600;
    }
    .table td {
        color: #ffffff !important;
        background: transparent;
    }
    .table td strong {
        color: #ffffff !important;
        font-weight: 600;
    }

    .admin-id {
        font-weight: 600;
        color: #3b82f6 !important;
    }

    .email, .full-name {
        color: rgba(255, 255, 255, 0.9) !important;
    }

    .last-login, .created-date {
        color: rgba(255, 255, 255, 0.9) !important;
    }

    .text-muted {
        color: rgba(255, 255, 255, 0.6) !important;
        font-size: 11px;
    }

    /* Utility classes for responsive */
    .text-center { text-align: center; }
    .d-block { display: block; }
    .d-none { display: none; }
    .d-inline { display: inline; }

    /* Bootstrap-like responsive utilities */
    @media (min-width: 576px) {
        .d-sm-none { display: none !important; }
        .d-sm-inline { display: inline !important; }
        .d-sm-block { display: block !important; }
        .d-sm-table-cell { display: table-cell !important; }
    }

    @media (min-width: 768px) {
        .d-md-none { display: none !important; }
        .d-md-inline { display: inline !important; }
        .d-md-block { display: block !important; }
        .d-md-table-cell { display: table-cell !important; }
    }

    @media (min-width: 992px) {
        .d-lg-none { display: none !important; }
        .d-lg-inline { display: inline !important; }
        .d-lg-block { display: block !important; }
        .d-lg-table-cell { display: table-cell !important; }
    }
    .table tbody tr {
        transition: all 0.2s ease;
    }
    .table tr:hover {
        background: rgba(255, 255, 255, 0.1) !important;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
    .table tr:hover td {
        background: transparent !important;
    }
    .status-badge {
        padding: 4px 8px;
        border-radius: 15px;
        font-size: 10px;
        font-weight: 600;
        text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        display: inline-block;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 100%;
        box-sizing: border-box;
    }
    .status-active {
        background: linear-gradient(45deg, #10b981, #059669);
        color: #ffffff;
        border: 1px solid rgba(16, 185, 129, 0.5);
        box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
    }
    .status-inactive {
        background: linear-gradient(45deg, #ef4444, #dc2626);
        color: #ffffff;
        border: 1px solid rgba(239, 68, 68, 0.5);
        box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
    }
    .role-badge {
        padding: 4px 8px;
        border-radius: 15px;
        font-size: 10px;
        font-weight: 600;
        text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        border: 1px solid rgba(255,255,255,0.2);
        display: inline-block;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 100%;
        box-sizing: border-box;
    }
    .role-super-admin {
        background: linear-gradient(45deg, #fbbf24, #f59e0b);
        color: white;
        box-shadow: 0 2px 4px rgba(251, 191, 36, 0.3);
    }
    .role-admin {
        background: linear-gradient(45deg, #3b82f6, #1d4ed8);
        color: white;
        box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
    }
    .role-moderator {
        background: linear-gradient(45deg, #6b7280, #374151);
        color: white;
        box-shadow: 0 2px 4px rgba(107, 114, 128, 0.3);
    }
    .btn-sm {
        padding: 6px 12px;
        font-size: 12px;
    }
    .btn-info {
        background: linear-gradient(45deg, #06b6d4, #0891b2);
        color: white;
        border: 1px solid rgba(6, 182, 212, 0.3);
        box-shadow: 0 2px 4px rgba(6, 182, 212, 0.2);
    }
    .btn-info:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(6, 182, 212, 0.3);
    }
    .no-results {
        text-align: center;
        padding: 60px 40px;
        color: white;
        opacity: 0.9;
    }
    .no-results h3 {
        margin-bottom: 15px;
        font-size: 1.5em;
    }
    .no-results p {
        opacity: 0.8;
        font-size: 1.1em;
    }
    .success-message {
        background: rgba(16, 185, 129, 0.2);
        border: 1px solid rgba(16, 185, 129, 0.3);
        color: white;
        padding: 15px;
        border-radius: 10px;
        margin-bottom: 20px;
        text-align: center;
    }
    .alert-error {
        background: rgba(239, 68, 68, 0.2);
        border: 1px solid rgba(239, 68, 68, 0.3);
        color: white;
        padding: 15px;
        border-radius: 10px;
        margin-bottom: 20px;
        text-align: center;
    }
    .create-button {
        margin-bottom: 20px;
    }

    /* Enhanced Responsive Design */
    .table-responsive {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    .admin-info {
        display: flex;
        flex-direction: column;
        gap: 5px;
    }

    .admin-info .username {
        font-weight: 600;
        color: #ffffff !important;
        font-size: 14px;
    }

    .admin-info .mobile-details {
        display: none;
        font-size: 12px;
        color: rgba(255, 255, 255, 0.8) !important;
        margin-top: 8px;
        line-height: 1.4;
    }

    .admin-info .mobile-details > div {
        margin-bottom: 3px;
        padding: 2px 0;
    }

    .mobile-badges {
        display: none;
        gap: 5px;
        margin-top: 5px;
        flex-wrap: wrap;
    }

    .mobile-badges .badge {
        font-size: 10px;
        padding: 3px 8px;
        border-radius: 12px;
    }

    .badge-role {
        background: linear-gradient(45deg, #3b82f6, #1d4ed8);
        color: white;
    }

    .badge-status {
        background: linear-gradient(45deg, #10b981, #059669);
        color: white;
    }

    .badge-status.inactive {
        background: linear-gradient(45deg, #ef4444, #dc2626);
    }

    .action-buttons {
        display: flex;
        gap: 5px;
        justify-content: center;
    }

    /* Tablet Design */
    @media (max-width: 1024px) {
        .page-header {
            padding: 25px;
        }
        .search-section {
            padding: 20px;
        }
        .table th,
        .table td {
            padding: 12px 10px;
            font-size: 13px;
        }
        .role-badge,
        .status-badge {
            padding: 3px 8px;
            font-size: 10px;
            max-width: 80px;
        }
    }

    /* Mobile Design */
    @media (max-width: 768px) {
        .search-form {
            flex-direction: column;
            align-items: stretch;
            gap: 15px;
        }
        .form-group.search-type,
        .form-group.search-input,
        .form-group.search-button {
            flex: none;
            width: 100%;
        }
        .form-group.search-button {
            margin-top: 0;
        }

        .page-header {
            padding: 20px;
        }
        .page-title {
            font-size: 24px;
        }
        .search-section {
            padding: 20px;
        }

        /* Mobile table layout */
        .admin-users-table {
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }

        .table {
            min-width: 100%;
            table-layout: fixed;
            border-spacing: 0;
            border-collapse: separate;
        }

        .table tbody tr {
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* Hide desktop columns on mobile */
        .table th:nth-child(3),  /* Email */
        .table th:nth-child(4),  /* Full Name */
        .table th:nth-child(6),  /* Status */
        .table th:nth-child(7),  /* Last Login */
        .table th:nth-child(8),  /* Created */
        .table td:nth-child(3),
        .table td:nth-child(4),
        .table td:nth-child(6),
        .table td:nth-child(7),
        .table td:nth-child(8) {
            display: none !important;
        }

        /* Show mobile info */
        .admin-info .mobile-details {
            display: block !important;
        }

        .mobile-badges {
            display: flex !important;
        }

        .table th,
        .table td {
            padding: 12px 6px;
            vertical-align: top;
            word-wrap: break-word;
            overflow: hidden;
            position: relative;
        }

        /* Fixed column widths for mobile */
        .table th:nth-child(1),
        .table td:nth-child(1) {
            width: 50px;
            min-width: 50px;
            max-width: 50px;
        }  /* ID */

        .table th:nth-child(2),
        .table td:nth-child(2) {
            width: calc(100% - 180px);
            min-width: 200px;
        }  /* Username + details */

        .table th:nth-child(5),
        .table td:nth-child(5) {
            width: 100px;
            min-width: 100px;
            max-width: 100px;
            text-align: center;
        }  /* Role */

        .table th:nth-child(9),
        .table td:nth-child(9) {
            width: 50px;
            min-width: 50px;
            max-width: 50px;
        }  /* Actions */

        /* Improve mobile info layout */
        .admin-info {
            display: block;
            width: 100%;
        }

        .admin-info .username {
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            font-weight: 600;
            line-height: 1.2;
        }

        .admin-info .mobile-details {
            font-size: 11px;
            line-height: 1.3;
            color: rgba(255, 255, 255, 0.8) !important;
        }

        .mobile-detail-item {
            display: flex;
            align-items: center;
            margin-bottom: 4px;
            padding: 1px 0;
            gap: 6px;
        }

        .mobile-icon {
            flex-shrink: 0;
            width: 16px;
            font-size: 10px;
        }

        .mobile-text {
            flex: 1;
            min-width: 0;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            font-size: 11px;
        }

        .mobile-badges {
            margin-top: 8px;
            gap: 4px;
            flex-wrap: wrap;
        }

        .mobile-badges .badge {
            font-size: 9px;
            padding: 2px 6px;
            border-radius: 10px;
            white-space: nowrap;
        }

        /* Compact role and status badges for mobile */
        .table .role-badge {
            font-size: 9px;
            padding: 3px 6px;
            border-radius: 12px;
            max-width: 90px;
        }

        .table .status-badge {
            font-size: 9px;
            padding: 3px 6px;
            border-radius: 12px;
            max-width: 90px;
        }
    }

    /* Small Mobile Design */
    @media (max-width: 576px) {
        .page-header {
            padding: 15px;
        }
        .search-section {
            padding: 15px;
        }
        .btn {
            padding: 10px 16px;
            font-size: 13px;
        }

        /* Ultra compact table for small screens */
        .table th,
        .table td {
            padding: 10px 4px;
            font-size: 11px;
        }

        /* Hide role column on very small screens */
        .table th:nth-child(5),
        .table td:nth-child(5) {
            display: none !important;
        }

        /* Adjust column widths for 3-column layout */
        .table th:nth-child(1),
        .table td:nth-child(1) {
            width: 40px;
            min-width: 40px;
            max-width: 40px;
        }  /* ID */

        .table th:nth-child(2),
        .table td:nth-child(2) {
            width: calc(100% - 90px);
            min-width: 150px;
        }  /* Username + details */

        .table th:nth-child(9),
        .table td:nth-child(9) {
            width: 50px;
            min-width: 50px;
            max-width: 50px;
        }  /* Actions */

        .admin-info .username {
            font-size: 12px;
            margin-bottom: 6px;
        }

        .admin-info .mobile-details {
            font-size: 10px;
            line-height: 1.2;
        }

        .mobile-detail-item {
            margin-bottom: 3px;
            padding: 0;
            gap: 4px;
        }

        .mobile-icon {
            width: 14px;
            font-size: 9px;
        }

        .mobile-text {
            font-size: 10px;
        }

        .mobile-badges {
            margin-top: 6px;
            gap: 3px;
        }

        .mobile-badges .badge {
            font-size: 8px;
            padding: 1px 4px;
            border-radius: 8px;
        }

        .create-button .btn {
            width: 100%;
            text-align: center;
            padding: 12px;
            font-size: 14px;
        }

        .form-control {
            font-size: 16px; /* Prevent zoom on iOS */
            padding: 14px;
        }

        .btn-sm {
            padding: 6px 8px;
            font-size: 10px;
        }

        /* Compact admin ID */
        .admin-id {
            font-size: 10px;
            font-weight: 600;
        }
    }
</style>
@endsection

@section('content')
    <!-- Success/Error Messages -->
    @if(session('success'))
        <div class="success-message">
            ✅ {{ session('success') }}
        </div>
    @endif

    @if($errors->any())
        <div class="alert-error">
            @foreach($errors->all() as $error)
                ❌ {{ $error }}
            @endforeach
        </div>
    @endif

    <!-- Page Header -->
    <div class="page-header">
        <h1 class="page-title">👥 Quản lý Admin Users</h1>
        <p class="page-desc">Tìm kiếm, xem thông tin và quản lý tài khoản admin</p>
    </div>

    <!-- Create Button -->
    @if($admin['role'] === 'super_admin')
        <div class="create-button">
            <a href="{{ route('admin.admin-users.create') }}" class="btn btn-success">
                ➕ Tạo Admin User mới
            </a>
        </div>
    @endif

    <!-- Search Section -->
    <div class="search-section">
        <form class="search-form" method="GET">
            <div class="form-group search-type">
                <label>Lọc theo role</label>
                <select name="role" class="form-control">
                    <option value="">Tất cả</option>
                    <option value="super_admin" {{ $role == 'super_admin' ? 'selected' : '' }}>Super Admin</option>
                    <option value="admin" {{ $role == 'admin' ? 'selected' : '' }}>Admin</option>
                    <option value="moderator" {{ $role == 'moderator' ? 'selected' : '' }}>Moderator</option>
                </select>
            </div>
            <div class="form-group search-input">
                <label>Từ khóa tìm kiếm</label>
                <input type="text" name="search" class="form-control" placeholder="Tên đăng nhập, email, họ tên..." value="{{ $search }}">
            </div>
            <div class="form-group search-button">
                <button type="submit" class="btn btn-primary">🔍 Tìm kiếm</button>
            </div>
        </form>
    </div>

    <!-- Admin Users Table -->
    <div class="admin-users-table">
        @if(count($adminUsers) > 0)
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th class="text-center">ID</th>
                            <th>Tên đăng nhập</th>
                            <th class="d-none d-md-table-cell">Email</th>
                            <th class="d-none d-md-table-cell">Họ và tên</th>
                            <th class="text-center d-none d-sm-table-cell">Role</th>
                            <th class="text-center d-none d-md-table-cell">Trạng thái</th>
                            <th class="text-center d-none d-md-table-cell">Đăng nhập cuối</th>
                            <th class="text-center d-none d-md-table-cell">Tạo lúc</th>
                            <th class="text-center">Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($adminUsers as $adminUser)
                            <tr class="admin-row" data-admin-id="{{ $adminUser->id }}">
                                <td class="text-center">
                                    <span class="admin-id">#{{ $adminUser->id }}</span>
                                </td>
                                <td>
                                    <div class="admin-info">
                                        <div class="username">{{ $adminUser->username }}</div>
                                        <div class="mobile-details d-md-none">
                                            <div class="mobile-detail-item">
                                                <span class="mobile-icon">📧</span>
                                                <span class="mobile-text">{{ Str::limit($adminUser->email ?: 'Chưa có email', 25) }}</span>
                                            </div>
                                            <div class="mobile-detail-item">
                                                <span class="mobile-icon">👤</span>
                                                <span class="mobile-text">{{ Str::limit($adminUser->full_name ?: 'Chưa có tên', 20) }}</span>
                                            </div>
                                            <div class="mobile-detail-item">
                                                <span class="mobile-icon">🕒</span>
                                                <span class="mobile-text">{{ $adminUser->last_login_at ? $adminUser->last_login_at->format('d/m H:i') : 'Chưa đăng nhập' }}</span>
                                            </div>
                                            <div class="mobile-detail-item">
                                                <span class="mobile-icon">📅</span>
                                                <span class="mobile-text">{{ $adminUser->created_at->format('d/m/Y') }}</span>
                                            </div>
                                        </div>
                                        <div class="mobile-badges d-sm-none">
                                            <span class="badge badge-role">
                                                @if($adminUser->role === 'super_admin')
                                                    Super
                                                @elseif($adminUser->role === 'admin')
                                                    Admin
                                                @elseif($adminUser->role === 'moderator')
                                                    Mod
                                                @else
                                                    {{ $adminUser->getRoleText() }}
                                                @endif
                                            </span>
                                            <span class="badge badge-status {{ $adminUser->is_active ? '' : 'inactive' }}">
                                                {{ $adminUser->is_active ? 'ON' : 'OFF' }}
                                            </span>
                                        </div>
                                    </div>
                                </td>
                                <td class="d-none d-md-table-cell">
                                    <span class="email">{{ $adminUser->email ?: 'Chưa có email' }}</span>
                                </td>
                                <td class="d-none d-md-table-cell">
                                    <span class="full-name">{{ $adminUser->full_name ?: 'Chưa có tên' }}</span>
                                </td>
                                <td class="text-center d-none d-sm-table-cell">
                                    <span class="role-badge role-{{ str_replace('_', '-', $adminUser->role) }}">
                                        @if($adminUser->role === 'super_admin')
                                            Super
                                        @elseif($adminUser->role === 'admin')
                                            Admin
                                        @elseif($adminUser->role === 'moderator')
                                            Mod
                                        @else
                                            {{ $adminUser->getRoleText() }}
                                        @endif
                                    </span>
                                </td>
                                <td class="text-center d-none d-md-table-cell">
                                    <span class="status-badge {{ $adminUser->is_active ? 'status-active' : 'status-inactive' }}">
                                        {{ $adminUser->is_active ? 'ON' : 'OFF' }}
                                    </span>
                                </td>
                                <td class="text-center d-none d-md-table-cell">
                                    <span class="last-login">
                                        {{ $adminUser->last_login_at ? $adminUser->last_login_at->format('d/m/Y') : 'Chưa đăng nhập' }}
                                    </span>
                                    @if($adminUser->last_login_at)
                                        <small class="d-block text-muted">
                                            {{ $adminUser->last_login_at->format('H:i') }}
                                        </small>
                                    @endif
                                </td>
                                <td class="text-center d-none d-md-table-cell">
                                    <span class="created-date">
                                        {{ $adminUser->created_at->format('d/m/Y') }}
                                    </span>
                                    <small class="d-block text-muted">
                                        {{ $adminUser->created_at->format('H:i') }}
                                    </small>
                                </td>
                                <td class="text-center">
                                    <div class="action-buttons">
                                        <a href="{{ route('admin.admin-users.show', $adminUser->id) }}"
                                           class="btn btn-info btn-sm"
                                           title="Xem chi tiết">
                                            👁️ <span class="d-none d-sm-inline">Xem</span>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {{ $adminUsers->appends(request()->query())->links('pagination.custom') }}
        @else
            <div class="no-results">
                <h3>🔍 Không tìm thấy admin user nào</h3>
                <p>Thử thay đổi từ khóa tìm kiếm hoặc bộ lọc</p>
            </div>
        @endif
    </div>
@endsection
