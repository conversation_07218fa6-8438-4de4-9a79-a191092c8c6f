@extends('layouts.admin')

@section('title', 'Quản lý Card Month')

@section('styles')
<style>
    /* Base Styles */
    .card-month-page {
        min-height: 100vh;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 20px 0;
    }

    .container-fluid {
        max-width: 1400px;
        margin: 0 auto;
        padding: 0 15px;
    }

    /* Page Header */
    .page-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        padding: 30px;
        margin-bottom: 30px;
        text-align: center;
        position: relative;
        overflow: hidden;
        animation: fadeInDown 0.6s ease-out;
    }

    @keyframes fadeInDown {
        from {
            opacity: 0;
            transform: translateY(-30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .page-title {
        color: white;
        font-size: 2.5rem;
        font-weight: 700;
        margin: 0;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .page-subtitle {
        color: rgba(255, 255, 255, 0.9);
        font-size: 1.1rem;
        margin-top: 10px;
        font-weight: 400;
    }

    /* Statistics Grid */
    .stats-section {
        margin-bottom: 30px;
        animation: fadeInUp 0.6s ease-out 0.2s both;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 0;
    }

    .stat-card {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(16px);
        border-radius: 16px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        padding: 24px;
        text-align: center;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
        transition: left 0.5s;
    }

    .stat-card:hover::before {
        left: 100%;
    }

    .stat-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 12px 24px rgba(0, 0, 0, 0.2);
    }

    .stat-icon {
        font-size: 2.5rem;
        margin-bottom: 12px;
        display: block;
    }

    .stat-value {
        font-size: 1.8rem;
        font-weight: 700;
        color: white;
        margin-bottom: 8px;
        line-height: 1;
    }

    .stat-label {
        color: rgba(255, 255, 255, 0.8);
        font-size: 0.9rem;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Action Sections */
    .action-section {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(16px);
        border-radius: 16px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        padding: 24px;
        margin-bottom: 24px;
        animation: fadeInUp 0.6s ease-out 0.4s both;
    }

    .search-form .row {
        align-items: end;
    }

    .form-floating {
        position: relative;
        margin-bottom: 0;
    }

    .form-floating .form-control,
    .form-floating .form-select {
        background: rgba(255, 255, 255, 0.1);
        border: 2px solid rgba(255, 255, 255, 0.2);
        border-radius: 12px;
        color: white;
        font-size: 1rem;
        padding: 1rem 0.75rem 0.5rem;
        height: calc(3.5rem + 2px);
        line-height: 1.25;
        transition: all 0.3s ease;
    }

    .form-floating .form-control:focus,
    .form-floating .form-select:focus {
        background: rgba(255, 255, 255, 0.15);
        border-color: #3b82f6;
        box-shadow: 0 0 0 0.25rem rgba(59, 130, 246, 0.25);
        color: white;
        outline: 0;
    }

    .form-floating .form-control::placeholder {
        color: transparent;
    }

    .form-floating label {
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        padding: 1rem 0.75rem;
        pointer-events: none;
        border: 1px solid transparent;
        transform-origin: 0 0;
        transition: opacity 0.1s ease-in-out, transform 0.1s ease-in-out;
        color: rgba(255, 255, 255, 0.7);
        font-size: 1rem;
    }

    .form-floating .form-control:focus ~ label,
    .form-floating .form-control:not(:placeholder-shown) ~ label,
    .form-floating .form-select ~ label {
        opacity: 0.65;
        transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
        color: #3b82f6;
    }

    /* Buttons */
    .btn-modern {
        padding: 12px 24px;
        border-radius: 12px;
        font-weight: 600;
        font-size: 0.95rem;
        border: none;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        min-height: 48px;
    }

    .btn-search {
        background: linear-gradient(135deg, #3b82f6, #1d4ed8);
        color: white;
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    }

    .btn-search:hover {
        background: linear-gradient(135deg, #1d4ed8, #1e40af);
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
        color: white;
    }

    .btn-create {
        background: linear-gradient(135deg, #10b981, #059669);
        color: white;
        box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
    }

    .btn-create:hover {
        background: linear-gradient(135deg, #059669, #047857);
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(16, 185, 129, 0.4);
        color: white;
    }

    .btn-toggle {
        background: rgba(255, 255, 255, 0.1);
        color: white;
        border: 2px solid rgba(255, 255, 255, 0.3);
        backdrop-filter: blur(10px);
    }

    .btn-toggle:hover {
        background: rgba(255, 255, 255, 0.2);
        border-color: rgba(255, 255, 255, 0.5);
        color: white;
    }

    /* Create Form */
    .create-form {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(16px);
        border-radius: 16px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        padding: 24px;
        margin-bottom: 24px;
        display: none;
        animation: slideDown 0.5s ease-out;
    }

    .create-form.show {
        display: block;
    }

    @keyframes slideDown {
        from {
            opacity: 0;
            transform: translateY(-20px);
            max-height: 0;
        }
        to {
            opacity: 1;
            transform: translateY(0);
            max-height: 500px;
        }
    }

    .create-form h4 {
        color: white;
        margin-bottom: 20px;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    /* Table Styles */
    .table-section {
        animation: fadeInUp 0.6s ease-out 0.6s both;
    }

    .table-container {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(16px);
        border-radius: 16px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        overflow: hidden;
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
    }

    .table-responsive {
        border-radius: 16px;
        overflow-x: auto;
    }

    .table {
        color: white;
        margin: 0;
        font-size: 0.9rem;
    }

    .table th {
        background: rgba(255, 255, 255, 0.2);
        border: none;
        color: white;
        font-weight: 600;
        padding: 16px 12px;
        text-transform: uppercase;
        font-size: 0.8rem;
        letter-spacing: 0.5px;
        white-space: nowrap;
        position: sticky;
        top: 0;
        z-index: 10;
    }

    .table td {
        background: rgba(255, 255, 255, 0.05);
        border: none;
        padding: 16px 12px;
        vertical-align: middle;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .table tbody tr {
        transition: all 0.3s ease;
    }

    .table tbody tr:hover {
        background: rgba(255, 255, 255, 0.15) !important;
        transform: scale(1.01);
    }

    .table tbody tr:last-child td {
        border-bottom: none;
    }

    /* User Info in Table */
    .user-info {
        display: flex;
        flex-direction: column;
        gap: 4px;
    }

    .user-name {
        font-weight: 600;
        color: white;
        font-size: 0.95rem;
    }

    .user-email {
        color: rgba(255, 255, 255, 0.6);
        font-size: 0.8rem;
    }

    .package-info {
        display: flex;
        flex-direction: column;
        gap: 4px;
    }

    .package-name {
        font-weight: 500;
        color: white;
    }

    .package-duration {
        color: rgba(255, 255, 255, 0.6);
        font-size: 0.8rem;
    }

    .coin-amount {
        color: #fbbf24;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 4px;
    }

    .date-info {
        font-size: 0.85rem;
        color: rgba(255, 255, 255, 0.8);
    }

    /* Status Badges */
    .status-badge {
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        display: inline-flex;
        align-items: center;
        gap: 6px;
        white-space: nowrap;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    }

    .status-active {
        background: linear-gradient(135deg, #10b981, #059669);
        color: white;
    }

    .status-active::before {
        content: '●';
        animation: pulse 2s infinite;
    }

    .status-expired {
        background: linear-gradient(135deg, #f59e0b, #d97706);
        color: white;
    }

    .status-cancelled {
        background: linear-gradient(135deg, #ef4444, #dc2626);
        color: white;
    }

    @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.5; }
    }

    /* Progress Badge */
    .progress-badge {
        background: rgba(59, 130, 246, 0.2);
        border: 1px solid rgba(59, 130, 246, 0.4);
        color: #60a5fa;
        padding: 6px 12px;
        border-radius: 12px;
        font-size: 0.8rem;
        font-weight: 600;
        white-space: nowrap;
    }

    /* Action Buttons */
    .action-buttons {
        display: flex;
        gap: 6px;
        flex-wrap: wrap;
    }

    .btn-action {
        padding: 8px 12px;
        border-radius: 8px;
        border: none;
        font-size: 0.8rem;
        font-weight: 500;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        min-width: 36px;
        height: 36px;
    }

    .btn-action:hover {
        transform: translateY(-2px);
    }

    .btn-view {
        background: rgba(59, 130, 246, 0.2);
        color: #60a5fa;
        border: 1px solid rgba(59, 130, 246, 0.4);
    }

    .btn-view:hover {
        background: rgba(59, 130, 246, 0.3);
        color: #93c5fd;
    }

    .btn-extend {
        background: rgba(245, 158, 11, 0.2);
        color: #fbbf24;
        border: 1px solid rgba(245, 158, 11, 0.4);
    }

    .btn-extend:hover {
        background: rgba(245, 158, 11, 0.3);
        color: #fcd34d;
    }

    .btn-cancel {
        background: rgba(239, 68, 68, 0.2);
        color: #f87171;
        border: 1px solid rgba(239, 68, 68, 0.4);
    }

    .btn-cancel:hover {
        background: rgba(239, 68, 68, 0.3);
        color: #fca5a5;
    }

    /* Empty State */
    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: rgba(255, 255, 255, 0.6);
    }

    .empty-icon {
        font-size: 4rem;
        margin-bottom: 20px;
        opacity: 0.5;
    }

    .empty-message {
        font-size: 1.1rem;
        font-weight: 500;
    }

    /* Pagination */
    .pagination-section {
        margin-top: 30px;
        animation: fadeInUp 0.6s ease-out 0.8s both;
    }

    .pagination {
        justify-content: center;
    }

    .pagination .page-link {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: white;
        margin: 0 4px;
        border-radius: 8px;
        padding: 10px 16px;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .pagination .page-link:hover {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        transform: translateY(-2px);
    }

    .pagination .page-item.active .page-link {
        background: linear-gradient(135deg, #3b82f6, #1d4ed8);
        border-color: #3b82f6;
        color: white;
    }

    .pagination .page-item.disabled .page-link {
        background: rgba(255, 255, 255, 0.05);
        color: rgba(255, 255, 255, 0.3);
    }

    /* Responsive Design */
    @media (max-width: 1200px) {
        .stats-grid {
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 16px;
        }

        .stat-card {
            padding: 20px;
        }

        .stat-value {
            font-size: 1.6rem;
        }
    }

    @media (max-width: 768px) {
        .container-fluid {
            padding: 0 10px;
        }

        .page-header {
            padding: 20px;
            margin-bottom: 20px;
        }

        .page-title {
            font-size: 2rem;
        }

        .page-subtitle {
            font-size: 1rem;
        }

        .stats-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
        }

        .stat-card {
            padding: 16px;
        }

        .stat-icon {
            font-size: 2rem;
        }

        .stat-value {
            font-size: 1.4rem;
        }

        .stat-label {
            font-size: 0.8rem;
        }

        .action-section {
            padding: 20px;
        }

        .search-form .row > div {
            margin-bottom: 16px;
        }

        .btn-modern {
            width: 100%;
            margin-bottom: 8px;
        }

        .table-responsive {
            font-size: 0.8rem;
        }

        .table th,
        .table td {
            padding: 12px 8px;
        }

        .action-buttons {
            flex-direction: column;
            gap: 4px;
        }

        .btn-action {
            width: 100%;
            justify-content: flex-start;
            gap: 8px;
        }
    }

    @media (max-width: 576px) {
        .stats-grid {
            grid-template-columns: 1fr;
        }

        .page-title {
            font-size: 1.8rem;
        }

        .create-form,
        .action-section {
            padding: 16px;
        }

        .table th,
        .table td {
            padding: 10px 6px;
            font-size: 0.75rem;
        }

        .status-badge {
            padding: 6px 10px;
            font-size: 0.7rem;
        }

        .progress-badge {
            padding: 4px 8px;
            font-size: 0.7rem;
        }
    }
</style>
@endsection

@section('content')
<div class="card-month-page">
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title">
                💳 Quản lý Card Month
            </h1>
            <p class="page-subtitle">Quản lý thẻ tháng từ hệ thống cũ và mới, tạo thẻ tháng cho người chơi</p>
        </div>

        <!-- Statistics -->
        <div class="stats-section">
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">📊</div>
                    <div class="stat-value">{{ number_format($stats['total_purchases']) }}</div>
                    <div class="stat-label">Tổng thẻ tháng</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">✅</div>
                    <div class="stat-value">{{ number_format($stats['active_cards']) }}</div>
                    <div class="stat-label">Đang hoạt động</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">⏰</div>
                    <div class="stat-value">{{ number_format($stats['expired_cards']) }}</div>
                    <div class="stat-label">Đã hết hạn</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">💰</div>
                    <div class="stat-value">{{ number_format($stats['total_revenue']) }}</div>
                    <div class="stat-label">Tổng doanh thu (coin)</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">📈</div>
                    <div class="stat-value">{{ number_format($stats['monthly_revenue']) }}</div>
                    <div class="stat-label">Doanh thu tháng này</div>
                </div>
            </div>
        </div>

        <!-- Create Card Month Form -->
        <div class="create-form" id="createForm">
            <h4>
                <i class="fas fa-plus-circle"></i>
                Tạo thẻ tháng mới
            </h4>

            <form action="{{ route('admin.card-month.create') }}" method="POST" class="search-form">
                @csrf
                <div class="row g-3">
                    <div class="col-md-4">
                        <div class="form-floating">
                            <input type="text" class="form-control" id="username" name="username" placeholder="Username" required>
                            <label for="username">
                                <i class="fas fa-user me-2"></i>Username
                            </label>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-floating">
                            <select class="form-select" id="character_id" name="character_id" required disabled>
                                <option value="">Chọn nhân vật...</option>
                            </select>
                            <label for="character_id">
                                <i class="fas fa-user-ninja me-2"></i>Nhân vật
                            </label>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-floating">
                            <input type="number" class="form-control" id="duration_days" name="duration_days" min="1" max="365" value="30" required>
                            <label for="duration_days">
                                <i class="fas fa-calendar me-2"></i>Số ngày
                            </label>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-floating">
                            <input type="number" class="form-control" id="daily_coins" name="daily_coins" min="1" value="2000" required>
                            <label for="daily_coins">
                                <i class="fas fa-coins me-2"></i>Coin/ngày
                            </label>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="form-floating">
                            <textarea class="form-control" id="reason" name="reason" style="height: 80px;" placeholder="Lý do tạo thẻ tháng"></textarea>
                            <label for="reason">
                                <i class="fas fa-comment me-2"></i>Lý do (tùy chọn)
                            </label>
                        </div>
                    </div>
                    <div class="col-12">
                        <button type="submit" class="btn-modern btn-create">
                            <i class="fas fa-plus"></i>Tạo thẻ tháng
                        </button>
                        <button type="button" class="btn-modern btn-toggle ms-2" onclick="toggleCreateForm()">
                            <i class="fas fa-times"></i>Hủy
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Search and Actions -->
        <div class="action-section">
            <form method="GET" action="{{ route('admin.card-month.index') }}" class="search-form">
                <div class="row g-3">
                    <div class="col-lg-4 col-md-6">
                        <div class="form-floating">
                            <input type="text" class="form-control" id="search" name="search" value="{{ $search }}" placeholder="Tìm kiếm...">
                            <label for="search">
                                <i class="fas fa-search me-2"></i>Tìm kiếm username, email...
                            </label>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="form-floating">
                            <select class="form-select" id="status" name="status">
                                <option value="all" {{ $statusFilter == 'all' ? 'selected' : '' }}>Tất cả</option>
                                <option value="active" {{ $statusFilter == 'active' ? 'selected' : '' }}>Đang hoạt động</option>
                                <option value="expired" {{ $statusFilter == 'expired' ? 'selected' : '' }}>Đã hết hạn</option>
                                <option value="cancelled" {{ $statusFilter == 'cancelled' ? 'selected' : '' }}>Đã hủy</option>
                            </select>
                            <label for="status">
                                <i class="fas fa-filter me-2"></i>Trạng thái
                            </label>
                        </div>
                    </div>
                    <div class="col-lg-2 col-md-6">
                        <button type="submit" class="btn-modern btn-search w-100">
                            <i class="fas fa-search"></i>Tìm kiếm
                        </button>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <button type="button" class="btn-modern btn-create w-100" onclick="toggleCreateForm()">
                            <i class="fas fa-plus"></i>Tạo thẻ tháng
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Card Month Table -->
        <div class="table-section">
            <div class="table-container">
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Người dùng</th>
                                <th>Nhân vật</th>
                                <th>Gói thẻ</th>
                                <th>Coin/ngày</th>
                                <th>Ngày tạo</th>
                                <th>Hết hạn</th>
                                <th>Tiến độ</th>
                                <th>Trạng thái</th>
                                <th>Thao tác</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($cardMonthPurchases as $purchase)
                            <tr>
                                <td>
                                    <strong>#{{ $purchase->id }}</strong>
                                </td>
                                <td>
                                    <div class="user-info">
                                        <div class="user-name">{{ $purchase->UserName ?? 'N/A' }}</div>
                                        @if($purchase->Email)
                                            <div class="user-email">{{ $purchase->Email }}</div>
                                        @endif
                                    </div>
                                </td>
                                <td>
                                    <div class="user-info">
                                        @if($purchase->character_name)
                                            <div class="user-name">🎮 {{ $purchase->character_name }}</div>
                                            <div class="user-email">ID: {{ $purchase->character_id }}</div>
                                        @else
                                            <div class="user-email" style="color: rgba(255, 255, 255, 0.5);">Chưa có thông tin</div>
                                        @endif
                                    </div>
                                </td>
                                <td>
                                    <div class="package-info">
                                        <div class="package-name">{{ $purchase->package_name }}</div>
                                        <div class="package-duration">{{ $purchase->duration_days }} ngày</div>
                                    </div>
                                </td>
                                <td>
                                    <div class="coin-amount">
                                        <i class="fas fa-coins"></i>
                                        {{ number_format($purchase->daily_reward_coins) }}
                                    </div>
                                </td>
                                <td>
                                    <div class="date-info">
                                        {{ date('d/m/Y', strtotime($purchase->created_at)) }}
                                        <br>
                                        <small>{{ date('H:i', strtotime($purchase->created_at)) }}</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="date-info">
                                        {{ date('d/m/Y', strtotime($purchase->expires_at)) }}
                                        <br>
                                        <small>{{ date('H:i', strtotime($purchase->expires_at)) }}</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="progress-badge">
                                        {{ $purchase->days_claimed }}/{{ $purchase->duration_days }}
                                    </div>
                                </td>
                                <td>
                                    @php
                                        $isActive = $purchase->status === 'active' && strtotime($purchase->expires_at) > time();
                                        $isExpired = strtotime($purchase->expires_at) <= time();
                                    @endphp

                                    @if($isActive)
                                        <span class="status-badge status-active">Hoạt động</span>
                                    @elseif($isExpired)
                                        <span class="status-badge status-expired">Hết hạn</span>
                                    @else
                                        <span class="status-badge status-cancelled">{{ ucfirst($purchase->status) }}</span>
                                    @endif
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <button type="button" class="btn-action btn-view" onclick="viewDetails({{ $purchase->id }})" title="Xem chi tiết">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        @if($isActive)
                                            <button type="button" class="btn-action btn-extend" onclick="extendCard({{ $purchase->id }})" title="Gia hạn">
                                                <i class="fas fa-clock"></i>
                                            </button>
                                            <button type="button" class="btn-action btn-cancel" onclick="cancelCard({{ $purchase->id }})" title="Hủy thẻ">
                                                <i class="fas fa-ban"></i>
                                            </button>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="10">
                                    <div class="empty-state">
                                        <div class="empty-icon">📭</div>
                                        <div class="empty-message">Không có dữ liệu thẻ tháng</div>
                                    </div>
                                </td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Pagination -->
        @if($cardMonthPurchases->hasPages())
            <div class="pagination-section">
                {{ $cardMonthPurchases->appends(request()->query())->links() }}
            </div>
        @endif
    </div>
</div>

<script>
function toggleCreateForm() {
    const form = document.getElementById('createForm');
    form.classList.toggle('show');
}

// Auto-load characters when username is entered
document.getElementById('username').addEventListener('input', function() {
    const username = this.value.trim();
    const characterSelect = document.getElementById('character_id');

    if (username.length >= 3) {
        // Clear previous options
        characterSelect.innerHTML = '<option value="">Đang tải...</option>';
        characterSelect.disabled = true;

        // Fetch characters
        fetch(`/admin/card-month/search-account?username=${encodeURIComponent(username)}`)
            .then(response => response.json())
            .then(data => {
                characterSelect.innerHTML = '<option value="">Chọn nhân vật...</option>';

                if (data.success && data.characters && data.characters.length > 0) {
                    data.characters.forEach(character => {
                        const option = document.createElement('option');
                        option.value = character.rid;
                        option.textContent = `${character.rname} (Lv.${character.level})`;
                        characterSelect.appendChild(option);
                    });
                    characterSelect.disabled = false;
                } else {
                    const option = document.createElement('option');
                    option.value = '';
                    option.textContent = 'Không tìm thấy nhân vật';
                    characterSelect.appendChild(option);
                }
            })
            .catch(error => {
                console.error('Error fetching characters:', error);
                characterSelect.innerHTML = '<option value="">Lỗi khi tải nhân vật</option>';
            });
    } else {
        characterSelect.innerHTML = '<option value="">Chọn nhân vật...</option>';
        characterSelect.disabled = true;
    }
});

function viewDetails(id) {
    // Implement view details functionality
    alert('Xem chi tiết thẻ tháng ID: ' + id);
}

function extendCard(id) {
    const days = prompt('Nhập số ngày muốn gia hạn (1-365):');
    if (!days || parseInt(days) <= 0 || parseInt(days) > 365) {
        return;
    }

    const reason = prompt('Nhập lý do gia hạn:');
    if (!reason || reason.trim() === '') {
        alert('Vui lòng nhập lý do gia hạn');
        return;
    }

    if (confirm(`Bạn có chắc chắn muốn gia hạn thẻ tháng này thêm ${days} ngày?`)) {
        fetch(`/admin/card-month/${id}/extend`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                days: parseInt(days),
                reason: reason.trim()
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                location.reload();
            } else {
                alert('Lỗi: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Có lỗi xảy ra khi gia hạn thẻ tháng');
        });
    }
}

function cancelCard(id) {
    const reason = prompt('Nhập lý do hủy thẻ tháng:');
    if (!reason || reason.trim() === '') {
        alert('Vui lòng nhập lý do hủy thẻ tháng');
        return;
    }

    if (confirm('Bạn có chắc chắn muốn hủy thẻ tháng này? Hành động này không thể hoàn tác!')) {
        fetch(`/admin/card-month/${id}/cancel`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                reason: reason.trim()
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                location.reload();
            } else {
                alert('Lỗi: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Có lỗi xảy ra khi hủy thẻ tháng');
        });
    }
}

// Show success/error messages
@if(session('success'))
    setTimeout(() => {
        alert('{{ session('success') }}');
    }, 500);
@endif

@if($errors->any())
    setTimeout(() => {
        alert('Có lỗi xảy ra:\n{{ implode('\n', $errors->all()) }}');
    }, 500);
@endif
</script>
@endsection
