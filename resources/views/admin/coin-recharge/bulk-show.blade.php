@extends('layouts.admin')

@section('title', '<PERSON> tiết nạp hàng loạt - MU Admin Panel')

@section('styles')
<style>
        .breadcrumb {
            color: white;
            margin-bottom: 20px;
            opacity: 0.8;
        }
        .breadcrumb a {
            color: white;
            text-decoration: none;
        }
        .breadcrumb a:hover {
            text-decoration: underline;
        }
        .bulk-header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(16px);
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 30px;
            margin-bottom: 30px;
            color: white;
        }
        .bulk-title {
            font-size: 28px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 15px;
        }
        .bulk-meta {
            display: flex;
            gap: 20px;
            align-items: center;
            flex-wrap: wrap;
        }
        .meta-badge {
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 14px;
        }
        .pattern-badge {
            background: linear-gradient(45deg, #8b5cf6, #7c3aed);
            color: white;
        }
        .amount-badge {
            background: linear-gradient(45deg, #10b981, #059669);
            color: white;
        }
        .count-badge {
            background: linear-gradient(45deg, #3b82f6, #2563eb);
            color: white;
        }
        .category-customer {
            background: rgba(16, 185, 129, 0.2);
            color: #10b981;
            border: 1px solid rgba(16, 185, 129, 0.3);
        }
        .category-founder {
            background: rgba(139, 92, 246, 0.2);
            color: #8b5cf6;
            border: 1px solid rgba(139, 92, 246, 0.3);
        }
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .summary-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(16px);
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 25px;
            color: white;
            text-align: center;
        }
        .summary-icon {
            font-size: 32px;
            margin-bottom: 15px;
        }
        .summary-value {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 5px;
        }
        .summary-label {
            opacity: 0.8;
            font-size: 14px;
        }
        .transactions-table {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(16px);
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
        }
        .table-header {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px 25px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        .table-title {
            color: white;
            font-size: 18px;
            font-weight: 600;
            margin: 0;
        }
        .table-responsive {
            overflow-x: auto;
            max-height: 60vh;
            overflow-y: auto;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            min-width: 800px;
        }
        th, td {
            padding: 15px 20px;
            text-align: left;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            white-space: nowrap;
        }
        th {
            background: rgba(255, 255, 255, 0.15);
            color: white;
            font-weight: 600;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            position: sticky;
            top: 0;
            z-index: 10;
            backdrop-filter: blur(16px);
        }
        td {
            color: white;
            font-size: 14px;
        }
        tr:hover {
            background: rgba(255, 255, 255, 0.05);
        }
        .amount-text {
            color: #10b981;
            font-weight: 600;
        }
        .diamonds-text {
            color: #8b5cf6;
            font-weight: 600;
        }
        .status-badge {
            padding: 4px 8px;
            border-radius: 10px;
            font-size: 12px;
            font-weight: 600;
        }
        .status-completed {
            background: rgba(16, 185, 129, 0.2);
            color: #10b981;
            border: 1px solid rgba(16, 185, 129, 0.3);
        }
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        .btn-secondary {
            background: rgba(107, 114, 128, 0.8);
            color: white;
        }
        .btn:hover {
            transform: translateY(-2px);
        }
        .transaction-id {
            font-family: 'Courier New', monospace;
            background: rgba(255, 255, 255, 0.1);
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 11px;
        }
        .admin-info {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(16px);
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 25px;
            color: white;
            margin-bottom: 30px;
        }
        .admin-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .admin-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        .admin-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        .admin-label {
            font-weight: 500;
            opacity: 0.8;
        }
        .admin-value {
            font-weight: 600;
        }
</style>
@endsection

@section('content')
<div class="container">
    <!-- Breadcrumb -->
    <div class="breadcrumb">
        <a href="/admin/dashboard">Dashboard</a> /
        <a href="/admin/coin-recharge">Quản lý nạp kim cương</a> /
        Chi tiết nạp hàng loạt
    </div>

    <!-- Bulk Header -->
    <div class="bulk-header">
        <h1 class="bulk-title">
            🔄 Nạp kim cương hàng loạt
            <span class="pattern-badge">{{ $bulkSummary['pattern'] }}</span>
        </h1>
        <div class="bulk-meta">
            <span class="meta-badge count-badge">{{ $bulkSummary['total_accounts'] }} tài khoản</span>
            <span class="meta-badge amount-badge">{{ number_format($bulkSummary['total_amount']) }}đ</span>
            <span class="meta-badge pattern-badge">{{ number_format($bulkSummary['total_coins']) }} 💎</span>
            @if($bulkSummary['recharge_category'] === 'founder_team')
                <span class="meta-badge category-founder">👥 Team sáng lập</span>
            @else
                <span class="meta-badge category-customer">💰 Khách hàng</span>
            @endif
        </div>
    </div>

    <!-- Summary Grid -->
    <div class="summary-grid">
        <div class="summary-card">
            <div class="summary-icon">👥</div>
            <div class="summary-value">{{ $bulkSummary['total_accounts'] }}</div>
            <div class="summary-label">Tài khoản được nạp</div>
        </div>
        <div class="summary-card">
            <div class="summary-icon">💰</div>
            <div class="summary-value">{{ number_format($bulkSummary['total_amount']) }}đ</div>
            <div class="summary-label">Tổng giá trị VNĐ</div>
        </div>
        <div class="summary-card">
            <div class="summary-icon">💎</div>
            <div class="summary-value">{{ number_format($bulkSummary['total_coins']) }}</div>
            <div class="summary-label">Tổng kim cương</div>
        </div>
        <div class="summary-card">
            <div class="summary-icon">📊</div>
            <div class="summary-value">{{ number_format($bulkSummary['total_coins'] / $bulkSummary['total_accounts']) }}</div>
            <div class="summary-label">Kim cương/tài khoản</div>
        </div>
    </div>

    <!-- Admin Info -->
    <div class="admin-info">
        <h3 class="admin-title">👨‍💼 Thông tin thực hiện</h3>
        <div class="admin-details">
            <div class="admin-item">
                <span class="admin-label">Admin:</span>
                <span class="admin-value">{{ $bulkSummary['admin_username'] }}</span>
            </div>
            <div class="admin-item">
                <span class="admin-label">Thời gian:</span>
                <span class="admin-value">{{ date('d/m/Y H:i:s', strtotime($bulkSummary['created_at'])) }}</span>
            </div>
            <div class="admin-item">
                <span class="admin-label">Pattern:</span>
                <span class="admin-value">{{ $bulkSummary['pattern'] }}</span>
            </div>
            <div class="admin-item">
                <span class="admin-label">Phân loại:</span>
                <span class="admin-value">
                    @if($bulkSummary['recharge_category'] === 'founder_team')
                        👥 Team sáng lập
                    @else
                        💰 Khách hàng
                    @endif
                </span>
            </div>
        </div>
    </div>

    <!-- Transactions Table -->
    <div class="transactions-table">
        <div class="table-header">
            <h3 class="table-title">📋 Chi tiết từng giao dịch ({{ $bulkTransactions->count() }} giao dịch)</h3>
        </div>
        
        <div class="table-responsive">
            <table>
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Tài khoản</th>
                        <th>Email</th>
                        <th>Số tiền</th>
                        <th>Kim cương</th>
                        <th>Trạng thái</th>
                        <th>Mã GD</th>
                        <th>Thời gian</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($bulkTransactions as $transaction)
                        <tr>
                            <td>{{ $transaction->id }}</td>
                            <td><strong>{{ $transaction->UserName }}</strong></td>
                            <td>{{ $transaction->Email ?: 'N/A' }}</td>
                            <td><span class="amount-text">{{ number_format($transaction->amount_vnd) }}đ</span></td>
                            <td><span class="diamonds-text">{{ number_format($transaction->coins_added) }} 💎</span></td>
                            <td>
                                <span class="status-badge status-{{ $transaction->status }}">
                                    @if($transaction->status === 'completed')
                                        ✅ Hoàn thành
                                    @else
                                        {{ $transaction->status }}
                                    @endif
                                </span>
                            </td>
                            <td><span class="transaction-id">{{ $transaction->transaction_id }}</span></td>
                            <td>{{ date('d/m/Y H:i', strtotime($transaction->created_at)) }}</td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>

    <!-- Action Buttons -->
    <div style="margin-top: 30px; text-align: center;">
        <a href="{{ route('admin.coin-recharge.index') }}" class="btn btn-secondary">⬅️ Quay lại danh sách</a>
    </div>
</div>
@endsection
