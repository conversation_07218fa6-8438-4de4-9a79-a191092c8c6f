@extends('layouts.admin')

@section('title', '<PERSON><PERSON><PERSON>')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-coins"></i>
                        <PERSON><PERSON><PERSON>
                    </h3>
                    <div class="card-tools">
                        <a href="{{ route('admin.coin-recharge.index') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> Quay lại
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    @if ($errors->any())
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    <!-- Instructions -->
                    <div class="alert alert-info">
                        <h5><i class="fas fa-info-circle"></i> Hướng dẫn sử dụng:</h5>
                        <ul class="mb-0">
                            <li><strong>Chế độ Pattern:</strong> Tiền tố + hậu tố số (ví dụ: "abcd" + 4-20 = abcd4, abcd5, ..., abcd20)</li>
                            <li><strong>Chế độ Single:</strong> Chỉ nạp cho 1 tài khoản cụ thể (ví dụ: "test")</li>
                            <li><strong>Giới hạn:</strong> Tối đa 100 tài khoản mỗi lần (chế độ Pattern)</li>
                            <li><strong>Lưu ý:</strong> Chỉ nạp cho các tài khoản đã tồn tại</li>
                        </ul>
                    </div>

                    <form id="bulkRechargeForm" method="POST" action="{{ route('admin.coin-recharge.bulk') }}">
                        @csrf
                        <input type="hidden" name="recharge_mode" id="recharge_mode_hidden" value="pattern">
                        <input type="hidden" name="recharge_mode" id="recharge_mode_hidden" value="pattern">
                        
                        <div class="row">
                            <!-- Mode Selection & Configuration -->
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">
                                            <i class="fas fa-cog"></i> Cấu hình tài khoản
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <!-- Mode Selection -->
                                        <div class="form-group">
                                            <label>Chế độ nạp <span class="text-danger">*</span></label>
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="recharge_mode" id="mode_pattern" value="pattern" checked>
                                                <label class="form-check-label" for="mode_pattern">
                                                    <strong>Pattern Mode</strong> - Nạp nhiều tài khoản theo pattern
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="recharge_mode" id="mode_single" value="single">
                                                <label class="form-check-label" for="mode_single">
                                                    <strong>Single Mode</strong> - Nạp cho 1 tài khoản cụ thể
                                                </label>
                                            </div>
                                        </div>

                                        <!-- Single Account Mode -->
                                        <div id="singleModeConfig" style="display: none;">
                                            <div class="form-group">
                                                <label for="single_username">Tên tài khoản <span class="text-danger">*</span></label>
                                                <input type="text"
                                                       class="form-control"
                                                       id="single_username"
                                                       name="single_username"
                                                       value="{{ old('single_username') }}"
                                                       placeholder="Ví dụ: test"
                                                       maxlength="50">
                                                <small class="form-text text-muted">Nhập tên tài khoản cần nạp</small>
                                            </div>
                                        </div>

                                        <!-- Pattern Mode -->
                                        <div id="patternModeConfig">
                                            <div class="form-group">
                                                <label for="prefix">Tiền tố <span class="text-danger">*</span></label>
                                                <input type="text"
                                                       class="form-control"
                                                       id="prefix"
                                                       name="prefix"
                                                       value="{{ old('prefix') }}"
                                                       placeholder="Ví dụ: abcd"
                                                       maxlength="20">
                                                <small class="form-text text-muted">Phần đầu của username</small>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label for="suffix_start">Hậu tố bắt đầu <span class="text-danger">*</span></label>
                                                        <input type="number"
                                                               class="form-control"
                                                               id="suffix_start"
                                                               name="suffix_start"
                                                               value="{{ old('suffix_start') }}"
                                                               placeholder="4"
                                                               min="1"
                                                               max="9999">
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label for="suffix_end">Hậu tố kết thúc <span class="text-danger">*</span></label>
                                                        <input type="number"
                                                               class="form-control"
                                                               id="suffix_end"
                                                               name="suffix_end"
                                                               value="{{ old('suffix_end') }}"
                                                               placeholder="20"
                                                               min="1"
                                                               max="9999">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="form-group">
                                            <label>Preview:</label>
                                            <div id="accountPreview" class="alert alert-light">
                                                <em>Chọn chế độ và nhập thông tin để xem preview</em>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Recharge Configuration -->
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">
                                            <i class="fas fa-money-bill"></i> Cấu hình Nạp Tiền
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group">
                                            <label for="coins_per_account">Kim cương mỗi tài khoản <span class="text-danger">*</span></label>
                                            <input type="number" 
                                                   class="form-control" 
                                                   id="coins_per_account" 
                                                   name="coins_per_account" 
                                                   value="{{ old('coins_per_account') }}" 
                                                   placeholder="100000"
                                                   min="1" 
                                                   max="1000000"
                                                   required>
                                            <small class="form-text text-muted">Số kim cương nạp cho mỗi tài khoản</small>
                                        </div>

                                        <div class="form-group">
                                            <label for="amount_vnd_per_account">Số tiền VNĐ mỗi tài khoản <span class="text-danger">*</span></label>
                                            <input type="number" 
                                                   class="form-control" 
                                                   id="amount_vnd_per_account" 
                                                   name="amount_vnd_per_account" 
                                                   value="{{ old('amount_vnd_per_account') }}" 
                                                   placeholder="1000000"
                                                   min="1000" 
                                                   max="*********"
                                                   required>
                                            <small class="form-text text-muted">Giá trị VNĐ tương ứng cho báo cáo</small>
                                        </div>

                                        <div class="form-group">
                                            <label for="recharge_category">Danh mục <span class="text-danger">*</span></label>
                                            <select class="form-control" id="recharge_category" name="recharge_category" required>
                                                <option value="">-- Chọn danh mục --</option>
                                                <option value="customer" {{ old('recharge_category') == 'customer' ? 'selected' : '' }}>
                                                    Khách hàng
                                                </option>
                                                <option value="founder_team" {{ old('recharge_category') == 'founder_team' ? 'selected' : '' }}>
                                                    Team/Founder
                                                </option>
                                            </select>
                                        </div>

                                        <div class="form-group">
                                            <label for="note">Ghi chú <span class="text-danger">*</span></label>
                                            <textarea class="form-control" 
                                                      id="note" 
                                                      name="note" 
                                                      rows="3" 
                                                      placeholder="Lý do nạp kim cương hàng loạt..."
                                                      maxlength="500"
                                                      required>{{ old('note') }}</textarea>
                                            <small class="form-text text-muted">Mô tả lý do nạp kim cương</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Preview Section -->
                        <div class="card mt-3">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-eye"></i> Xem trước
                                </h5>
                                <div class="card-tools">
                                    <button type="button" id="previewBtn" class="btn btn-info btn-sm">
                                        <i class="fas fa-search"></i> Xem trước
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div id="previewContent">
                                    <p class="text-muted">Nhấn "Xem trước" để kiểm tra các tài khoản sẽ được nạp</p>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Section -->
                        <div class="card mt-3">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <button type="submit" id="submitBtn" class="btn btn-success btn-lg" disabled>
                                            <i class="fas fa-coins"></i> Thực hiện nạp hàng loạt
                                        </button>
                                    </div>
                                    <div class="col-md-6 text-right">
                                        <a href="{{ route('admin.coin-recharge.index') }}" class="btn btn-secondary btn-lg">
                                            <i class="fas fa-times"></i> Hủy bỏ
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.preview-table {
    font-size: 0.875rem;
}

.preview-table th {
    background-color: #f8f9fa;
    font-weight: 600;
}

.account-exists {
    background-color: #d4edda;
}

.account-not-exists {
    background-color: #f8d7da;
}

.summary-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
}

.summary-card .card-body {
    padding: 1.5rem;
}

.summary-stat {
    text-align: center;
    margin-bottom: 1rem;
}

.summary-stat h4 {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 0.25rem;
}

.summary-stat p {
    margin-bottom: 0;
    opacity: 0.9;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const prefixInput = document.getElementById('prefix');
    const startInput = document.getElementById('suffix_start');
    const endInput = document.getElementById('suffix_end');
    const singleUsernameInput = document.getElementById('single_username');
    const coinsInput = document.getElementById('coins_per_account');
    const previewDiv = document.getElementById('accountPreview');
    const previewBtn = document.getElementById('previewBtn');
    const submitBtn = document.getElementById('submitBtn');
    const previewContent = document.getElementById('previewContent');

    const modePatternRadio = document.getElementById('mode_pattern');
    const modeSingleRadio = document.getElementById('mode_single');
    const patternModeConfig = document.getElementById('patternModeConfig');
    const singleModeConfig = document.getElementById('singleModeConfig');
    const rechargeModeHidden = document.getElementById('recharge_mode_hidden');

    // Handle mode switching
    function switchMode() {
        if (modePatternRadio.checked) {
            patternModeConfig.style.display = 'block';
            singleModeConfig.style.display = 'none';
            // Make pattern fields required
            prefixInput.required = true;
            startInput.required = true;
            endInput.required = true;
            singleUsernameInput.required = false;
            rechargeModeHidden.value = 'pattern';
        } else {
            patternModeConfig.style.display = 'none';
            singleModeConfig.style.display = 'block';
            // Make single field required
            prefixInput.required = false;
            startInput.required = false;
            endInput.required = false;
            singleUsernameInput.required = true;
            rechargeModeHidden.value = 'single';
        }
        updateAccountPreview();
    }

    // Update account preview based on mode
    function updateAccountPreview() {
        if (modePatternRadio.checked) {
            updatePatternPreview();
        } else {
            updateSinglePreview();
        }
    }

    // Update pattern preview
    function updatePatternPreview() {
        const prefix = prefixInput.value;
        const start = parseInt(startInput.value);
        const end = parseInt(endInput.value);

        if (prefix && start && end && start <= end) {
            const count = end - start + 1;
            if (count <= 5) {
                let examples = [];
                for (let i = start; i <= end; i++) {
                    examples.push(prefix + i);
                }
                previewDiv.innerHTML = `<strong>Sẽ nạp cho:</strong> ${examples.join(', ')} <br><small class="text-muted">(${count} tài khoản)</small>`;
            } else {
                previewDiv.innerHTML = `<strong>Sẽ nạp cho:</strong> ${prefix}${start}, ${prefix}${start+1}, ..., ${prefix}${end} <br><small class="text-muted">(${count} tài khoản)</small>`;
            }
        } else {
            previewDiv.innerHTML = '<em>Nhập thông tin để xem preview</em>';
        }
    }

    // Update single account preview
    function updateSinglePreview() {
        const username = singleUsernameInput.value.trim();
        if (username) {
            previewDiv.innerHTML = `<strong>Sẽ nạp cho:</strong> ${username} <br><small class="text-muted">(1 tài khoản)</small>`;
        } else {
            previewDiv.innerHTML = '<em>Nhập tên tài khoản để xem preview</em>';
        }
    }

    // Event listeners
    modePatternRadio.addEventListener('change', switchMode);
    modeSingleRadio.addEventListener('change', switchMode);

    // Event listeners for pattern preview
    [prefixInput, startInput, endInput].forEach(input => {
        input.addEventListener('input', updateAccountPreview);
    });

    // Event listener for single username
    singleUsernameInput.addEventListener('input', updateAccountPreview);

    // Initialize mode
    switchMode();

    // Preview button click
    previewBtn.addEventListener('click', function() {
        const formData = new FormData();
        formData.append('_token', document.querySelector('input[name="_token"]').value);
        formData.append('recharge_mode', modePatternRadio.checked ? 'pattern' : 'single');

        if (modePatternRadio.checked) {
            formData.append('prefix', prefixInput.value);
            formData.append('suffix_start', startInput.value);
            formData.append('suffix_end', endInput.value);
        } else {
            formData.append('single_username', singleUsernameInput.value);
        }

        formData.append('coins_per_account', coinsInput.value);

        previewBtn.disabled = true;
        previewBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Đang tải...';

        fetch('{{ route('admin.coin-recharge.bulk-preview') }}', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayPreview(data.preview_data, data.summary);
                submitBtn.disabled = data.summary.valid_accounts === 0;
            } else {
                previewContent.innerHTML = `<div class="alert alert-danger">${data.message}</div>`;
                submitBtn.disabled = true;
            }
        })
        .catch(error => {
            previewContent.innerHTML = '<div class="alert alert-danger">Có lỗi xảy ra khi tải preview</div>';
            submitBtn.disabled = true;
        })
        .finally(() => {
            previewBtn.disabled = false;
            previewBtn.innerHTML = '<i class="fas fa-search"></i> Xem trước';
        });
    });

    function displayPreview(previewData, summary) {
        let html = `
            <div class="row mb-3">
                <div class="col-md-12">
                    <div class="card summary-card">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="summary-stat">
                                        <h4>${summary.total_accounts}</h4>
                                        <p>Tổng tài khoản</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="summary-stat">
                                        <h4 class="text-success">${summary.valid_accounts}</h4>
                                        <p>Tài khoản hợp lệ</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="summary-stat">
                                        <h4 class="text-warning">${summary.invalid_accounts}</h4>
                                        <p>Tài khoản không tồn tại</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="summary-stat">
                                        <h4>${summary.total_coins.toLocaleString()}</h4>
                                        <p>Tổng kim cương</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        html += `
            <div class="table-responsive">
                <table class="table table-bordered preview-table">
                    <thead>
                        <tr>
                            <th>Username</th>
                            <th>Trạng thái</th>
                            <th>Email</th>
                            <th>Kim cương hiện tại</th>
                            <th>Sẽ nạp</th>
                            <th>Tổng sau nạp</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        previewData.forEach(account => {
            const rowClass = account.exists ? 'account-exists' : 'account-not-exists';
            const status = account.exists ? 
                `<span class="badge badge-success">Tồn tại</span>` : 
                `<span class="badge badge-danger">Không tồn tại</span>`;
            
            html += `
                <tr class="${rowClass}">
                    <td><strong>${account.username}</strong></td>
                    <td>${status}</td>
                    <td>${account.email || '-'}</td>
                    <td>${account.current_coins.toLocaleString()}</td>
                    <td>${account.coins_to_add.toLocaleString()}</td>
                    <td><strong>${account.new_total.toLocaleString()}</strong></td>
                </tr>
            `;
        });

        html += `
                    </tbody>
                </table>
            </div>
        `;

        previewContent.innerHTML = html;
    }
});
</script>
@endsection
