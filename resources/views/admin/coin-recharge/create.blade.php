@extends('layouts.admin')

@section('title', '<PERSON><PERSON><PERSON> kim c<PERSON> - MU Admin Panel')

@section('styles')
<style>
        .breadcrumb {
            color: white;
            margin-bottom: 20px;
            opacity: 0.8;
        }
        .breadcrumb a {
            color: white;
            text-decoration: none;
        }
        .breadcrumb a:hover {
            text-decoration: underline;
        }
        .recharge-form {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(16px);
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 30px;
            color: white;
        }
        .form-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .form-header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }
        .form-header p {
            opacity: 0.8;
        }
        .form-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 25px;
            margin-bottom: 30px;
        }
        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        .form-group label {
            font-weight: 600;
            font-size: 14px;
            color: white;
        }
        .form-control {
            padding: 12px 16px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 14px;
            transition: all 0.2s;
        }
        .form-control:focus {
            outline: none;
            border-color: #3b82f6;
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        .form-control::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }
        .account-search {
            position: relative;
        }
        .search-results {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            margin-top: 2px;
            max-height: 250px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
        }

        .search-results.show {
            display: block;
        }

        .search-loading,
        .search-empty {
            padding: 12px 16px;
            text-align: center;
            color: #666;
            font-size: 14px;
            display: none;
        }

        .search-loading.show,
        .search-empty.show {
            display: block;
        }

        .search-result-item {
            padding: 12px 16px;
            cursor: pointer;
            border-bottom: 1px solid #eee;
            transition: background-color 0.2s;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .search-result-item:hover {
            background-color: #f0f9ff;
        }

        .search-result-item:last-child {
            border-bottom: none;
        }

        .search-result-username {
            font-weight: 600;
            color: #1f2937;
        }

        .search-result-diamonds {
            color: #8b5cf6;
            font-size: 12px;
            font-weight: 500;
        }
        .account-info {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 25px;
            display: none;
        }
        .account-info h3 {
            margin-bottom: 15px;
            color: #3b82f6;
        }
        .info-row {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        .info-row:last-child {
            border-bottom: none;
        }
        .info-label {
            font-weight: 500;
            opacity: 0.8;
        }
        .info-value {
            font-weight: 600;
        }
        .amount-calculator {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 25px;
        }
        .calculator-title {
            font-weight: 600;
            margin-bottom: 15px;
            color: #8b5cf6;
        }
        .quick-amounts {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
            margin-bottom: 15px;
        }
        .quick-amount-btn {
            padding: 10px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            color: white;
            cursor: pointer;
            text-align: center;
            transition: all 0.2s;
        }
        .quick-amount-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }
        .conversion-info {
            font-size: 14px;
            opacity: 0.8;
            text-align: center;
        }
        .form-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
        }
        .btn {
            padding: 12px 30px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            font-size: 14px;
        }
        .btn-primary {
            background: linear-gradient(45deg, #3b82f6, #2563eb);
            color: white;
        }
        .btn-secondary {
            background: rgba(107, 114, 128, 0.8);
            color: white;
        }
        .btn:hover {
            transform: translateY(-2px);
        }
        .warning-box {
            background: rgba(245, 158, 11, 0.2);
            border: 1px solid rgba(245, 158, 11, 0.3);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 25px;
            color: #fbbf24;
        }
        .warning-box h4 {
            margin-bottom: 8px;
        }
        .alert alert-error {
            background: rgba(239, 68, 68, 0.2);
            border: 1px solid rgba(239, 68, 68, 0.3);
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }
</style>
@endsection

@section('content')
</div>

    <!-- Main Content -->
    <div class="container">
        <!-- Breadcrumb -->
        <div class="breadcrumb">
            <a href="/admin/dashboard">Dashboard</a> /
            <a href="/admin/coin-recharge">Quản lý nạp kim cương</a> /
            Nạp kim cương mới
        </div>

        <!-- Error Messages -->
        @if($errors->any())
            <div class="alert alert-error">
                <h4>❌ Có lỗi xảy ra:</h4>
                <ul style="margin-left: 20px; margin-top: 10px;">
                    @foreach($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        <!-- Recharge Form -->
        <div class="recharge-form">
            <div class="form-header">
                <h1>💎 Nạp kim cương</h1>
                <p>Nạp kim cương trực tiếp vào tài khoản người chơi</p>
            </div>

            <!-- Warning -->
            <div class="warning-box">
                <h4>⚠️ Lưu ý quan trọng</h4>
                <p>Việc nạp kim cương sẽ được ghi lại đầy đủ trong hệ thống. Hãy kiểm tra kỹ thông tin trước khi thực hiện.</p>
            </div>

            <!-- Form -->
            <form action="{{ route('admin.coin-recharge.store') }}" method="POST" id="rechargeForm">
                @csrf
                <div class="form-grid">
                    <!-- Recharge Category -->
                    <div class="form-group">
                        <label for="recharge_category">Loại nạp tiền *</label>
                        <select id="recharge_category" name="recharge_category" class="form-control" required>
                            <option value="">-- Chọn loại nạp tiền --</option>
                            <option value="customer" {{ old('recharge_category') === 'customer' ? 'selected' : '' }}>
                                💰 Nạp cho khách hàng
                            </option>
                            <option value="founder_team" {{ old('recharge_category') === 'founder_team' ? 'selected' : '' }}>
                                👥 Nạp cho team sáng lập
                            </option>
                        </select>
                        <small style="opacity: 0.7; font-size: 12px;">Chọn loại để phân biệt doanh thu khách hàng và team</small>
                    </div>

                    <!-- Account Search -->
                    <div class="form-group">
                        <label for="username">Tên tài khoản *</label>
                        <div class="account-search">
                            <input type="text" id="username" name="username" class="form-control"
                                   placeholder="Nhập tên tài khoản..."
                                   value="{{ old('username') }}" required autocomplete="off">
                            <div class="search-results" id="searchResults">
                                <div class="search-loading">
                                    <i class="fas fa-spinner fa-spin"></i> Đang tìm kiếm...
                                </div>
                                <div class="search-list"></div>
                                <div class="search-empty">
                                    Không tìm thấy tài khoản
                                </div>
                            </div>
                        </div>
                        <small style="opacity: 0.7; font-size: 12px;">Nhập tên tài khoản để tìm kiếm</small>
                    </div>

                    <!-- Account Info Display -->
                    <div class="account-info" id="accountInfo">
                        <h3>📋 Thông tin tài khoản</h3>
                        <div class="info-row">
                            <span class="info-label">Email:</span>
                            <span class="info-value" id="accountEmail">-</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">Kim cương hiện tại:</span>
                            <span class="info-value" id="accountBalance">-</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">VIP Level:</span>
                            <span class="info-value" id="accountVip">-</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">Trạng thái:</span>
                            <span class="info-value" id="accountStatus">-</span>
                        </div>
                    </div>

                    <!-- Amount Calculator -->
                    <div class="amount-calculator">
                        <div class="calculator-title">💵 Tính toán số tiền</div>
                        <div class="quick-amounts">
                            <div class="quick-amount-btn" onclick="setAmount(50000, 50000)">50,000đ</div>
                            <div class="quick-amount-btn" onclick="setAmount(100000, 100000)">100,000đ</div>
                            <div class="quick-amount-btn" onclick="setAmount(200000, 200000)">200,000đ</div>
                            <div class="quick-amount-btn" onclick="setAmount(500000, 500000)">500,000đ</div>
                            <div class="quick-amount-btn" onclick="setAmount(1000000, 1000000)">1,000,000đ</div>
                            <div class="quick-amount-btn" onclick="setAmount(2000000, 2000000)">2,000,000đ</div>
                        </div>
                        <div class="conversion-info">
                            💎 Tỷ lệ chuyển đổi: 1đ = 1 kim cương
                        </div>
                    </div>

                    <!-- Amount Input -->
                    <div class="form-group">
                        <label for="amount_vnd">Số tiền nạp (VNĐ) *</label>
                        <input type="number" id="amount_vnd" name="amount_vnd" class="form-control"
                               placeholder="Nhập số tiền..."
                               value="{{ old('amount_vnd') }}"
                               min="1000" max="100000000" required>
                        <small style="opacity: 0.7; font-size: 12px;">Tối thiểu 1,000đ - Tối đa 100,000,000đ</small>
                    </div>

                    <!-- Diamonds Added -->
                    <div class="form-group">
                        <label for="coins_added">Số kim cương nhận được *</label>
                        <input type="number" id="coins_added" name="coins_added" class="form-control"
                               placeholder="Số kim cương sẽ được cộng vào tài khoản..."
                               value="{{ old('coins_added') }}"
                               min="1" max="1000000" required>
                        <small style="opacity: 0.7; font-size: 12px;">Số kim cương thực tế sẽ được cộng vào tài khoản website</small>
                    </div>

                    <!-- Note -->
                    <div class="form-group">
                        <label for="note">Ghi chú</label>
                        <textarea id="note" name="note" class="form-control" rows="3"
                                  placeholder="Nhập lý do nạp kim cương, ghi chú...">{{ old('note') }}</textarea>
                        <small style="opacity: 0.7; font-size: 12px;">Ghi chú sẽ được lưu trong lịch sử giao dịch</small>
                    </div>
                </div>

                <div class="form-buttons">
                    <a href="{{ route('admin.coin-recharge.index') }}" class="btn btn-secondary">
                        ❌ Hủy bỏ
                    </a>
                    <button type="submit" class="btn btn-primary" id="submitBtn">
                        ✅ Nạp kim cương
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        let searchTimeout;
        let selectedAccount = null;

        // Account search functionality
        document.getElementById('username').addEventListener('input', function() {
            const username = this.value.trim();
            
            if (username.length < 2) {
                hideSearchResults();
                hideAccountInfo();
                return;
            }

            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                searchAccount(username);
            }, 300);
        });

        // Hide dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (!e.target.closest('#username') && !e.target.closest('#searchResults')) {
                hideSearchResults();
            }
        });

        function searchAccount(username) {
            const searchResults = document.getElementById('searchResults');
            const loadingDiv = searchResults.querySelector('.search-loading');
            const listDiv = searchResults.querySelector('.search-list');
            const emptyDiv = searchResults.querySelector('.search-empty');

            // Show loading
            searchResults.classList.add('show');
            loadingDiv.classList.add('show');
            listDiv.innerHTML = '';
            emptyDiv.classList.remove('show');

            fetch(`{{ route('admin.coin-recharge.search-account') }}?username=${encodeURIComponent(username)}`, {
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                loadingDiv.classList.remove('show');

                if (data.success && data.accounts && data.accounts.length > 0) {
                    showSearchResults(data.accounts);
                } else {
                    emptyDiv.classList.add('show');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                loadingDiv.classList.remove('show');
                emptyDiv.classList.add('show');
            });
        }

        function showSearchResults(accounts) {
            const listDiv = document.querySelector('.search-list');
            listDiv.innerHTML = '';

            // Add header with count
            if (accounts.length > 0) {
                const header = document.createElement('div');
                header.style.cssText = 'padding: 8px 16px; background: #f8f9fa; border-bottom: 1px solid #eee; font-size: 12px; color: #666; font-weight: 500;';
                header.textContent = `Tìm thấy ${accounts.length} tài khoản`;
                listDiv.appendChild(header);
            }

            accounts.forEach(account => {
                const item = document.createElement('div');
                item.className = 'search-result-item';
                item.innerHTML = `
                    <span class="search-result-username">${account.username}</span>
                    <span class="search-result-diamonds">${parseInt(account.current_coins || 0).toLocaleString()} 💎</span>
                `;

                item.addEventListener('click', function() {
                    selectAccount(account);
                });

                listDiv.appendChild(item);
            });
        }

        function selectAccount(account) {
            // Fill username
            document.getElementById('username').value = account.username;

            // Show account info
            document.getElementById('accountEmail').textContent = account.email || 'Không có';
            document.getElementById('accountBalance').textContent = `${parseInt(account.current_coins || 0).toLocaleString()} 💎`;
            document.getElementById('accountVip').textContent = 'N/A';
            document.getElementById('accountStatus').textContent = account.status == 1 ? 'Hoạt động' : 'Bị khóa';
            document.getElementById('accountInfo').style.display = 'block';

            // Hide search results
            hideSearchResults();
            selectedAccount = account;
        }

        function hideSearchResults() {
            const searchResults = document.getElementById('searchResults');
            searchResults.classList.remove('show');
            searchResults.querySelector('.search-loading').classList.remove('show');
            searchResults.querySelector('.search-empty').classList.remove('show');
            searchResults.querySelector('.search-list').innerHTML = '';
        }

        function hideAccountInfo() {
            document.getElementById('accountInfo').style.display = 'none';
            selectedAccount = null;
        }

        // Amount calculation
        function setAmount(amount, coins) {
            document.getElementById('amount_vnd').value = amount;
            document.getElementById('coins_added').value = coins;
        }

        // Auto-calculate coins when amount changes
        document.getElementById('amount_vnd').addEventListener('input', function() {
            const amount = parseInt(this.value) || 0;
            document.getElementById('coins_added').value = amount; // 1:1 ratio
        });

        // Form validation
        document.getElementById('rechargeForm').addEventListener('submit', function(e) {
            const username = document.getElementById('username').value.trim();
            const amount = parseInt(document.getElementById('amount_vnd').value);
            const coins = parseInt(document.getElementById('coins_added').value);
            const note = document.getElementById('note').value.trim();
            const category = document.getElementById('recharge_category').value;

            if (!category) {
                alert('Vui lòng chọn loại nạp tiền');
                e.preventDefault();
                return;
            }

            if (!username) {
                alert('Vui lòng nhập tên tài khoản');
                e.preventDefault();
                return;
            }

            if (amount < 1000 || amount > 100000000) {
                alert('Số tiền phải từ 1,000đ đến 100,000,000đ');
                e.preventDefault();
                return;
            }

            if (coins < 1 || coins > 1000000) {
                alert('Số coin phải từ 1 đến 1,000,000');
                e.preventDefault();
                return;
            }

            if (!note) {
                alert('Vui lòng nhập ghi chú');
                e.preventDefault();
                return;
            }

            const categoryText = category === 'founder_team' ? 'team sáng lập' : 'khách hàng';

            // Confirm before submit
            if (!confirm(`Bạn có chắc chắn muốn nạp ${coins.toLocaleString()} kim cương cho ${categoryText} - tài khoản "${username}"?\n\nLoại: ${categoryText}\nSố tiền: ${amount.toLocaleString()}đ\nKim cương nhận: ${coins.toLocaleString()}\nGhi chú: ${note}`)) {
                e.preventDefault();
                return;
            }

            // Show loading state
            document.getElementById('submitBtn').textContent = '⏳ Đang xử lý...';
            document.getElementById('submitBtn').disabled = true;
        });

        // Format number inputs
        document.querySelectorAll('input[type="number"]').forEach(input => {
            input.addEventListener('blur', function() {
                if (this.value) {
                    this.value = parseInt(this.value);
                }
            });
        });
    </script>
@endsection
