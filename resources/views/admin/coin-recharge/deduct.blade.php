@extends('layouts.admin')

@section('title', 'Trừ <PERSON>')

@section('styles')
<style>
    .deduct-page {
        min-height: 100vh;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 20px 0;
    }

    .page-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(16px);
        border-radius: 15px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        padding: 30px;
        margin-bottom: 30px;
        color: white;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .page-title {
        font-size: 28px;
        font-weight: 700;
        margin-bottom: 10px;
    }

    .page-subtitle {
        opacity: 0.8;
        font-size: 16px;
    }

    .btn {
        padding: 12px 24px;
        border: none;
        border-radius: 8px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s;
        text-decoration: none;
        display: inline-block;
        text-align: center;
        font-size: 14px;
    }

    .btn-primary {
        background: linear-gradient(45deg, #3b82f6, #2563eb);
        color: white;
    }

    .btn:hover {
        transform: translateY(-2px);
    }

    .form-container {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(16px);
        border-radius: 15px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        padding: 30px;
        margin-bottom: 30px;
    }

    .form-title {
        color: white;
        font-size: 20px;
        font-weight: 600;
        margin-bottom: 25px;
        text-align: center;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-group label {
        color: white;
        font-weight: 500;
        font-size: 14px;
        margin-bottom: 8px;
        display: block;
    }

    .form-control {
        padding: 12px 16px;
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 8px;
        background: rgba(255, 255, 255, 0.1);
        color: white;
        font-size: 14px;
        width: 100%;
        transition: all 0.3s ease;
    }

    .form-control:focus {
        outline: none;
        border-color: #3b82f6;
        background: rgba(255, 255, 255, 0.15);
        box-shadow: 0 0 0 0.25rem rgba(59, 130, 246, 0.25);
    }

    .form-control::placeholder {
        color: rgba(255, 255, 255, 0.6);
    }

    .btn-danger {
        background: linear-gradient(45deg, #ef4444, #dc2626);
        color: white;
        width: 100%;
        padding: 15px;
        font-size: 16px;
        margin-top: 10px;
    }

    .btn-secondary {
        background: linear-gradient(45deg, #6b7280, #4b5563);
        color: white;
    }

    .alert {
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 20px;
    }

    .alert-success {
        background: rgba(16, 185, 129, 0.2);
        border: 1px solid rgba(16, 185, 129, 0.3);
        color: white;
    }

    .alert-danger {
        background: rgba(239, 68, 68, 0.2);
        border: 1px solid rgba(239, 68, 68, 0.3);
        color: white;
    }

    .text-danger {
        color: #fca5a5 !important;
    }

    /* Suggestions Dropdown */
    .suggestions-dropdown {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        z-index: 1000;
        max-height: 200px;
        overflow-y: auto;
        display: none;
        margin-top: 2px;
    }

    .suggestions-dropdown.show {
        display: block;
    }

    .suggestions-loading,
    .suggestions-empty {
        padding: 12px 16px;
        text-align: center;
        color: #666;
        font-size: 14px;
        display: none;
    }

    .suggestions-loading.show,
    .suggestions-empty.show {
        display: block;
    }

    .suggestion-item {
        padding: 10px 16px;
        border-bottom: 1px solid #eee;
        cursor: pointer;
        transition: background-color 0.2s;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .suggestion-item:last-child {
        border-bottom: none;
    }

    .suggestion-item:hover {
        background-color: #f0f9ff;
    }

    .suggestion-username {
        font-weight: 600;
        color: #1f2937;
    }

    .suggestion-diamonds {
        color: #8b5cf6;
        font-size: 12px;
        font-weight: 500;
    }

    /* Mobile Responsive */
    @media (max-width: 768px) {
        .page-header {
            flex-direction: column;
            gap: 15px;
            align-items: stretch;
            padding: 20px;
        }

        .form-container {
            padding: 20px;
        }

        .btn {
            width: 100%;
            margin-bottom: 10px;
        }
    }
</style>
@endsection

@section('content')
<div class="deduct-page">
    <div class="container">
        <!-- Page Header -->
        <div class="page-header">
            <div>
                <h1 class="page-title">➖ Trừ Kim Cương Tài Khoản</h1>
                <p class="page-subtitle">Trừ kim cương từ tài khoản người chơi với lý do cụ thể</p>
            </div>
            <div>
                <a href="{{ route('admin.coin-recharge.index') }}" class="btn btn-secondary">← Quay lại</a>
            </div>
        </div>

        <!-- Success/Error Messages -->
        @if(session('success'))
            <div class="alert alert-success">
                ✅ {{ session('success') }}
            </div>
        @endif

        @if($errors->any())
            <div class="alert alert-danger">
                ❌ Có lỗi xảy ra:
                <ul style="margin: 10px 0 0 20px;">
                    @foreach($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        <!-- Deduct Form -->
        <div class="form-container">
            <h3 class="form-title">📝 Thông tin trừ kim cương</h3>
            
            <form action="{{ route('admin.coin-recharge.deduct') }}" method="POST">
                @csrf
                
                <div class="form-group">
                    <label for="username">Username <span class="text-danger">*</span></label>
                    <div style="position: relative;">
                        <input type="text"
                               class="form-control @error('username') is-invalid @enderror"
                               id="username"
                               name="username"
                               value="{{ old('username') }}"
                               placeholder="Nhập username cần trừ kim cương"
                               autocomplete="off"
                               required>

                        <!-- Dropdown suggestions -->
                        <div id="userSuggestions" class="suggestions-dropdown">
                            <div class="suggestions-loading">
                                <i class="fas fa-spinner fa-spin"></i> Đang tìm kiếm...
                            </div>
                            <div class="suggestions-list"></div>
                            <div class="suggestions-empty">
                                Không tìm thấy tài khoản
                            </div>
                        </div>
                    </div>
                    @error('username')
                        <div class="text-danger mt-1">{{ $message }}</div>
                    @enderror
                </div>

                <div class="form-group">
                    <label for="coins">Số kim cương cần trừ <span class="text-danger">*</span></label>
                    <input type="number"
                           class="form-control @error('coins') is-invalid @enderror"
                           id="coins"
                           name="coins"
                           value="{{ old('coins') }}"
                           placeholder="Nhập số kim cương cần trừ"
                           min="1"
                           required>
                    @error('coins')
                        <div class="text-danger mt-1">{{ $message }}</div>
                    @enderror
                </div>

                <div class="form-group">
                    <label for="reason">Lý do trừ kim cương <span class="text-danger">*</span></label>
                    <textarea class="form-control @error('reason') is-invalid @enderror"
                              id="reason"
                              name="reason"
                              rows="4"
                              placeholder="Nhập lý do trừ kim cương (ví dụ: Vi phạm quy định, hoàn tiền, điều chỉnh...)"
                              required>{{ old('reason') }}</textarea>
                    @error('reason')
                        <div class="text-danger mt-1">{{ $message }}</div>
                    @enderror
                </div>

                <button type="submit" class="btn btn-danger">
                    ➖ Xác nhận trừ kim cương
                </button>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const usernameInput = document.getElementById('username');
    const suggestionsDropdown = document.getElementById('userSuggestions');
    const loadingDiv = suggestionsDropdown.querySelector('.suggestions-loading');
    const listDiv = suggestionsDropdown.querySelector('.suggestions-list');
    const emptyDiv = suggestionsDropdown.querySelector('.suggestions-empty');

    let searchTimeout = null;

    // Auto-suggest when typing
    usernameInput.addEventListener('input', function() {
        const query = this.value.trim();

        // Clear previous timeout
        if (searchTimeout) {
            clearTimeout(searchTimeout);
        }

        // Hide dropdown if query too short
        if (query.length < 2) {
            hideSuggestions();
            return;
        }

        // Search after 300ms delay
        searchTimeout = setTimeout(() => {
            searchUsers(query);
        }, 300);
    });

    // Hide dropdown when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.closest('#username') && !e.target.closest('#userSuggestions')) {
            hideSuggestions();
        }
    });

    function searchUsers(query) {
        showLoading();

        fetch(`{{ route('admin.coin-recharge.search-account') }}?username=${encodeURIComponent(query)}`, {
            method: 'GET',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();

            if (data.success && data.accounts && data.accounts.length > 0) {
                showSuggestions(data.accounts);
            } else {
                showEmpty();
            }
        })
        .catch(error => {
            console.error('Search error:', error);
            hideLoading();
            showEmpty();
        });
    }

    function showLoading() {
        suggestionsDropdown.classList.add('show');
        loadingDiv.classList.add('show');
        listDiv.innerHTML = '';
        emptyDiv.classList.remove('show');
    }

    function hideLoading() {
        loadingDiv.classList.remove('show');
    }

    function showSuggestions(accounts) {
        listDiv.innerHTML = '';

        // Add header with count
        if (accounts.length > 0) {
            const header = document.createElement('div');
            header.style.cssText = 'padding: 8px 16px; background: #f8f9fa; border-bottom: 1px solid #eee; font-size: 12px; color: #666; font-weight: 500;';
            header.textContent = `Tìm thấy ${accounts.length} tài khoản`;
            listDiv.appendChild(header);
        }

        accounts.forEach(account => {
            const item = document.createElement('div');
            item.className = 'suggestion-item';
            item.innerHTML = `
                <span class="suggestion-username">${account.username}</span>
                <span class="suggestion-diamonds">${parseInt(account.current_coins || 0).toLocaleString()} 💎</span>
            `;

            item.addEventListener('click', function() {
                usernameInput.value = account.username;
                hideSuggestions();
                usernameInput.focus();
            });

            listDiv.appendChild(item);
        });

        suggestionsDropdown.classList.add('show');
        emptyDiv.classList.remove('show');
    }

    function showEmpty() {
        suggestionsDropdown.classList.add('show');
        emptyDiv.classList.add('show');
        listDiv.innerHTML = '';
    }

    function hideSuggestions() {
        suggestionsDropdown.classList.remove('show');
        loadingDiv.classList.remove('show');
        emptyDiv.classList.remove('show');
        listDiv.innerHTML = '';
    }
});
</script>
@endsection
