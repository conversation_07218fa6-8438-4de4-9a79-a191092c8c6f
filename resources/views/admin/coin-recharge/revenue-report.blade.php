@extends('layouts.admin')

@section('title', 'Báo cáo doanh thu - MU Admin Panel')

@section('styles')
<style>
    .breadcrumb {
        color: white;
        margin-bottom: 20px;
        opacity: 0.8;
    }
    .breadcrumb a {
        color: white;
        text-decoration: none;
    }
    .breadcrumb a:hover {
        text-decoration: underline;
    }
    .page-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(16px);
        border-radius: 15px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        padding: 30px;
        margin-bottom: 30px;
        color: white;
        text-align: center;
    }
    .page-title {
        font-size: 28px;
        font-weight: 700;
        margin-bottom: 10px;
    }
    .page-subtitle {
        opacity: 0.8;
        font-size: 16px;
    }
    .filter-section {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(16px);
        border-radius: 15px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        padding: 25px;
        margin-bottom: 30px;
    }
    .filter-form {
        display: flex;
        gap: 15px;
        align-items: end;
        flex-wrap: wrap;
    }
    .form-group {
        display: flex;
        flex-direction: column;
        gap: 5px;
    }
    .form-group label {
        color: white;
        font-weight: 500;
        font-size: 14px;
    }
    .form-control {
        padding: 12px 16px;
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 8px;
        background: rgba(255, 255, 255, 0.1);
        color: white;
        font-size: 14px;
        min-width: 150px;
    }
    .form-control:focus {
        outline: none;
        border-color: #3b82f6;
        background: rgba(255, 255, 255, 0.15);
    }
    .btn {
        padding: 12px 24px;
        border: none;
        border-radius: 8px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s;
        text-decoration: none;
        display: inline-block;
        text-align: center;
        font-size: 14px;
    }
    .btn-primary {
        background: linear-gradient(45deg, #3b82f6, #2563eb);
        color: white;
    }
    .btn:hover {
        transform: translateY(-2px);
    }
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }
    .stat-section {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(16px);
        border-radius: 15px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        padding: 25px;
        color: white;
    }
    .section-title {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 20px;
        text-align: center;
        padding-bottom: 10px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    }
    .customer-section .section-title {
        color: #10b981;
    }
    .founder-section .section-title {
        color: #8b5cf6;
    }
    .total-section .section-title {
        color: #f59e0b;
    }
    .stat-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }
    .stat-item:last-child {
        border-bottom: none;
    }
    .stat-label {
        font-weight: 500;
        opacity: 0.8;
    }
    .stat-value {
        font-weight: 700;
        font-size: 16px;
    }
    .customer-section .stat-value {
        color: #10b981;
    }
    .founder-section .stat-value {
        color: #8b5cf6;
    }
    .total-section .stat-value {
        color: #f59e0b;
    }
    .chart-section {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(16px);
        border-radius: 15px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        padding: 25px;
        margin-bottom: 30px;
        color: white;
    }
    .daily-breakdown {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(16px);
        border-radius: 15px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        overflow: hidden;
    }
    .table-header {
        background: rgba(255, 255, 255, 0.1);
        padding: 20px 25px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    }
    .table-title {
        color: white;
        font-size: 18px;
        font-weight: 600;
    }
    .table-responsive {
        overflow-x: auto;
        max-height: 60vh;
        overflow-y: auto;
    }
    table {
        width: 100%;
        border-collapse: collapse;
        min-width: 800px;
    }
    th, td {
        padding: 15px 20px;
        text-align: left;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        white-space: nowrap;
    }
    th {
        background: rgba(255, 255, 255, 0.15);
        color: white;
        font-weight: 600;
        font-size: 14px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        position: sticky;
        top: 0;
        z-index: 10;
        backdrop-filter: blur(16px);
    }
    td {
        color: white;
        font-size: 14px;
    }
    tr:hover {
        background: rgba(255, 255, 255, 0.05);
    }
    .amount-customer {
        color: #10b981;
        font-weight: 600;
    }
    .amount-founder {
        color: #8b5cf6;
        font-weight: 600;
    }
    .amount-total {
        color: #f59e0b;
        font-weight: 600;
    }
    .no-data {
        text-align: center;
        padding: 60px 20px;
        color: white;
        opacity: 0.7;
    }

    /* Mobile Responsive */
    @media (max-width: 768px) {
        .filter-form {
            flex-direction: column;
            align-items: stretch;
        }
        .form-control {
            min-width: auto;
            width: 100%;
        }
        .btn {
            width: 100%;
            margin-top: 10px;
        }
        .stats-grid {
            grid-template-columns: 1fr;
            gap: 15px;
        }
        th, td {
            padding: 10px 8px;
            font-size: 12px;
        }
    }
</style>
@endsection

@section('content')
<div class="container">
    <!-- Breadcrumb -->
    <div class="breadcrumb">
        <a href="/admin/dashboard">Dashboard</a> /
        <a href="/admin/coin-recharge">Quản lý nạp coin</a> /
        Báo cáo doanh thu
    </div>

    <!-- Page Header -->
    <div class="page-header">
        <h1 class="page-title">📊 Báo cáo doanh thu nạp coin</h1>
        <p class="page-subtitle">Phân tích doanh thu từ khách hàng và team sáng lập</p>
    </div>

    <!-- Filter Section -->
    <div class="filter-section">
        <form method="GET" action="{{ route('admin.coin-recharge.revenue-report') }}" class="filter-form">
            <div class="form-group">
                <label>Khoảng thời gian</label>
                <select name="period" class="form-control" onchange="toggleDateInputs(this.value)">
                    <option value="today" {{ $period == 'today' ? 'selected' : '' }}>Hôm nay</option>
                    <option value="week" {{ $period == 'week' ? 'selected' : '' }}>Tuần này</option>
                    <option value="month" {{ $period == 'month' ? 'selected' : '' }}>Tháng này</option>
                    <option value="year" {{ $period == 'year' ? 'selected' : '' }}>Năm này</option>
                    <option value="custom" {{ $startDate && $endDate ? 'selected' : '' }}>Tùy chọn</option>
                </select>
            </div>
            <div class="form-group" id="startDateGroup" style="{{ $startDate && $endDate ? '' : 'display: none;' }}">
                <label>Từ ngày</label>
                <input type="date" name="start_date" class="form-control" value="{{ $startDate }}">
            </div>
            <div class="form-group" id="endDateGroup" style="{{ $startDate && $endDate ? '' : 'display: none;' }}">
                <label>Đến ngày</label>
                <input type="date" name="end_date" class="form-control" value="{{ $endDate }}">
            </div>
            <button type="submit" class="btn btn-primary">📊 Xem báo cáo</button>
        </form>
    </div>

    <!-- Statistics Grid -->
    <div class="stats-grid">
        <!-- Total Statistics -->
        <div class="stat-section total-section">
            <h3 class="section-title">📈 Tổng doanh thu</h3>
            <div class="stat-item">
                <span class="stat-label">Tổng tiền:</span>
                <span class="stat-value">{{ number_format($totalStats['total_amount']) }}đ</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">Số giao dịch:</span>
                <span class="stat-value">{{ number_format($totalStats['total_transactions']) }}</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">Trung bình/GD:</span>
                <span class="stat-value">{{ number_format($totalStats['avg_amount']) }}đ</span>
            </div>
        </div>

        <!-- Customer Statistics -->
        <div class="stat-section customer-section">
            <h3 class="section-title">💰 Doanh thu khách hàng</h3>
            <div class="stat-item">
                <span class="stat-label">Tổng tiền:</span>
                <span class="stat-value">{{ number_format($customerStats['amount']) }}đ</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">Số giao dịch:</span>
                <span class="stat-value">{{ number_format($customerStats['transactions']) }}</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">Trung bình/GD:</span>
                <span class="stat-value">{{ number_format($customerStats['avg_amount']) }}đ</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">% Tổng doanh thu:</span>
                <span class="stat-value">
                    {{ $totalStats['total_amount'] > 0 ? number_format(($customerStats['amount'] / $totalStats['total_amount']) * 100, 1) : 0 }}%
                </span>
            </div>
        </div>

        <!-- Founder Team Statistics -->
        <div class="stat-section founder-section">
            <h3 class="section-title">👥 Doanh thu team sáng lập</h3>
            <div class="stat-item">
                <span class="stat-label">Tổng tiền:</span>
                <span class="stat-value">{{ number_format($founderStats['amount']) }}đ</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">Số giao dịch:</span>
                <span class="stat-value">{{ number_format($founderStats['transactions']) }}</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">Trung bình/GD:</span>
                <span class="stat-value">{{ number_format($founderStats['avg_amount']) }}đ</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">% Tổng doanh thu:</span>
                <span class="stat-value">
                    {{ $totalStats['total_amount'] > 0 ? number_format(($founderStats['amount'] / $totalStats['total_amount']) * 100, 1) : 0 }}%
                </span>
            </div>
        </div>
    </div>

    <!-- Daily Breakdown -->
    <div class="daily-breakdown">
        <div class="table-header">
            <h3 class="table-title">📅 Chi tiết theo ngày</h3>
        </div>

        @if($dailyBreakdown->count() > 0)
            <div class="table-responsive">
                <table>
                    <thead>
                        <tr>
                            <th>Ngày</th>
                            <th>Khách hàng</th>
                            <th>GD khách</th>
                            <th>Team sáng lập</th>
                            <th>GD team</th>
                            <th>Tổng doanh thu</th>
                            <th>Tổng GD</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($dailyBreakdown as $day)
                            <tr>
                                <td>{{ date('d/m/Y', strtotime($day->date)) }}</td>
                                <td><span class="amount-customer">{{ number_format($day->customer_amount) }}đ</span></td>
                                <td>{{ $day->customer_count }}</td>
                                <td><span class="amount-founder">{{ number_format($day->founder_amount) }}đ</span></td>
                                <td>{{ $day->founder_count }}</td>
                                <td><span class="amount-total">{{ number_format($day->customer_amount + $day->founder_amount) }}đ</span></td>
                                <td>{{ $day->customer_count + $day->founder_count }}</td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @else
            <div class="no-data">
                <h3>📊 Không có dữ liệu trong khoảng thời gian này</h3>
                <p>Hãy thử chọn khoảng thời gian khác hoặc kiểm tra lại dữ liệu.</p>
            </div>
        @endif
    </div>
</div>

<script>
    function toggleDateInputs(period) {
        const startDateGroup = document.getElementById('startDateGroup');
        const endDateGroup = document.getElementById('endDateGroup');

        if (period === 'custom') {
            startDateGroup.style.display = 'flex';
            endDateGroup.style.display = 'flex';
        } else {
            startDateGroup.style.display = 'none';
            endDateGroup.style.display = 'none';
        }
    }
</script>
@endsection
