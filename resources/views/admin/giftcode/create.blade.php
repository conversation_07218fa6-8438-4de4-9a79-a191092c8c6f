@extends('layouts.admin')

@section('title', 'Tạo Giftcode mới')

@section('content')
<div class="giftcode-create-container">
    <!-- Header Section -->
    <div class="page-header-modern">
        <div class="header-content">
            <div class="header-icon">
                <i class="fas fa-gift"></i>
            </div>
            <div class="header-text">
                <h1 class="page-title">Tạo Giftcode Mới</h1>
                <p class="page-subtitle">Tạo mã quà tặng cho người chơi MU Online</p>
            </div>
        </div>
        <div class="header-actions">
            <a href="{{ route('admin.giftcode.index') }}" class="btn btn-glass">
                <i class="fas fa-arrow-left"></i>
                <span>Quay lại</span>
            </a>
        </div>
    </div>

    <form method="POST" action="{{ route('admin.giftcode.store') }}" class="giftcode-form">
        @csrf

        <!-- Basic Information Card -->
        <div class="form-card">
            <div class="card-header-modern">
                <div class="card-icon">
                    <i class="fas fa-info-circle"></i>
                </div>
                <h3 class="card-title">Thông tin cơ bản</h3>
            </div>
            <div class="card-body-modern">
                <div class="form-grid">
                    <div class="form-group-modern">
                        <label for="type" class="form-label-modern">
                            <i class="fas fa-tag"></i>
                            Loại Giftcode
                            <span class="required">*</span>
                        </label>
                        <select class="form-input-modern @error('type') is-invalid @enderror"
                                id="type"
                                name="type">
                            <option value="1" {{ old('type', 1) == 1 ? 'selected' : '' }}>Công khai - Ai cũng dùng được</option>
                            <option value="2" {{ old('type') == 2 ? 'selected' : '' }}>Riêng tư - Chỉ tài khoản cụ thể</option>
                            <option value="0" {{ old('type') == 0 ? 'selected' : '' }}>Theo nhân vật - Mỗi nhân vật dùng 1 lần</option>
                        </select>
                        @error('type')
                            <div class="error-message">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-group-modern">
                        <label for="limit" class="form-label-modern">
                            <i class="fas fa-users"></i>
                            Giới hạn sử dụng
                            <span class="required">*</span>
                        </label>
                        <input type="number"
                               class="form-input-modern @error('limit') is-invalid @enderror"
                               id="limit"
                               name="limit"
                               value="{{ old('limit', 0) }}"
                               min="0"
                               placeholder="0 = Không giới hạn">
                        @error('limit')
                            <div class="error-message">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <div class="form-grid">
                    <div class="form-group-modern">
                        <label for="period" class="form-label-modern">
                            <i class="fas fa-clock"></i>
                            Thời hạn (ngày)
                            <span class="required">*</span>
                        </label>
                        <input type="number"
                               class="form-input-modern @error('period') is-invalid @enderror"
                               id="period"
                               name="period"
                               value="{{ old('period', 0) }}"
                               min="0"
                               placeholder="0 = Không hết hạn">
                        @error('period')
                            <div class="error-message">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-group-modern">
                        <label for="zoneid" class="form-label-modern">
                            <i class="fas fa-server"></i>
                            Server áp dụng
                            <span class="required">*</span>
                        </label>
                        <select class="form-input-modern @error('zoneid') is-invalid @enderror"
                                id="zoneid"
                                name="zoneid">
                            <option value="0" {{ old('zoneid', 0) == 0 ? 'selected' : '' }}>Tất cả server</option>
                            <option value="1" {{ old('zoneid') == 1 ? 'selected' : '' }}>Server 1</option>
                            <option value="2" {{ old('zoneid') == 2 ? 'selected' : '' }}>Server 2</option>
                        </select>
                        @error('zoneid')
                            <div class="error-message">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <div class="form-group-modern full-width">
                    <label for="content" class="form-label-modern">
                        <i class="fas fa-align-left"></i>
                        Nội dung giftcode
                        <span class="required">*</span>
                    </label>
                    <textarea class="form-textarea-modern @error('content') is-invalid @enderror"
                              id="content"
                              name="content"
                              rows="3"
                              placeholder="Nội dung mô tả giftcode này...">{{ old('content') }}</textarea>
                    @error('content')
                        <div class="error-message">{{ $message }}</div>
                    @enderror
                </div>

                <!-- Accounts field for private type -->
                <div class="form-group-modern full-width" id="accounts_section" style="display: none;">
                    <label for="accounts" class="form-label-modern">
                        <i class="fas fa-users"></i>
                        Danh sách tài khoản được phép
                        <span class="required">*</span>
                    </label>
                    <textarea class="form-textarea-modern @error('accounts') is-invalid @enderror"
                              id="accounts"
                              name="accounts"
                              rows="3"
                              placeholder="Nhập tên tài khoản, cách nhau bằng dấu phẩy. Ví dụ: user1,user2,user3">{{ old('accounts') }}</textarea>
                    @error('accounts')
                        <div class="error-message">{{ $message }}</div>
                    @enderror
                </div>
            </div>
        </div>

        <!-- Code Generation Card -->
        <div class="form-card">
            <div class="card-header-modern">
                <div class="card-icon">
                    <i class="fas fa-code"></i>
                </div>
                <h3 class="card-title">Tạo mã Code</h3>
            </div>
            <div class="card-body-modern">
                <div class="radio-group-modern">
                    <label class="form-label-modern">
                        <i class="fas fa-cogs"></i>
                        Loại tạo code
                        <span class="required">*</span>
                    </label>
                    <div class="radio-options">
                        <div class="radio-option">
                            <input type="radio"
                                   name="multiple"
                                   id="single_code"
                                   value="0"
                                   {{ old('multiple', '0') == '0' ? 'checked' : '' }}>
                            <label for="single_code" class="radio-label">
                                <div class="radio-icon">
                                    <i class="fas fa-file-alt"></i>
                                </div>
                                <div class="radio-content">
                                    <span class="radio-title">Code duy nhất</span>
                                    <span class="radio-desc">Tạo một mã code đơn lẻ</span>
                                </div>
                            </label>
                        </div>
                        <div class="radio-option">
                            <input type="radio"
                                   name="multiple"
                                   id="multiple_codes"
                                   value="1"
                                   {{ old('multiple') == '1' ? 'checked' : '' }}>
                            <label for="multiple_codes" class="radio-label">
                                <div class="radio-icon">
                                    <i class="fas fa-copy"></i>
                                </div>
                                <div class="radio-content">
                                    <span class="radio-title">Nhiều code</span>
                                    <span class="radio-desc">Tạo nhiều mã code cùng lúc</span>
                                </div>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Single Code Section -->
                <div id="single_code_section" class="code-section">
                    <div class="form-group-modern">
                        <label for="code" class="form-label-modern">
                            <i class="fas fa-key"></i>
                            Mã Code
                            <span class="required">*</span>
                        </label>
                        <input type="text"
                               class="form-input-modern @error('code') is-invalid @enderror"
                               id="code"
                               name="code"
                               value="{{ old('code') }}"
                               placeholder="Ví dụ: MUTET2025">
                        @error('code')
                            <div class="error-message">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <!-- Multiple Codes Section -->
                <div id="multiple_codes_section" class="code-section" style="display: none;">
                    <div class="form-group-modern">
                        <label for="number" class="form-label-modern">
                            <i class="fas fa-sort-numeric-up"></i>
                            Số lượng Code
                            <span class="required">*</span>
                        </label>
                        <input type="number"
                               class="form-input-modern @error('number') is-invalid @enderror"
                               id="number"
                               name="number"
                               value="{{ old('number', 10) }}"
                               min="1"
                               max="1000"
                               placeholder="100">
                        <small class="form-hint">Hệ thống sẽ tự động tạo mã code ngẫu nhiên</small>
                        @error('number')
                            <div class="error-message">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>
        </div>

        <!-- Items Reward Card with Integrated Interactive Selection -->
        <div class="form-card">
            <div class="card-header-modern">
                <div class="card-icon">
                    <i class="fas fa-gift"></i>
                </div>
                <h3 class="card-title">Vật phẩm thưởng</h3>
            </div>
            <div class="card-body-modern">
                <div class="item-selection-container">
                    <!-- Selection Controls -->
                    <div class="item-selection-header">
                        <div class="selection-title"><i class="fas fa-gem"></i> Chọn Vật Phẩm</div>
                        <div class="selection-controls">
                            <input type="text" id="item_search" class="search-input form-input-modern" placeholder="Tìm kiếm vật phẩm..." />
                            <select id="item_category" class="category-select form-input-modern">
                                <option value="">Tất cả loại</option>
                            </select>
                            <button type="button" class="btn btn-secondary-modern" onclick="clearAllItems()"><i class="fas fa-trash"></i> Clear All</button>
                        </div>
                    </div>
                    <!-- Item ID Notice -->
                    <div class="item-id-notice">
                        <div class="notice-header">
                            <i class="fas fa-info-circle"></i>
                            <span>Thông tin quan trọng về Item ID</span>
                        </div>
                        <div class="notice-content">
                            <div class="notice-grid">
                                <div class="notice-column">
                                    <h6>✅ Item ID đúng (đã cập nhật):</h6>
                                    <ul>
                                        <li><strong>Jewel of Bless:</strong> <code>50014</code></li>
                                        <li><strong>Jewel of Soul:</strong> <code>1015101</code></li>
                                        <li><strong>Jewel of Life:</strong> <code>50016</code></li>
                                        <li><strong>Jewel of Chaos:</strong> <code>50013</code></li>
                                    </ul>
                                </div>
                                <div class="notice-column">
                                    <h6>⚠️ Lưu ý:</h6>
                                    <ul>
                                        <li>Hệ thống đã tự động cập nhật tất cả giftcode cũ</li>
                                        <li>Item ID cũ (14,15,16) sẽ được chuyển đổi tự động</li>
                                        <li>Sử dụng Item ID đúng để đảm bảo vật phẩm hiển thị trong game</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Simple Shopping Cart Interface -->
                    <div class="shopping-cart-interface">
                        <!-- Header Notice -->
                        <div class="interface-header">
                            <h4><i class="fas fa-shopping-cart"></i> Giao diện chọn vật phẩm</h4>
                            <div class="legacy-notice">
                                <div class="notice-icon">ℹ️</div>
                                <div class="notice-content">
                                    <strong>Lưu ý:</strong> Sử dụng Legacy ID (14,15,16...) để tương thích với game hiện tại.
                                    Hệ thống sẽ tự động chuyển đổi khi cần thiết.
                                </div>
                            </div>
                        </div>

                        <!-- Popular Items -->
                        <div class="quick-add-section">
                            <h5><i class="fas fa-star"></i> Vật phẩm phổ biến - Click để thêm</h5>
                            <div class="popular-items-grid">
                                <!-- Jewels (Legacy IDs) -->
                                <div class="popular-item" onclick="quickAddItem(14, 'Jewel of Bless', '💎')">
                                    <div class="item-icon">💎</div>
                                    <div class="item-name">Jewel of Bless</div>
                                    <div class="item-id">ID: 14</div>
                                </div>
                                <div class="popular-item" onclick="quickAddItem(15, 'Jewel of Soul', '💠')">
                                    <div class="item-icon">💠</div>
                                    <div class="item-name">Jewel of Soul</div>
                                    <div class="item-id">ID: 15</div>
                                </div>
                                <div class="popular-item" onclick="quickAddItem(16, 'Jewel of Life', '🔮')">
                                    <div class="item-icon">🔮</div>
                                    <div class="item-name">Jewel of Life</div>
                                    <div class="item-id">ID: 16</div>
                                </div>
                                <div class="popular-item" onclick="quickAddItem(13, 'Jewel of Chaos', '🌀')">
                                    <div class="item-icon">🌀</div>
                                    <div class="item-name">Jewel of Chaos</div>
                                    <div class="item-id">ID: 13</div>
                                </div>
                                <div class="popular-item" onclick="quickAddItem(22, 'Jewel of Creation', '✨')">
                                    <div class="item-icon">✨</div>
                                    <div class="item-name">Jewel of Creation</div>
                                    <div class="item-id">ID: 22</div>
                                </div>
                                <div class="popular-item" onclick="quickAddItem(1011, 'Premium Equipment', '⚔️')">
                                    <div class="item-icon">⚔️</div>
                                    <div class="item-name">Premium Equipment</div>
                                    <div class="item-id">ID: 1011</div>
                                </div>
                            </div>
                        </div>

                        <!-- Category Items -->
                        <div class="category-items-section">
                            <h5><i class="fas fa-th-large"></i> Vật phẩm theo danh mục</h5>
                            <div class="category-tabs">
                                <button type="button" class="category-tab active" onclick="showCategory('currency')">💰 Currency</button>
                                <button type="button" class="category-tab" onclick="showCategory('special')">🎁 Special</button>
                                <button type="button" class="category-tab" onclick="showCategory('equipment')">⚔️ Equipment</button>
                                <button type="button" class="category-tab" onclick="showCategory('consumables')">🧪 Consumables</button>
                            </div>

                            <div class="category-content">
                                <!-- Currency Items -->
                                <div id="category-currency" class="category-items active">
                                    <div class="category-grid">
                                        <div class="popular-item" onclick="quickAddItem(702501, 'Premium Currency', '💰')">
                                            <div class="item-icon">💰</div>
                                            <div class="item-name">Premium Currency</div>
                                            <div class="item-id">ID: 702501</div>
                                        </div>
                                        <div class="popular-item" onclick="quickAddItem(990000, 'Game Coins', '🪙')">
                                            <div class="item-icon">🪙</div>
                                            <div class="item-name">Game Coins</div>
                                            <div class="item-id">ID: 990000</div>
                                        </div>
                                        <div class="popular-item" onclick="quickAddItem(1000, 'Zen Currency', '💵')">
                                            <div class="item-icon">💵</div>
                                            <div class="item-name">Zen Currency</div>
                                            <div class="item-id">ID: 1000</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Special Items -->
                                <div id="category-special" class="category-items">
                                    <div class="category-grid">
                                        <div class="popular-item" onclick="quickAddItem(1015092, 'Special Box', '🎁')">
                                            <div class="item-icon">🎁</div>
                                            <div class="item-name">Special Box</div>
                                            <div class="item-id">ID: 1015092</div>
                                        </div>
                                        <div class="popular-item" onclick="quickAddItem(1015005, 'Rare Item', '🎊')">
                                            <div class="item-icon">🎊</div>
                                            <div class="item-name">Rare Item</div>
                                            <div class="item-id">ID: 1015005</div>
                                        </div>
                                        <div class="popular-item" onclick="quickAddItem(5050, 'Event Item', '🎉')">
                                            <div class="item-icon">🎉</div>
                                            <div class="item-name">Event Item</div>
                                            <div class="item-id">ID: 5050</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Equipment Items -->
                                <div id="category-equipment" class="category-items">
                                    <div class="category-grid">
                                        <div class="popular-item" onclick="quickAddItem(2003, 'Elite Weapon', '⚔️')">
                                            <div class="item-icon">⚔️</div>
                                            <div class="item-name">Elite Weapon</div>
                                            <div class="item-id">ID: 2003</div>
                                        </div>
                                        <div class="popular-item" onclick="quickAddItem(2002, 'Elite Armor', '🛡️')">
                                            <div class="item-icon">🛡️</div>
                                            <div class="item-name">Elite Armor</div>
                                            <div class="item-id">ID: 2002</div>
                                        </div>
                                        <div class="popular-item" onclick="quickAddItem(4000, 'Legendary Gear', '👑')">
                                            <div class="item-icon">👑</div>
                                            <div class="item-name">Legendary Gear</div>
                                            <div class="item-id">ID: 4000</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Consumables -->
                                <div id="category-consumables" class="category-items">
                                    <div class="category-grid">
                                        <div class="popular-item" onclick="quickAddItem(6001, 'Health Potion', '🧪')">
                                            <div class="item-icon">🧪</div>
                                            <div class="item-name">Health Potion</div>
                                            <div class="item-id">ID: 6001</div>
                                        </div>
                                        <div class="popular-item" onclick="quickAddItem(6002, 'Mana Potion', '🔵')">
                                            <div class="item-icon">🔵</div>
                                            <div class="item-name">Mana Potion</div>
                                            <div class="item-id">ID: 6002</div>
                                        </div>
                                        <div class="popular-item" onclick="quickAddItem(6004, 'Buff Scroll', '📜')">
                                            <div class="item-icon">📜</div>
                                            <div class="item-name">Buff Scroll</div>
                                            <div class="item-id">ID: 6004</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Manual Add Item -->
                        <div class="manual-add-section">
                            <h5><i class="fas fa-plus"></i> Thêm vật phẩm thủ công</h5>
                            <div class="add-item-form">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label>Item ID:</label>
                                        <input type="number" id="manual_item_id" placeholder="Ví dụ: 50014" class="form-input">
                                    </div>
                                    <div class="form-group">
                                        <label>Tên vật phẩm:</label>
                                        <input type="text" id="manual_item_name" placeholder="Ví dụ: Jewel of Bless" class="form-input">
                                    </div>
                                    <div class="form-group">
                                        <label>Số lượng:</label>
                                        <input type="number" id="manual_item_quantity" value="1" min="1" max="999" class="form-input">
                                    </div>
                                    <div class="form-group">
                                        <button type="button" onclick="addManualItem()" class="add-btn">
                                            <i class="fas fa-plus"></i> Thêm
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Shopping Cart -->
                        <div class="cart-section">
                            <h5><i class="fas fa-shopping-cart"></i> Giỏ hàng vật phẩm</h5>
                            <div id="cart_items" class="cart-items">
                                <div class="empty-cart">
                                    <i class="fas fa-shopping-cart"></i>
                                    <p>Giỏ hàng trống. Hãy thêm vật phẩm ở trên.</p>
                                </div>
                            </div>
                            <div class="cart-summary">
                                <div class="total-items">Tổng: <span id="total_items">0</span> loại vật phẩm</div>
                                <button type="button" onclick="clearCart()" class="clear-cart-btn">
                                    <i class="fas fa-trash"></i> Xóa tất cả
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Hidden textarea for form submission -->
                    <textarea id="items" name="items" class="item-textarea" style="display:none;">{{ old('items') }}</textarea>
                    @error('items')
                        <div class="error-message">{{ $message }}</div>
                    @enderror
                </div>
            </div>
        </div>

        <!-- Submit Actions -->
        <div class="form-actions">
            <button type="submit" class="btn btn-primary-modern">
                <i class="fas fa-magic"></i>
                <span>Tạo Giftcode</span>
            </button>
            <a href="{{ route('admin.giftcode.index') }}" class="btn btn-secondary-modern">
                <i class="fas fa-times"></i>
                <span>Hủy bỏ</span>
            </a>
        </div>
    </form>
</div>


<!-- Shopping Cart JS -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    let cartItems = [];

    // Quick add item from popular items
    window.quickAddItem = function(itemId, itemName, itemIcon) {
        const existingIndex = cartItems.findIndex(item => item.id == itemId);

        if (existingIndex !== -1) {
            // Item already exists, increase quantity
            cartItems[existingIndex].quantity++;
        } else {
            // Add new item
            cartItems.push({
                id: itemId,
                name: itemName,
                icon: itemIcon,
                quantity: 1
            });
        }

        updateCartDisplay();
        updateHiddenTextarea();
    };

    // Add item manually
    window.addManualItem = function() {
        const itemId = document.getElementById('manual_item_id').value;
        const itemName = document.getElementById('manual_item_name').value;
        const quantity = parseInt(document.getElementById('manual_item_quantity').value) || 1;

        if (!itemId || !itemName) {
            alert('Vui lòng nhập đầy đủ Item ID và tên vật phẩm');
            return;
        }

        const existingIndex = cartItems.findIndex(item => item.id == itemId);

        if (existingIndex !== -1) {
            // Item already exists, increase quantity
            cartItems[existingIndex].quantity += quantity;
        } else {
            // Add new item
            cartItems.push({
                id: itemId,
                name: itemName,
                icon: '📦',
                quantity: quantity
            });
        }

        // Clear form
        document.getElementById('manual_item_id').value = '';
        document.getElementById('manual_item_name').value = '';
        document.getElementById('manual_item_quantity').value = '1';

        updateCartDisplay();
        updateHiddenTextarea();
    };

    // Remove item from cart
    window.removeCartItem = function(itemId) {
        cartItems = cartItems.filter(item => item.id != itemId);
        updateCartDisplay();
        updateHiddenTextarea();
    };

    // Update item quantity
    window.updateItemQuantity = function(itemId, newQuantity) {
        const quantity = Math.max(1, parseInt(newQuantity) || 1);
        const itemIndex = cartItems.findIndex(item => item.id == itemId);

        if (itemIndex !== -1) {
            cartItems[itemIndex].quantity = quantity;
            updateCartDisplay();
            updateHiddenTextarea();
        }
    };

    // Clear entire cart
    window.clearCart = function() {
        if (cartItems.length > 0 && confirm('Bạn có chắc muốn xóa tất cả vật phẩm?')) {
            cartItems = [];
            updateCartDisplay();
            updateHiddenTextarea();
        }
    };

    // Update cart display
    function updateCartDisplay() {
        const cartContainer = document.getElementById('cart_items');
        const totalItemsSpan = document.getElementById('total_items');

        if (cartItems.length === 0) {
            cartContainer.innerHTML = `
                <div class="empty-cart">
                    <i class="fas fa-shopping-cart"></i>
                    <p>Giỏ hàng trống. Hãy thêm vật phẩm ở trên.</p>
                </div>
            `;
            totalItemsSpan.textContent = '0';
            return;
        }

        cartContainer.innerHTML = cartItems.map(item => `
            <div class="cart-item">
                <div class="item-icon">${item.icon}</div>
                <div class="item-info">
                    <div class="item-name">${item.name}</div>
                    <div class="item-id">ID: ${item.id}</div>
                </div>
                <div class="item-quantity">
                    <label>Số lượng:</label>
                    <input type="number" value="${item.quantity}" min="1" max="999"
                           onchange="updateItemQuantity(${item.id}, this.value)" class="quantity-input">
                </div>
                <button type="button" onclick="removeCartItem(${item.id})" class="remove-btn">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `).join('');

        totalItemsSpan.textContent = cartItems.length;
    }

    // Update hidden textarea for form submission
    function updateHiddenTextarea() {
        const textarea = document.getElementById('items');
        textarea.value = cartItems.map(item =>
            `${item.id},${item.quantity},1,0,0,0,0`
        ).join('\n');
    }

    // Toggle accounts section based on giftcode type
    document.getElementById('type').addEventListener('change', function(e) {
        const accountsSection = document.getElementById('accounts_section');

        if (e.target.value == '2') { // Private type
            accountsSection.style.display = 'block';
        } else {
            accountsSection.style.display = 'none';
        }
    });

    // Toggle multiple/single code sections
    document.getElementById('multiple').addEventListener('change', function(e) {
        const multipleSection = document.getElementById('multiple_section');
        const singleCodeSection = document.getElementById('single_code_section');

        if (e.target.checked) {
            multipleSection.style.display = 'block';
            singleCodeSection.style.display = 'none';
        } else {
            multipleSection.style.display = 'none';
            singleCodeSection.style.display = 'block';
        }
    });

    // Category switching
    window.showCategory = function(categoryName) {
        // Hide all category items
        document.querySelectorAll('.category-items').forEach(item => {
            item.classList.remove('active');
        });

        // Remove active class from all tabs
        document.querySelectorAll('.category-tab').forEach(tab => {
            tab.classList.remove('active');
        });

        // Show selected category
        const targetCategory = document.getElementById('category-' + categoryName);
        if (targetCategory) {
            targetCategory.classList.add('active');
        }

        // Add active class to clicked tab
        const clickedTab = event.target.closest('.category-tab');
        if (clickedTab) {
            clickedTab.classList.add('active');
        }
    };

    // Initialize cart display
    updateCartDisplay();
});
</script>

<style>
/* Modern Giftcode Creation Styles */
.giftcode-create-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
}

/* Page Header */
.page-header-modern {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 25px;
    background: linear-gradient(135deg, rgba(147, 51, 234, 0.1) 0%, rgba(79, 70, 229, 0.1) 100%);
    border: 1px solid rgba(147, 51, 234, 0.2);
    border-radius: 20px;
    backdrop-filter: blur(20px);
}

.header-content {
    display: flex;
    align-items: center;
    gap: 20px;
}

.header-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #9333ea 0%, #4f46e5 100%);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
    box-shadow: 0 8px 25px rgba(147, 51, 234, 0.3);
}

.header-text h1 {
    margin: 0;
    font-size: 28px;
    font-weight: 700;
    background: linear-gradient(135deg, #9333ea 0%, #4f46e5 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.header-text p {
    margin: 5px 0 0 0;
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
}

.header-actions .btn-glass {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 12px 20px;
    border-radius: 12px;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.header-actions .btn-glass:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* Form Cards */
.form-card {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    margin-bottom: 25px;
    overflow: hidden;
    backdrop-filter: blur(20px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.card-header-modern {
    background: linear-gradient(135deg, rgba(147, 51, 234, 0.2) 0%, rgba(79, 70, 229, 0.2) 100%);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 20px 25px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.card-icon {
    width: 45px;
    height: 45px;
    background: linear-gradient(135deg, #9333ea 0%, #4f46e5 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    color: white;
    box-shadow: 0 4px 15px rgba(147, 51, 234, 0.3);
}

.card-title {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: white;
}

.card-body-modern {
    padding: 25px;
}

/* Form Groups */
.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.form-group-modern {
    margin-bottom: 20px;
}

.form-group-modern.full-width {
    grid-column: 1 / -1;
}

.form-label-modern {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 10px;
    font-weight: 600;
    color: white;
    font-size: 14px;
}

.form-label-modern i {
    color: #9333ea;
    width: 16px;
}

.required {
    color: #ef4444;
    font-weight: bold;
}

/* Form Inputs */
.form-input-modern, .form-textarea-modern {
    width: 100%;
    padding: 15px 18px;
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.05);
    color: white;
    font-size: 14px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.form-input-modern:focus, .form-textarea-modern:focus {
    outline: none;
    border-color: #9333ea;
    background: rgba(255, 255, 255, 0.1);
    box-shadow: 0 0 0 4px rgba(147, 51, 234, 0.1);
}

.form-input-modern::placeholder, .form-textarea-modern::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.coin-input {
    background: linear-gradient(135deg, rgba(251, 191, 36, 0.1) 0%, rgba(245, 158, 11, 0.1) 100%);
    border-color: rgba(251, 191, 36, 0.3);
}

.item-textarea {
    font-family: 'Courier New', monospace;
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.1) 0%, rgba(16, 185, 129, 0.1) 100%);
    border-color: rgba(34, 197, 94, 0.3);
}

.form-hint {
    display: block;
    margin-top: 8px;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.6);
    font-style: italic;
}

.error-message {
    margin-top: 8px;
    color: #ef4444;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.error-message::before {
    content: "⚠️";
    font-size: 14px;
}

/* Radio Groups */
.radio-group-modern {
    margin-bottom: 25px;
}

.radio-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.radio-option {
    position: relative;
}

.radio-option input[type="radio"] {
    position: absolute;
    opacity: 0;
    pointer-events: none;
}

.radio-label {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    background: rgba(255, 255, 255, 0.05);
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.radio-label:hover {
    border-color: rgba(147, 51, 234, 0.5);
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.radio-option input[type="radio"]:checked + .radio-label {
    border-color: #9333ea;
    background: linear-gradient(135deg, rgba(147, 51, 234, 0.2) 0%, rgba(79, 70, 229, 0.2) 100%);
    box-shadow: 0 8px 25px rgba(147, 51, 234, 0.2);
}

.radio-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: white;
    flex-shrink: 0;
}

.radio-icon.coin-icon {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.radio-icon.item-icon {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.radio-icon.mixed-icon {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
}

.radio-content {
    flex: 1;
}

.radio-title {
    display: block;
    font-weight: 600;
    color: white;
    font-size: 16px;
    margin-bottom: 4px;
}

.radio-desc {
    display: block;
    color: rgba(255, 255, 255, 0.6);
    font-size: 13px;
}

/* Code and Reward Sections */
.code-section, .reward-section {
    margin-top: 20px;
    transition: all 0.3s ease;
}

/* Item Format Guide */
.item-format-guide {
    margin-top: 15px;
    padding: 15px;
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-radius: 10px;
    backdrop-filter: blur(10px);
}

.guide-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: #60a5fa;
    margin-bottom: 8px;
    font-size: 14px;
}

.guide-content {
    color: rgba(255, 255, 255, 0.8);
    font-size: 13px;
    line-height: 1.5;
}

.guide-content code {
    background: rgba(255, 255, 255, 0.1);
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    color: #60a5fa;
}

/* Form Actions */
.form-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 30px;
    padding: 25px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
}

.btn-primary-modern {
    background: linear-gradient(135deg, #9333ea 0%, #4f46e5 100%);
    border: none;
    color: white;
    padding: 15px 30px;
    border-radius: 12px;
    font-weight: 600;
    font-size: 16px;
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(147, 51, 234, 0.3);
}

.btn-primary-modern:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(147, 51, 234, 0.4);
}

.btn-primary-modern:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

.btn-secondary-modern {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 13px 28px;
    border-radius: 12px;
    font-weight: 600;
    font-size: 16px;
    display: flex;
    align-items: center;
    gap: 10px;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.btn-secondary-modern:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    color: white;
    text-decoration: none;
}

/* Responsive Design */
@media (max-width: 768px) {
    .giftcode-create-container {
        padding: 15px;
    }

    .page-header-modern {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }

    .header-content {
        flex-direction: column;
        gap: 15px;
    }

    .form-grid {
        grid-template-columns: 1fr;
    }

    .radio-options {
        grid-template-columns: 1fr;
    }

    .form-actions {
        flex-direction: column;
    }

    .btn-primary-modern, .btn-secondary-modern {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .header-text h1 {
        font-size: 24px;
    }

    .card-body-modern {
        padding: 20px;
    }

    .radio-label {
        padding: 15px;
    }

    .radio-icon {
        width: 40px;
        height: 40px;
        font-size: 16px;
    }
}

/* Shopping Cart Interface Styles */
.shopping-cart-interface {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 25px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Interface Header */
.interface-header {
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.interface-header h4 {
    color: white;
    margin: 0 0 15px 0;
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.legacy-notice {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 15px;
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: 10px;
    backdrop-filter: blur(10px);
}

.notice-icon {
    font-size: 18px;
    flex-shrink: 0;
    margin-top: 2px;
}

.notice-content {
    color: rgba(255, 255, 255, 0.9);
    font-size: 13px;
    line-height: 1.4;
}

.notice-content strong {
    color: #60a5fa;
    font-weight: 600;
}

/* Quick Add Section */
.quick-add-section {
    margin-bottom: 30px;
}

/* Category Items Section */
.category-items-section {
    margin-bottom: 30px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.category-items-section h5 {
    color: white;
    margin-bottom: 15px;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.category-tabs {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.category-tab {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: rgba(255, 255, 255, 0.8);
    padding: 8px 16px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 6px;
}

.category-tab:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
    color: white;
}

.category-tab.active {
    background: linear-gradient(135deg, #9333ea 0%, #7c3aed 100%);
    border-color: #9333ea;
    color: white;
    box-shadow: 0 4px 15px rgba(147, 51, 234, 0.3);
}

.category-content {
    position: relative;
}

.category-items {
    display: none;
}

.category-items.active {
    display: block;
}

.category-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    gap: 12px;
}

.quick-add-section h5 {
    color: white;
    margin-bottom: 15px;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.popular-items-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 12px;
    max-height: 400px;
    overflow-y: auto;
}

.popular-item {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 15px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.popular-item:hover {
    border-color: #9333ea;
    background: rgba(147, 51, 234, 0.1);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(147, 51, 234, 0.2);
}

.popular-item .item-icon {
    font-size: 24px;
    margin-bottom: 8px;
}

.popular-item .item-name {
    color: white;
    font-weight: 600;
    font-size: 14px;
    margin-bottom: 4px;
}

.popular-item .item-id {
    color: rgba(255, 255, 255, 0.6);
    font-size: 12px;
}

/* Manual Add Section */
.manual-add-section {
    margin-bottom: 30px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.manual-add-section h5 {
    color: white;
    margin-bottom: 15px;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.add-item-form .form-row {
    display: grid;
    grid-template-columns: 1fr 2fr 1fr auto;
    gap: 15px;
    align-items: end;
}

.add-item-form .form-group {
    margin-bottom: 0;
}

.add-item-form .form-group label {
    display: block;
    color: white;
    font-size: 12px;
    margin-bottom: 5px;
    font-weight: 500;
}

.add-item-form .form-input {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 14px;
}

.add-item-form .form-input:focus {
    outline: none;
    border-color: #9333ea;
    background: rgba(255, 255, 255, 0.15);
}

.add-btn {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    border: none;
    color: white;
    padding: 10px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.add-btn:hover {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

/* Cart Section */
.cart-section {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    padding: 20px;
}

.cart-section h5 {
    color: white;
    margin-bottom: 15px;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.cart-items {
    min-height: 100px;
    margin-bottom: 15px;
}

.empty-cart {
    text-align: center;
    padding: 40px 20px;
    color: rgba(255, 255, 255, 0.6);
}

.empty-cart i {
    font-size: 48px;
    margin-bottom: 15px;
    opacity: 0.5;
}

.empty-cart p {
    margin: 0;
    font-size: 14px;
}

.cart-item {
    display: grid;
    grid-template-columns: auto 1fr auto auto;
    gap: 15px;
    align-items: center;
    padding: 15px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    margin-bottom: 10px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.cart-item .item-icon {
    font-size: 24px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
}

.cart-item .item-info {
    flex: 1;
}

.cart-item .item-name {
    color: white;
    font-weight: 600;
    font-size: 14px;
    margin-bottom: 2px;
}

.cart-item .item-id {
    color: rgba(255, 255, 255, 0.6);
    font-size: 12px;
}

.cart-item .item-quantity {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

.cart-item .item-quantity label {
    color: rgba(255, 255, 255, 0.8);
    font-size: 11px;
    margin: 0;
}

.quantity-input {
    width: 60px;
    padding: 5px 8px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    text-align: center;
    font-size: 14px;
}

.quantity-input:focus {
    outline: none;
    border-color: #9333ea;
    background: rgba(255, 255, 255, 0.15);
}

.remove-btn {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    border: none;
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.remove-btn:hover {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
}

.cart-summary {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 15px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.total-items {
    color: white;
    font-weight: 600;
    font-size: 14px;
}

.total-items span {
    color: #10b981;
    font-weight: 700;
}

.clear-cart-btn {
    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
    border: none;
    color: white;
    padding: 8px 15px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: all 0.3s ease;
}

.clear-cart-btn:hover {
    background: linear-gradient(135deg, #4b5563 0%, #374151 100%);
    transform: translateY(-1px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .add-item-form .form-row {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .cart-item {
        grid-template-columns: auto 1fr;
        gap: 10px;
    }

    .cart-item .item-quantity,
    .cart-item .remove-btn {
        grid-column: 1 / -1;
        justify-self: center;
    }

    .popular-items-grid {
        grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    }

    .category-grid {
        grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    }

    .category-tabs {
        justify-content: center;
    }

    .category-tab {
        font-size: 12px;
        padding: 6px 12px;
    }

    .shopping-cart-interface {
        padding: 15px;
    }

    .interface-header h4 {
        font-size: 16px;
    }
}

/* Item ID Notice */
.item-id-notice {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.1) 0%, rgba(16, 185, 129, 0.1) 100%);
    border: 1px solid rgba(34, 197, 94, 0.3);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 25px;
    backdrop-filter: blur(10px);
}

.notice-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
    font-weight: 600;
    color: #10b981;
    font-size: 16px;
}

.notice-header i {
    font-size: 18px;
}

.notice-content {
    color: rgba(255, 255, 255, 0.9);
}

.notice-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.notice-column h6 {
    margin: 0 0 10px 0;
    font-weight: 600;
    color: white;
    font-size: 14px;
}

.notice-column ul {
    margin: 0;
    padding-left: 20px;
    list-style-type: none;
}

.notice-column li {
    margin-bottom: 5px;
    font-size: 13px;
    position: relative;
}

.notice-column li::before {
    content: "•";
    color: #10b981;
    font-weight: bold;
    position: absolute;
    left: -15px;
}

.notice-column code {
    background: rgba(16, 185, 129, 0.2);
    color: #10b981;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    font-weight: bold;
}

.item-selection-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    flex-wrap: wrap;
    gap: 15px;
}

.selection-title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: white;
    display: flex;
    align-items: center;
    gap: 10px;
}

.selection-controls {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.search-input, .category-select {
    min-width: 200px;
    padding: 10px 15px;
    font-size: 14px;
}

.popular-items-section {
    margin-bottom: 30px;
}

.popular-title {
    margin: 0 0 15px 0;
    font-size: 16px;
    font-weight: 600;
    color: #fbbf24;
    display: flex;
    align-items: center;
    gap: 8px;
}

.popular-items-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.item-browser {
    margin-bottom: 30px;
}

.item-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 15px;
    max-height: 400px;
    overflow-y: auto;
    padding: 15px;
    background: rgba(255, 255, 255, 0.02);
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.item-card {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 15px;
    display: flex;
    align-items: center;
    gap: 12px;
    transition: all 0.3s ease;
    cursor: pointer;
    backdrop-filter: blur(10px);
}

.item-card:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: #9333ea;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(147, 51, 234, 0.2);
}

.item-card.popular-item {
    border-color: #fbbf24;
    background: linear-gradient(135deg, rgba(251, 191, 36, 0.1) 0%, rgba(245, 158, 11, 0.1) 100%);
}

.item-icon {
    font-size: 24px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    flex-shrink: 0;
}

.item-info {
    flex: 1;
    min-width: 0;
}

.item-name {
    font-weight: 600;
    color: white;
    font-size: 14px;
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.item-category {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.6);
    margin-bottom: 2px;
}

.item-id {
    font-size: 11px;
    color: rgba(255, 255, 255, 0.4);
    font-family: 'Courier New', monospace;
}

.add-item-btn {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    border: none;
    color: white;
    width: 32px;
    height: 32px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.add-item-btn:hover {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    transform: scale(1.1);
}

.selected-items-section {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 25px;
}

.selected-title {
    margin: 0 0 15px 0;
    font-size: 16px;
    font-weight: 600;
    color: #10b981;
    display: flex;
    align-items: center;
    gap: 8px;
}

.selected-items-list {
    background: rgba(255, 255, 255, 0.02);
    border-radius: 10px;
    padding: 15px;
    border: 1px solid rgba(255, 255, 255, 0.05);
    min-height: 100px;
}

.empty-selection {
    text-align: center;
    color: rgba(255, 255, 255, 0.5);
    padding: 30px;
}

.empty-selection i {
    font-size: 32px;
    margin-bottom: 10px;
    display: block;
}

.selected-item {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    padding: 12px;
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 10px;
    transition: all 0.3s ease;
}

.selected-item:last-child {
    margin-bottom: 0;
}

.selected-item:hover {
    background: rgba(255, 255, 255, 0.15);
}

.selected-item-icon {
    font-size: 20px;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    flex-shrink: 0;
}

.selected-item-info {
    flex: 1;
    min-width: 0;
}

.selected-item-name {
    font-weight: 600;
    color: white;
    font-size: 14px;
    margin-bottom: 2px;
}

.selected-item-category {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.6);
    margin-bottom: 1px;
}

.selected-item-id {
    font-size: 11px;
    color: rgba(255, 255, 255, 0.4);
    font-family: 'Courier New', monospace;
}

.selected-item-quantity {
    margin-right: 10px;
}

.quantity-input {
    width: 60px;
    padding: 6px 8px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    text-align: center;
    font-size: 14px;
}

.quantity-input:focus {
    outline: none;
    border-color: #9333ea;
    background: rgba(255, 255, 255, 0.15);
}

.remove-item-btn {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    border: none;
    color: white;
    width: 28px;
    height: 28px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.remove-item-btn:hover {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    transform: scale(1.1);
}
</style>
@endsection
