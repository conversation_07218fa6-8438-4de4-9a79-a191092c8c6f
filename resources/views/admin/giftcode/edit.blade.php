@extends('layouts.admin')

@section('title', 'Chỉnh sửa Giftcode')

@section('content')
<div class="container-fluid">
    <div class="page-header">
        <h1 class="page-title">✏️ Chỉnh sửa Giftcode</h1>
        <div class="page-actions">
            <a href="{{ route('admin.giftcode.show', $giftcode->id) }}" class="btn btn-info">
                <i class="fas fa-eye"></i> Xem chi tiết
            </a>
            <a href="{{ route('admin.giftcode.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Quay lại
            </a>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <h3 class="card-title">📝 Thông tin Giftcode</h3>
        </div>
        <div class="card-body">
            <form method="POST" action="{{ route('admin.giftcode.update', $giftcode->id) }}">
                @csrf
                @method('PUT')
                
                <!-- Basic Information -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <label for="name" class="form-label">Tên Giftcode</label>
                        <input type="text" class="form-control @error('name') is-invalid @enderror"
                               id="name" name="name" value="{{ old('name', $giftcode->name) }}"
                               placeholder="Ví dụ: Giftcode chào mừng năm mới">
                        @error('name')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="col-md-6">
                        <label for="type" class="form-label">Loại Giftcode <span class="text-danger">*</span></label>
                        <select class="form-control @error('type') is-invalid @enderror" id="type" name="type">
                            <option value="1" {{ old('type', $giftcode->type) == 1 ? 'selected' : '' }}>Công khai</option>
                            <option value="2" {{ old('type', $giftcode->type) == 2 ? 'selected' : '' }}>Riêng tư</option>
                            <option value="0" {{ old('type', $giftcode->type) == 0 ? 'selected' : '' }}>Theo nhân vật</option>
                        </select>
                        @error('type')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <div class="row mb-4">
                    <div class="col-md-6">
                        <label for="limit" class="form-label">Giới hạn sử dụng <span class="text-danger">*</span></label>
                        <input type="number" class="form-control @error('limit') is-invalid @enderror"
                               id="limit" name="limit" value="{{ old('limit', $giftcode->limit) }}"
                               min="0">
                        <small class="form-text text-muted">0 = không giới hạn</small>
                        @error('limit')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="col-md-6">
                        <label for="period" class="form-label">Thời hạn (ngày) <span class="text-danger">*</span></label>
                        <input type="number" class="form-control @error('period') is-invalid @enderror"
                               id="period" name="period" value="{{ old('period', $giftcode->period) }}"
                               min="0">
                        <small class="form-text text-muted">0 = không hết hạn</small>
                        @error('period')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <div class="row mb-4">
                    <div class="col-12">
                        <label for="content" class="form-label">Nội dung <span class="text-danger">*</span></label>
                        <textarea class="form-control @error('content') is-invalid @enderror"
                                  id="content" name="content" rows="3"
                                  placeholder="Mô tả chi tiết về giftcode này...">{{ old('content', $giftcode->content) }}</textarea>
                        @error('content')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <div class="row mb-4" id="accounts-section" style="{{ old('type', $giftcode->type) == 2 ? '' : 'display: none;' }}">
                    <div class="col-12">
                        <label for="accounts" class="form-label">Tài khoản được phép (riêng tư)</label>
                        <textarea class="form-control @error('accounts') is-invalid @enderror"
                                  id="accounts" name="accounts" rows="2"
                                  placeholder="Nhập tên tài khoản, cách nhau bằng dấu phẩy">{{ old('accounts', $giftcode->accounts) }}</textarea>
                        <small class="form-text text-muted">Ví dụ: user1, user2, user3</small>
                        @error('accounts')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <!-- Items -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title">🎁 Vật phẩm <span class="text-danger">*</span></h5>
                    </div>
                    <div class="card-body">
                        <label for="items" class="form-label">Danh sách vật phẩm</label>
                        <textarea class="form-control @error('items') is-invalid @enderror"
                                  id="items" name="items" rows="8"
                                  placeholder="Mỗi dòng một vật phẩm theo định dạng: goodsid,count,binding,forge_level,appendproplev,lucky,excellenceinfo">{{ old('items', implode(PHP_EOL, $giftcode->getItemsArray())) }}</textarea>
                        <small class="form-text text-muted">
                            Định dạng: goodsid,count,binding,forge_level,appendproplev,lucky,excellenceinfo<br>
                            Ví dụ: 1001,10,0,0,0,0,0
                        </small>
                        @error('items')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <!-- Code Display (Read-only) -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title">🔢 Mã Code (Không thể thay đổi)</h5>
                    </div>
                    <div class="card-body">
                        @php
                            $codes = $giftcode->getCodesArray();
                        @endphp

                        @if(count($codes) > 1)
                            <div class="alert alert-info">
                                <strong>Giftcode này có {{ count($codes) }} mã code:</strong>
                                <div class="codes-preview mt-2">
                                    @foreach(array_slice($codes, 0, 5) as $code)
                                        <span class="badge badge-secondary me-1">{{ $code }}</span>
                                    @endforeach
                                    @if(count($codes) > 5)
                                        <span class="text-muted">... và {{ count($codes) - 5 }} mã khác</span>
                                    @endif
                                </div>
                            </div>
                        @else
                            <div class="alert alert-info">
                                <strong>Mã code:</strong>
                                <span class="badge badge-secondary">{{ $codes[0] ?? 'N/A' }}</span>
                            </div>
                        @endif
                    </div>
                </div>



                <!-- Settings -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title">⚙️ Cài đặt</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">Trạng thái</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="is_active" id="is_active"
                                           value="1" {{ old('is_active', $giftcode->is_active) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="is_active">
                                        Kích hoạt giftcode
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Current Stats -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title">📊 Thống kê hiện tại</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="stat-box">
                                    <div class="stat-value">{{ number_format($giftcode->getUsageCount()) }}</div>
                                    <div class="stat-label">Đã sử dụng</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-box">
                                    @php $remaining = $giftcode->limit > 0 ? max(0, $giftcode->limit - $giftcode->getUsageCount()) : 'Không giới hạn'; @endphp
                                    <div class="stat-value">{{ is_numeric($remaining) ? number_format($remaining) : $remaining }}</div>
                                    <div class="stat-label">Còn lại</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-box">
                                    @php $usagePercent = $giftcode->limit > 0 ? ($giftcode->getUsageCount() / $giftcode->limit) * 100 : 0; @endphp
                                    <div class="stat-value">{{ number_format($usagePercent, 1) }}%</div>
                                    <div class="stat-label">Tỷ lệ sử dụng</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-box">
                                    <div class="stat-value">
                                        @if($giftcode->is_active)
                                            <span class="text-success">Hoạt động</span>
                                        @else
                                            <span class="text-danger">Vô hiệu</span>
                                        @endif
                                    </div>
                                    <div class="stat-label">Trạng thái</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Submit -->
                <div class="row">
                    <div class="col-12">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save"></i> Cập nhật Giftcode
                        </button>
                        <a href="{{ route('admin.giftcode.show', $giftcode->id) }}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Hủy
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.card {
    border: 1px solid #e9ecef;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px 10px 0 0;
}

.reward-item {
    margin-bottom: 10px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 5px;
}

.reward-section {
    margin-top: 15px;
}

.items-list {
    margin-top: 10px;
}

.item-entry {
    padding: 8px;
    margin-bottom: 5px;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 4px;
}

.item-name {
    font-weight: bold;
    color: #495057;
}

.item-details {
    font-size: 0.9em;
    color: #6c757d;
    margin-left: 10px;
}

.codes-preview .badge {
    margin-right: 5px;
    margin-bottom: 5px;
}

.stat-box {
    text-align: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: #495057;
}

.stat-label {
    font-size: 0.9rem;
    color: #6c757d;
    margin-top: 5px;
}

.form-check-input:checked {
    background-color: #667eea;
    border-color: #667eea;
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
}

.btn-success:hover {
    background: linear-gradient(135deg, #218838 0%, #1ea080 100%);
}

.text-danger {
    color: #dc3545 !important;
}

.alert-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const typeSelect = document.getElementById('type');
    const accountsSection = document.getElementById('accounts-section');

    function toggleAccountsSection() {
        if (typeSelect.value == '2') {
            accountsSection.style.display = '';
        } else {
            accountsSection.style.display = 'none';
        }
    }

    typeSelect.addEventListener('change', toggleAccountsSection);
    toggleAccountsSection(); // Initial call
});
</script>
@endsection
