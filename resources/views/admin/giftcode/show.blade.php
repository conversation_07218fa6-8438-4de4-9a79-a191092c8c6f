@extends('layouts.admin')

@section('title', 'Chi tiết Giftcode')

@section('styles')
<style>
    .breadcrumb {
        color: white;
        margin-bottom: 20px;
        opacity: 0.8;
    }
    .breadcrumb a {
        color: white;
        text-decoration: none;
    }
    .breadcrumb a:hover {
        text-decoration: underline;
    }
    .page-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(16px);
        border-radius: 15px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        padding: 30px;
        margin-bottom: 30px;
        color: white;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 20px;
    }
    .page-title {
        font-size: 28px;
        font-weight: 700;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 10px;
    }
    .page-actions {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
    }
    .btn {
        padding: 12px 24px;
        border: none;
        border-radius: 8px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;
    }
    .btn-primary {
        background: linear-gradient(45deg, #3b82f6, #2563eb);
        color: white;
    }
    .btn-secondary {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.3);
    }
    .btn-success {
        background: linear-gradient(45deg, #10b981, #059669);
        color: white;
    }
    .btn-danger {
        background: linear-gradient(45deg, #ef4444, #dc2626);
        color: white;
    }
    .btn-warning {
        background: linear-gradient(45deg, #f59e0b, #d97706);
        color: white;
    }
    .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
    .card {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(16px);
        border-radius: 15px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        margin-bottom: 30px;
        overflow: hidden;
    }
    .card-header {
        background: rgba(255, 255, 255, 0.1);
        padding: 20px 25px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    .card-title {
        color: white;
        font-size: 18px;
        font-weight: 600;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 10px;
    }
    .card-body {
        padding: 25px;
        color: white;
    }

    /* Info Grid Layout */
    .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
    }
    .info-item {
        display: flex;
        flex-direction: column;
        gap: 8px;
        padding: 15px;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 10px;
        border: 1px solid rgba(255, 255, 255, 0.1);
    }
    .info-item.full-width {
        grid-column: 1 / -1;
    }
    .info-label {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.8);
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 8px;
    }
    .info-value {
        font-size: 16px;
        font-weight: 600;
        color: white;
    }

    /* Badges and Status */
    .status-badge, .type-badge, .server-badge, .limit-badge, .usage-badge, .date-badge, .expiry-badge {
        display: inline-flex;
        align-items: center;
        gap: 6px;
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    .status-active {
        background: rgba(16, 185, 129, 0.2);
        color: #10b981;
        border: 1px solid rgba(16, 185, 129, 0.3);
    }
    .status-inactive {
        background: rgba(107, 114, 128, 0.2);
        color: #9ca3af;
        border: 1px solid rgba(107, 114, 128, 0.3);
    }
    .status-expired {
        background: rgba(239, 68, 68, 0.2);
        color: #ef4444;
        border: 1px solid rgba(239, 68, 68, 0.3);
    }
    .status-used-up {
        background: rgba(245, 158, 11, 0.2);
        color: #f59e0b;
        border: 1px solid rgba(245, 158, 11, 0.3);
    }
    .type-public {
        background: rgba(59, 130, 246, 0.2);
        color: #3b82f6;
        border: 1px solid rgba(59, 130, 246, 0.3);
    }
    .type-private {
        background: rgba(139, 92, 246, 0.2);
        color: #8b5cf6;
        border: 1px solid rgba(139, 92, 246, 0.3);
    }
    .type-character {
        background: rgba(245, 158, 11, 0.2);
        color: #f59e0b;
        border: 1px solid rgba(245, 158, 11, 0.3);
    }
    .type-other {
        background: rgba(107, 114, 128, 0.2);
        color: #9ca3af;
        border: 1px solid rgba(107, 114, 128, 0.3);
    }
    .server-badge, .limit-badge, .usage-badge, .date-badge, .expiry-badge {
        background: rgba(255, 255, 255, 0.1);
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    /* Code Display */
    .code-display {
        display: flex;
        align-items: center;
        gap: 10px;
        flex-wrap: wrap;
    }
    .main-code {
        font-family: 'Courier New', monospace;
        background: rgba(255, 255, 255, 0.1);
        padding: 8px 12px;
        border-radius: 6px;
        font-weight: 600;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }
    .code-count {
        font-size: 12px;
        color: rgba(255, 255, 255, 0.7);
        background: rgba(255, 255, 255, 0.05);
        padding: 4px 8px;
        border-radius: 4px;
    }

    /* Accounts List */
    .accounts-list {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
    }
    .account-tag {
        background: rgba(59, 130, 246, 0.2);
        color: #3b82f6;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 500;
        border: 1px solid rgba(59, 130, 246, 0.3);
    }

    /* Statistics Grid */
    .stats-grid {
        display: flex;
        flex-direction: column;
        gap: 20px;
    }
    .stat-card {
        display: flex;
        align-items: center;
        gap: 15px;
        padding: 20px;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 12px;
        border: 1px solid rgba(255, 255, 255, 0.1);
        transition: all 0.3s ease;
    }
    .stat-card:hover {
        background: rgba(255, 255, 255, 0.08);
        transform: translateY(-2px);
    }
    .stat-icon {
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(45deg, #3b82f6, #2563eb);
        border-radius: 12px;
        color: white;
        font-size: 20px;
    }
    .stat-content {
        flex: 1;
    }
    .stat-label {
        font-size: 12px;
        color: rgba(255, 255, 255, 0.7);
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-bottom: 5px;
    }
    .stat-value {
        font-size: 18px;
        font-weight: 700;
        color: white;
        margin-bottom: 8px;
    }
    .progress-bar-container {
        width: 100%;
        height: 6px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 3px;
        overflow: hidden;
    }
    .progress-bar-fill {
        height: 100%;
        background: linear-gradient(45deg, #10b981, #059669);
        border-radius: 3px;
        transition: width 0.3s ease;
    }

    /* Actions Grid */
    .actions-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }
    .action-btn {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 8px;
        padding: 20px 15px;
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 12px;
        color: white;
        text-decoration: none;
        transition: all 0.3s ease;
        cursor: pointer;
        font-size: 14px;
        font-weight: 500;
    }
    .action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        color: white;
        text-decoration: none;
    }
    .action-btn i {
        font-size: 20px;
        margin-bottom: 5px;
    }
    .action-toggle:hover {
        background: rgba(245, 158, 11, 0.2);
        border-color: rgba(245, 158, 11, 0.3);
    }
    .action-edit:hover {
        background: rgba(59, 130, 246, 0.2);
        border-color: rgba(59, 130, 246, 0.3);
    }
    .action-delete:hover {
        background: rgba(239, 68, 68, 0.2);
        border-color: rgba(239, 68, 68, 0.3);
    }
    .action-copy:hover {
        background: rgba(16, 185, 129, 0.2);
        border-color: rgba(16, 185, 129, 0.3);
    }

    /* Items Display */
    .items-grid {
        display: grid;
        gap: 15px;
    }
    .item-card {
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 10px;
        padding: 15px;
    }
    .item-header {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 10px;
    }
    .item-id {
        background: linear-gradient(45deg, #3b82f6, #2563eb);
        color: white;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 600;
    }
    .item-quantity {
        background: linear-gradient(45deg, #10b981, #059669);
        color: white;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 600;
    }
    .item-details {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 10px;
        font-size: 12px;
    }
    .item-detail {
        display: flex;
        justify-content: space-between;
        padding: 4px 8px;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 4px;
    }
    .item-detail-label {
        color: rgba(255, 255, 255, 0.7);
    }
    .item-detail-value {
        color: white;
        font-weight: 500;
    }

    /* Codes Grid */
    .codes-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 10px;
    }
    .code-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px;
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 8px;
    }
    .code-text {
        font-family: 'Courier New', monospace;
        font-weight: 600;
        color: white;
    }
    .copy-btn {
        background: rgba(16, 185, 129, 0.2);
        border: 1px solid rgba(16, 185, 129, 0.3);
        color: #10b981;
        padding: 4px 8px;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.2s;
    }
    .copy-btn:hover {
        background: rgba(16, 185, 129, 0.3);
    }

    /* Empty State */
    .empty-state {
        text-align: center;
        padding: 40px 20px;
        color: rgba(255, 255, 255, 0.7);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .page-header {
            flex-direction: column;
            text-align: center;
        }
        .info-grid {
            grid-template-columns: 1fr;
        }
        .actions-grid {
            grid-template-columns: 1fr;
        }
        .stats-grid .stat-card {
            flex-direction: column;
            text-align: center;
        }
        .codes-grid {
            grid-template-columns: 1fr;
        }
    }
</style>
@endsection

@section('content')
<div class="container-fluid">
    <!-- Breadcrumb -->
    <div class="breadcrumb">
        <a href="/admin/dashboard">Dashboard</a> /
        <a href="{{ route('admin.giftcode.index') }}">Quản lý Giftcode</a> /
        Chi tiết Giftcode #{{ $giftcode->id }}
    </div>

    <div class="page-header">
        <h1 class="page-title">
            <i class="fas fa-gift"></i>
            Chi tiết Giftcode #{{ $giftcode->id }}
        </h1>
        <div class="page-actions">
            <a href="{{ route('admin.giftcode.edit', $giftcode->id) }}" class="btn btn-primary">
                <i class="fas fa-edit"></i> Chỉnh sửa
            </a>
            <a href="{{ route('admin.giftcode.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Quay lại
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Basic Information -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-info-circle"></i>
                        Thông tin cơ bản
                    </h3>
                </div>
                <div class="card-body">
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">
                                <i class="fas fa-hashtag"></i>
                                ID
                            </div>
                            <div class="info-value">#{{ $giftcode->id }}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">
                                <i class="fas fa-toggle-on"></i>
                                Trạng thái
                            </div>
                            <div class="info-value">
                                @if($giftcode->is_active)
                                    <span class="status-badge status-active">
                                        <i class="fas fa-check-circle"></i>
                                        Hoạt động
                                    </span>
                                @else
                                    <span class="status-badge status-inactive">
                                        <i class="fas fa-times-circle"></i>
                                        Vô hiệu hóa
                                    </span>
                                @endif
                            </div>
                        </div>

                    <div class="row">
                        <div class="info-item">
                            <div class="info-label">
                                <i class="fas fa-tag"></i>
                                Tên
                            </div>
                            <div class="info-value">{{ $giftcode->name ?: $giftcode->content }}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">
                                <i class="fas fa-code"></i>
                                Mã Code
                            </div>
                            <div class="info-value">
                                @if(count($codes) > 1)
                                    <div class="code-display">
                                        <span class="main-code">{{ $codes[0] }}</span>
                                        <span class="code-count">+{{ count($codes) - 1 }} khác</span>
                                    </div>
                                @else
                                    <div class="code-display">
                                        <span class="main-code">{{ $codes[0] ?? 'N/A' }}</span>
                                    </div>
                                @endif
                            </div>
                        </div>
                        <div class="info-item full-width">
                            <div class="info-label">
                                <i class="fas fa-file-text"></i>
                                Nội dung
                            </div>
                            <div class="info-value">{{ $giftcode->content }}</div>
                        </div>

                        <div class="info-item">
                            <div class="info-label">
                                <i class="fas fa-layer-group"></i>
                                Loại
                            </div>
                            <div class="info-value">
                                @switch($giftcode->type)
                                    @case(1)
                                        <span class="type-badge type-public">
                                            <i class="fas fa-globe"></i>
                                            Công khai
                                        </span>
                                        @break
                                    @case(2)
                                        <span class="type-badge type-private">
                                            <i class="fas fa-lock"></i>
                                            Riêng tư
                                        </span>
                                        @break
                                    @case(0)
                                        <span class="type-badge type-character">
                                            <i class="fas fa-user"></i>
                                            Theo nhân vật
                                        </span>
                                        @break
                                    @default
                                        <span class="type-badge type-other">
                                            <i class="fas fa-question"></i>
                                            Khác
                                        </span>
                                @endswitch
                            </div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">
                                <i class="fas fa-server"></i>
                                Server
                            </div>
                            <div class="info-value">
                                <span class="server-badge">
                                    {{ $giftcode->zoneid == 0 ? 'Tất cả server' : 'Server ' . $giftcode->zoneid }}
                                </span>
                            </div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">
                                <i class="fas fa-users"></i>
                                Giới hạn sử dụng
                            </div>
                            <div class="info-value">
                                <span class="limit-badge">
                                    {{ $giftcode->limit > 0 ? number_format($giftcode->limit) : 'Không giới hạn' }}
                                </span>
                            </div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">
                                <i class="fas fa-chart-line"></i>
                                Đã sử dụng
                            </div>
                            <div class="info-value">
                                <span class="usage-badge">
                                    {{ number_format($giftcode->getUsageCount()) }}
                                </span>
                            </div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">
                                <i class="fas fa-calendar-plus"></i>
                                Ngày tạo
                            </div>
                            <div class="info-value">
                                <span class="date-badge">
                                    {{ \Carbon\Carbon::parse($giftcode->created_at)->format('d/m/Y H:i:s') }}
                                </span>
                            </div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">
                                <i class="fas fa-clock"></i>
                                Hết hạn
                            </div>
                            <div class="info-value">
                                <span class="expiry-badge">
                                    @if($giftcode->period > 0)
                                        {{ \Carbon\Carbon::parse($giftcode->created_at)->addDays($giftcode->period)->format('d/m/Y H:i:s') }}
                                    @else
                                        Không giới hạn
                                    @endif
                                </span>
                            </div>
                        </div>
                        @if($giftcode->accounts && $giftcode->type == 2)
                        <div class="info-item full-width">
                            <div class="info-label">
                                <i class="fas fa-user-check"></i>
                                Tài khoản được phép
                            </div>
                            <div class="info-value">
                                <div class="accounts-list">
                                    @foreach(explode(',', $giftcode->accounts) as $account)
                                        <span class="account-tag">{{ trim($account) }}</span>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Items -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-gift"></i>
                        Vật phẩm thưởng
                    </h3>
                    <span class="badge" style="background: rgba(255, 255, 255, 0.2); color: white;">
                        {{ count($giftcode->getItemsArray()) }} vật phẩm
                    </span>
                </div>
                <div class="card-body">
                    @if($giftcode->items && count($giftcode->getItemsArray()) > 0)
                        <div class="items-grid">
                            @foreach($giftcode->getItemsArray() as $index => $item)
                                @php
                                    $parts = explode(',', trim($item));
                                    if (count($parts) >= 7) {
                                        $itemId = $parts[0];
                                        $count = $parts[1];
                                        $binding = $parts[2];
                                        $forgeLevel = $parts[3];
                                        $appendPropLev = $parts[4];
                                        $lucky = $parts[5];
                                        $excellenceInfo = $parts[6];
                                    }
                                @endphp
                                @if(count($parts) >= 7)
                                    <div class="item-card">
                                        <div class="item-header">
                                            <span class="item-id">Item #{{ $itemId }}</span>
                                            <span class="item-quantity">x{{ $count }}</span>
                                        </div>
                                        <div class="item-details">
                                            <div class="item-detail">
                                                <span class="item-detail-label">Binding:</span>
                                                <span class="item-detail-value">{{ $binding == 1 ? 'Yes' : 'No' }}</span>
                                            </div>
                                            <div class="item-detail">
                                                <span class="item-detail-label">Forge:</span>
                                                <span class="item-detail-value">+{{ $forgeLevel }}</span>
                                            </div>
                                            <div class="item-detail">
                                                <span class="item-detail-label">Append:</span>
                                                <span class="item-detail-value">{{ $appendPropLev }}</span>
                                            </div>
                                            <div class="item-detail">
                                                <span class="item-detail-label">Lucky:</span>
                                                <span class="item-detail-value">{{ $lucky == 1 ? 'Yes' : 'No' }}</span>
                                            </div>
                                            <div class="item-detail">
                                                <span class="item-detail-label">Excellence:</span>
                                                <span class="item-detail-value">{{ $excellenceInfo }}</span>
                                            </div>
                                        </div>
                                    </div>
                                @endif
                            @endforeach
                        </div>
                    @else
                        <div class="empty-state">
                            <i class="fas fa-box-open" style="font-size: 48px; color: rgba(255, 255, 255, 0.3); margin-bottom: 15px;"></i>
                            <p style="color: rgba(255, 255, 255, 0.7); margin: 0;">Không có vật phẩm nào</p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- All Codes (if multiple) -->
            @if(count($codes) > 1)
            <div class="card mb-4">
                <div class="card-header">
                    <h3 class="card-title">🔢 Tất cả mã Code ({{ count($codes) }})</h3>
                </div>
                <div class="card-body">
                    <div class="codes-grid">
                        @foreach($codes as $index => $code)
                            <div class="code-item">
                                <span class="code-text">{{ $code }}</span>
                                <button class="btn btn-sm btn-outline-primary copy-btn" data-code="{{ $code }}">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
            @endif
        </div>

        <!-- Statistics -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-chart-pie"></i>
                        Thống kê
                    </h3>
                </div>
                <div class="card-body">
                    @php
                        $usageCount = $giftcode->getUsageCount();
                        $usagePercent = $giftcode->limit > 0 ? ($usageCount / $giftcode->limit) * 100 : 0;
                        $remainingUses = $giftcode->limit > 0 ? max(0, $giftcode->limit - $usageCount) : 'Không giới hạn';
                        $isExpired = $giftcode->period > 0 && \Carbon\Carbon::parse($giftcode->created_at)->addDays($giftcode->period)->isPast();
                        $isUsedUp = $giftcode->limit > 0 && $usageCount >= $giftcode->limit;
                    @endphp

                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-percentage"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-label">Tỷ lệ sử dụng</div>
                                <div class="stat-value">{{ number_format($usagePercent, 1) }}%</div>
                                <div class="progress-bar-container">
                                    <div class="progress-bar-fill" style="width: {{ min($usagePercent, 100) }}%"></div>
                                </div>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-hourglass-half"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-label">Còn lại</div>
                                <div class="stat-value">{{ is_numeric($remainingUses) ? number_format($remainingUses) : $remainingUses }}</div>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-heartbeat"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-label">Trạng thái hiện tại</div>
                                <div class="stat-value">
                                    @if(!$giftcode->is_active)
                                        <span class="status-badge status-inactive">
                                            <i class="fas fa-pause-circle"></i>
                                            Vô hiệu hóa
                                        </span>
                                    @elseif($isExpired)
                                        <span class="status-badge status-expired">
                                            <i class="fas fa-clock"></i>
                                            Hết hạn
                                        </span>
                                    @elseif($isUsedUp)
                                        <span class="status-badge status-used-up">
                                            <i class="fas fa-ban"></i>
                                            Hết lượt
                                        </span>
                                    @else
                                        <span class="status-badge status-active">
                                            <i class="fas fa-check-circle"></i>
                                            Hoạt động
                                        </span>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-bolt"></i>
                        Thao tác nhanh
                    </h3>
                </div>
                <div class="card-body">
                    <div class="actions-grid">
                        <button class="action-btn action-toggle" onclick="toggleStatus({{ $giftcode->id }})">
                            @if($giftcode->is_active)
                                <i class="fas fa-pause-circle"></i>
                                <span>Vô hiệu hóa</span>
                            @else
                                <i class="fas fa-play-circle"></i>
                                <span>Kích hoạt</span>
                            @endif
                        </button>

                        <a href="{{ route('admin.giftcode.edit', $giftcode->id) }}" class="action-btn action-edit">
                            <i class="fas fa-edit"></i>
                            <span>Chỉnh sửa</span>
                        </a>

                        <button class="action-btn action-delete" onclick="deleteGiftcode({{ $giftcode->id }})">
                            <i class="fas fa-trash-alt"></i>
                            <span>Xóa</span>
                        </button>

                        <button class="action-btn action-copy" onclick="copyAllCodes()">
                            <i class="fas fa-copy"></i>
                            <span>Copy mã</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Usage History -->
    @if($usageHistory && $usageHistory->count() > 0)
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">📈 Lịch sử sử dụng ({{ $usageHistory->count() }} gần nhất)</h3>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Thời gian</th>
                            <th>Tài khoản</th>
                            <th>Nhân vật</th>
                            <th>Server</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($usageHistory as $usage)
                            <tr>
                                <td>{{ \Carbon\Carbon::parse($usage->created_at)->format('d/m/Y H:i:s') }}</td>
                                <td>{{ $usage->uid }}</td>
                                <td>{{ $usage->rid ?: 'N/A' }}</td>
                                <td>{{ $usage->zoneid ?: 'N/A' }}</td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    @endif
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Copy individual code functionality
    document.querySelectorAll('.copy-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const code = this.dataset.code;
            navigator.clipboard.writeText(code).then(() => {
                const originalContent = this.innerHTML;
                this.innerHTML = '<i class="fas fa-check"></i>';
                this.style.background = 'rgba(16, 185, 129, 0.3)';
                setTimeout(() => {
                    this.innerHTML = originalContent;
                    this.style.background = 'rgba(16, 185, 129, 0.2)';
                }, 2000);
            }).catch(err => {
                console.error('Failed to copy: ', err);
                alert('Không thể copy mã code');
            });
        });
    });
});

// Copy all codes functionality
function copyAllCodes() {
    const codes = @json($codes);
    const allCodes = codes.join('\n');

    navigator.clipboard.writeText(allCodes).then(() => {
        // Show success notification
        showNotification('Đã copy tất cả mã code!', 'success');
    }).catch(err => {
        console.error('Failed to copy: ', err);
        alert('Không thể copy mã code');
    });
}

// Show notification
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 'info-circle'}"></i>
        <span>${message}</span>
    `;

    // Add notification styles
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? 'rgba(16, 185, 129, 0.9)' : 'rgba(59, 130, 246, 0.9)'};
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        gap: 10px;
        z-index: 10000;
        backdrop-filter: blur(10px);
        border: 1px solid ${type === 'success' ? 'rgba(16, 185, 129, 0.3)' : 'rgba(59, 130, 246, 0.3)'};
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        animation: slideIn 0.3s ease;
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    @keyframes slideOut {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(style);

// Toggle status
function toggleStatus(id) {
    if (!confirm('Bạn có chắc chắn muốn thay đổi trạng thái giftcode này?')) {
        return;
    }

    // Show loading state
    const btn = event.target.closest('.action-btn');
    const originalContent = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i><span>Đang xử lý...</span>';
    btn.disabled = true;

    fetch(`/admin/giftcode/${id}/toggle-status`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            setTimeout(() => location.reload(), 1000);
        } else {
            showNotification('Lỗi: ' + data.message, 'error');
            btn.innerHTML = originalContent;
            btn.disabled = false;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Có lỗi xảy ra khi thay đổi trạng thái', 'error');
        btn.innerHTML = originalContent;
        btn.disabled = false;
    });
}

// Delete giftcode
function deleteGiftcode(id) {
    if (!confirm('Bạn có chắc chắn muốn xóa giftcode này? Hành động này không thể hoàn tác!')) {
        return;
    }

    // Show loading state
    const btn = event.target.closest('.action-btn');
    const originalContent = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i><span>Đang xóa...</span>';
    btn.disabled = true;

    fetch(`/admin/giftcode/${id}`, {
        method: 'DELETE',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            setTimeout(() => {
                window.location.href = '{{ route("admin.giftcode.index") }}';
            }, 1000);
        } else {
            showNotification('Lỗi: ' + data.message, 'error');
            btn.innerHTML = originalContent;
            btn.disabled = false;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Có lỗi xảy ra khi xóa giftcode', 'error');
        btn.innerHTML = originalContent;
        btn.disabled = false;
    });
}
</script>

<style>
.info-item {
    margin-bottom: 15px;
}

.info-item label {
    font-weight: bold;
    color: #495057;
    margin-right: 10px;
}

.code-display {
    font-family: 'Courier New', monospace;
    background: #f8f9fa;
    padding: 4px 8px;
    border-radius: 4px;
    border: 1px solid #dee2e6;
}

.reward-item {
    margin-bottom: 10px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 5px;
}

.reward-section {
    margin-top: 15px;
}

.items-list {
    margin-top: 10px;
}

.item-entry {
    padding: 8px;
    margin-bottom: 5px;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 4px;
}

.item-name {
    font-weight: bold;
    color: #495057;
}

.item-details {
    font-size: 0.9em;
    color: #6c757d;
    margin-left: 10px;
}

.codes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 10px;
}

.code-item {
    display: flex;
    align-items: center;
    padding: 8px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
}

.code-text {
    font-family: 'Courier New', monospace;
    flex: 1;
    margin-right: 10px;
}

.stat-item {
    margin-bottom: 20px;
}

.stat-label {
    font-size: 0.9em;
    color: #6c757d;
    margin-bottom: 5px;
}

.stat-value {
    font-size: 1.1em;
    font-weight: bold;
}

.progress {
    height: 20px;
}

.badge {
    font-size: 0.8em;
}
</style>
@endsection
