@extends('layouts.admin')

@section('title', 'Báo cáo sử dụng Giftcode')

@section('content')
<div class="container-fluid">
    <div class="page-header">
        <h1 class="page-title">📊 Báo cáo sử dụng Giftcode</h1>
        <div class="page-actions">
            <a href="{{ route('admin.giftcode.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Quay lại
            </a>
        </div>
    </div>

    <!-- Date Filter -->
    <div class="card mb-4">
        <div class="card-header">
            <h3 class="card-title">🗓️ Bộ lọc thời gian</h3>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('admin.giftcode.usage-report') }}" class="row g-3">
                <div class="col-md-4">
                    <label for="start_date" class="form-label">Từ ngày</label>
                    <input type="date" class="form-control" id="start_date" name="start_date" value="{{ $startDate }}">
                </div>
                <div class="col-md-4">
                    <label for="end_date" class="form-label">Đến ngày</label>
                    <input type="date" class="form-control" id="end_date" name="end_date" value="{{ $endDate }}">
                </div>
                <div class="col-md-4">
                    <label class="form-label">&nbsp;</label>
                    <button type="submit" class="btn btn-primary d-block">
                        <i class="fas fa-search"></i> Lọc dữ liệu
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Summary Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon">📈</div>
                <div class="stat-value">{{ number_format($dailyUsage->sum('total_usage')) }}</div>
                <div class="stat-label">Tổng lượt sử dụng</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon">🎯</div>
                <div class="stat-value">{{ number_format($topGiftcodes->count()) }}</div>
                <div class="stat-label">Giftcode được sử dụng</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon">📅</div>
                <div class="stat-value">{{ number_format($dailyUsage->count()) }}</div>
                <div class="stat-label">Ngày có hoạt động</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon">⭐</div>
                <div class="stat-value">{{ $topGiftcodes->first() ? number_format($topGiftcodes->first()->total_usage) : 0 }}</div>
                <div class="stat-label">Lượt sử dụng cao nhất</div>
            </div>
        </div>
    </div>

    <!-- Top Giftcodes -->
    <div class="card mb-4">
        <div class="card-header">
            <h3 class="card-title">🏆 Top 10 Giftcode được sử dụng nhiều nhất</h3>
        </div>
        <div class="card-body">
            @if($topGiftcodes->count() > 0)
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Thứ hạng</th>
                                <th>Mã Code</th>
                                <th>Tên</th>
                                <th>Giới hạn</th>
                                <th>Đã sử dụng</th>
                                <th>Tỷ lệ sử dụng</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($topGiftcodes as $index => $giftcode)
                                <tr>
                                    <td>
                                        <span class="rank-badge rank-{{ $index + 1 <= 3 ? $index + 1 : 'other' }}">
                                            #{{ $index + 1 }}
                                        </span>
                                    </td>
                                    <td>
                                        <code>{{ is_array($giftcode->code) ? $giftcode->code[0] : $giftcode->code }}</code>
                                    </td>
                                    <td>{{ $giftcode->name ?: 'N/A' }}</td>
                                    <td>{{ $giftcode->limit > 0 ? number_format($giftcode->limit) : 'Không giới hạn' }}</td>
                                    <td>{{ number_format($giftcode->total_usage) }}</td>
                                    <td>
                                        @if($giftcode->limit > 0)
                                            @php $percentage = ($giftcode->total_usage / $giftcode->limit) * 100; @endphp
                                            <div class="progress" style="height: 20px;">
                                                <div class="progress-bar" style="width: {{ min($percentage, 100) }}%">
                                                    {{ number_format($percentage, 1) }}%
                                                </div>
                                            </div>
                                        @else
                                            <span class="text-muted">N/A</span>
                                        @endif
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @else
                <div class="text-center py-4">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <p class="text-muted">Không có dữ liệu sử dụng trong khoảng thời gian này</p>
                </div>
            @endif
        </div>
    </div>

    <!-- Daily Usage Chart -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">📊 Biểu đồ sử dụng theo ngày</h3>
        </div>
        <div class="card-body">
            @if($dailyUsage->count() > 0)
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Ngày</th>
                                <th>Lượt sử dụng</th>
                                <th>Biểu đồ</th>
                            </tr>
                        </thead>
                        <tbody>
                            @php $maxUsage = $dailyUsage->max('total_usage'); @endphp
                            @foreach($dailyUsage as $day)
                                <tr>
                                    <td>{{ \Carbon\Carbon::parse($day->date)->format('d/m/Y') }}</td>
                                    <td>{{ number_format($day->total_usage) }}</td>
                                    <td>
                                        <div class="usage-bar">
                                            <div class="usage-fill" style="width: {{ $maxUsage > 0 ? ($day->total_usage / $maxUsage) * 100 : 0 }}%"></div>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @else
                <div class="text-center py-4">
                    <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                    <p class="text-muted">Không có dữ liệu biểu đồ trong khoảng thời gian này</p>
                </div>
            @endif
        </div>
    </div>
</div>

<style>
.stat-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
}

.stat-icon {
    font-size: 2rem;
    margin-bottom: 10px;
}

.stat-value {
    font-size: 2rem;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 5px;
}

.stat-label {
    color: #6c757d;
    font-size: 0.9rem;
}

.rank-badge {
    padding: 4px 8px;
    border-radius: 15px;
    font-weight: bold;
    font-size: 0.8rem;
}

.rank-1 { background: #ffd700; color: #000; }
.rank-2 { background: #c0c0c0; color: #000; }
.rank-3 { background: #cd7f32; color: #fff; }
.rank-other { background: #6c757d; color: #fff; }

.usage-bar {
    width: 100%;
    height: 20px;
    background: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
}

.usage-fill {
    height: 100%;
    background: linear-gradient(90deg, #28a745, #20c997);
    transition: width 0.3s ease;
}

.progress {
    background: #e9ecef;
}

.progress-bar {
    background: linear-gradient(90deg, #007bff, #0056b3);
    color: white;
    font-size: 0.8rem;
    line-height: 20px;
}
</style>
@endsection
