@extends('layouts.admin')

@section('title', '<PERSON><PERSON><PERSON> mật hệ thống - MU Admin Panel')

@section('styles')
<style>
    .security-card {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(16px);
        border-radius: 15px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        padding: 25px;
        margin-bottom: 25px;
        color: white;
    }
    .security-status {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 15px;
    }
    .status-good { color: #10b981; }
    .status-warning { color: #f59e0b; }
    .status-danger { color: #ef4444; }
    .security-metric {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 0;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }
    .security-metric:last-child {
        border-bottom: none;
    }
    .metric-value {
        font-weight: 600;
        font-size: 18px;
    }
</style>
@endsection

@section('content')
<div class="page-header">
    <h1 class="page-title">🔒 <PERSON><PERSON>o mật hệ thống</h1>
    <p class="page-desc"><PERSON><PERSON><PERSON><PERSON> s<PERSON><PERSON> và quản lý bảo mật admin panel</p>
</div>

<!-- Security Overview -->
<div class="security-card">
    <h3>📊 Tổng quan bảo mật</h3>
    
    <div class="security-status status-good">
        <span>✅</span>
        <span>Hệ thống đang hoạt động bình thường</span>
    </div>
    
    <div class="security-metric">
        <span>Admin users đang hoạt động</span>
        <span class="metric-value">{{ $stats['active_admins'] ?? 0 }}</span>
    </div>
    
    <div class="security-metric">
        <span>Đăng nhập thành công hôm nay</span>
        <span class="metric-value status-good">{{ $stats['successful_logins_today'] ?? 0 }}</span>
    </div>
    
    <div class="security-metric">
        <span>Đăng nhập thất bại hôm nay</span>
        <span class="metric-value status-warning">{{ $stats['failed_logins_today'] ?? 0 }}</span>
    </div>
    
    <div class="security-metric">
        <span>IP bị chặn</span>
        <span class="metric-value status-danger">{{ $stats['blocked_ips'] ?? 0 }}</span>
    </div>
</div>

<!-- Recent Login Attempts -->
<div class="security-card">
    <h3>🔍 Hoạt động đăng nhập gần đây</h3>
    
    @if(isset($recentLogins) && count($recentLogins) > 0)
        <div class="table-responsive">
            <table class="table">
                <thead>
                    <tr>
                        <th>Thời gian</th>
                        <th>Admin</th>
                        <th>IP Address</th>
                        <th>Trạng thái</th>
                        <th>User Agent</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($recentLogins as $login)
                    <tr>
                        <td>{{ $login->created_at }}</td>
                        <td>{{ $login->admin_username }}</td>
                        <td>{{ $login->ip_address }}</td>
                        <td>
                            @if($login->action === 'login')
                                <span class="status-good">✅ Thành công</span>
                            @else
                                <span class="status-danger">❌ Thất bại</span>
                            @endif
                        </td>
                        <td>{{ Str::limit($login->user_agent ?? '', 50) }}</td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    @else
        <p style="opacity: 0.7;">Không có hoạt động đăng nhập nào gần đây.</p>
    @endif
</div>

<!-- Security Recommendations -->
<div class="security-card">
    <h3>💡 Khuyến nghị bảo mật</h3>
    
    <div class="security-recommendations">
        <div class="recommendation">
            <strong>🔐 Mật khẩu mạnh:</strong>
            <p>Sử dụng mật khẩu ít nhất 8 ký tự, bao gồm chữ hoa, chữ thường, số và ký tự đặc biệt.</p>
        </div>
        
        <div class="recommendation">
            <strong>🕒 Đổi mật khẩu định kỳ:</strong>
            <p>Thay đổi mật khẩu mỗi 90 ngày để đảm bảo bảo mật.</p>
        </div>
        
        <div class="recommendation">
            <strong>🌐 Kiểm tra IP:</strong>
            <p>Chỉ đăng nhập từ các địa chỉ IP đáng tin cậy.</p>
        </div>
        
        <div class="recommendation">
            <strong>📱 Đăng xuất sau khi sử dụng:</strong>
            <p>Luôn đăng xuất khỏi hệ thống khi hoàn thành công việc.</p>
        </div>
    </div>
</div>

@if($admin['role'] === 'super_admin')
<!-- Admin Actions -->
<div class="security-card">
    <h3>⚙️ Hành động bảo mật</h3>
    
    <div class="action-buttons">
        <a href="{{ route('admin.admin-users.index') }}" class="btn btn-primary">
            👥 Quản lý Admin Users
        </a>
        
        <button class="btn btn-warning" onclick="clearFailedLogins()">
            🧹 Xóa log đăng nhập thất bại
        </button>
        
        <button class="btn btn-danger" onclick="forceLogoutAll()">
            🚪 Đăng xuất tất cả admin
        </button>
    </div>
</div>
@endif

<script>
function clearFailedLogins() {
    if (confirm('Bạn có chắc muốn xóa tất cả log đăng nhập thất bại?')) {
        // Implementation for clearing failed login logs
        alert('Chức năng sẽ được triển khai trong phiên bản tiếp theo');
    }
}

function forceLogoutAll() {
    if (confirm('Bạn có chắc muốn đăng xuất tất cả admin khỏi hệ thống?')) {
        // Implementation for force logout all
        alert('Chức năng sẽ được triển khai trong phiên bản tiếp theo');
    }
}
</script>
@endsection
