<!DOCTYPE html>
<html lang="{{ app()->getLocale() }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>@yield('title', __('user.portal'))</title>

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        /* Header */
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .header-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 1rem;
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            min-height: 60px;
            gap: 1rem;
            width: 100%;
            flex-wrap: wrap;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            color: #667eea;
            text-decoration: none;
            flex-shrink: 0;
            white-space: nowrap;
            align-self: center;
        }

        .nav-links {
            display: flex;
            gap: 2rem;
            align-items: center;
            flex-wrap: wrap;
        }

        .nav-links a {
            text-decoration: none;
            color: #555;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            transition: all 0.3s ease;
            white-space: nowrap;
        }

        .nav-links a:hover,
        .nav-links a.active {
            background: #667eea;
            color: white;
        }

        .user-menu {
            position: relative;
        }

        .user-dropdown {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            position: absolute;
            top: 100%;
            right: 0;
            min-width: 200px;
            display: none;
            z-index: 1001;
        }

        .user-dropdown.show {
            display: block;
        }

        .user-dropdown a {
            display: block;
            padding: 0.75rem 1rem;
            color: #555;
            text-decoration: none;
            border-bottom: 1px solid #f3f4f6;
        }

        .user-dropdown a:hover {
            background: #f9fafb;
        }

        /* Language Switcher */
        .language-switcher {
            position: relative;
            display: inline-block;
        }

        .language-dropdown {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            position: absolute;
            top: 100%;
            right: 0;
            min-width: 160px;
            display: none;
            z-index: 1001;
            margin-top: 0.25rem;
        }

        .language-dropdown.show {
            display: block;
        }

        .language-dropdown a {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1rem;
            color: #555;
            text-decoration: none;
            border-bottom: 1px solid #f3f4f6;
            white-space: nowrap;
        }

        .language-dropdown a:last-child {
            border-bottom: none;
        }

        .language-dropdown a:hover {
            background: #f9fafb;
        }

        .language-dropdown a.active {
            background: #667eea;
            color: white;
        }

        .language-toggle {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            transition: all 0.3s ease;
            cursor: pointer;
            color: #555;
            text-decoration: none;
            white-space: nowrap;
            min-width: fit-content;
        }

        .language-toggle:hover {
            background: #667eea;
            color: white;
        }

        /* Main Content */
        .main-container {
            max-width: 1400px;
            margin: 2rem auto;
            padding: 0 1rem;
        }

        /* Cards */
        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 2rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-bottom: 2rem;
        }

        .card-header {
            border-bottom: 1px solid #e5e7eb;
            padding-bottom: 1rem;
            margin-bottom: 1.5rem;
        }

        .card-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1f2937;
        }

        /* Buttons */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.875rem;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a67d8;
            transform: translateY(-1px);
        }

        .btn-success {
            background: #10b981;
            color: white;
        }

        .btn-success:hover {
            background: #059669;
        }

        .btn-warning {
            background: #f59e0b;
            color: white;
        }

        .btn-warning:hover {
            background: #d97706;
        }

        .btn-danger {
            background: #ef4444;
            color: white;
        }

        .btn-danger:hover {
            background: #dc2626;
        }

        .btn-outline {
            background: transparent;
            border: 2px solid #667eea;
            color: #667eea;
        }

        .btn-outline:hover {
            background: #667eea;
            color: white;
        }

        /* Status badges */
        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .status-active {
            background: #dcfce7;
            color: #166534;
        }

        .status-pending {
            background: #fef3c7;
            color: #92400e;
        }

        .status-completed,
        .status-success {
            background: #dcfce7;
            color: #166534;
        }

        .status-rejected,
        .status-failed {
            background: #fee2e2;
            color: #991b1b;
        }

        /* Grid */
        .grid {
            display: grid;
            gap: 1.5rem;
        }

        .grid-2 {
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        }

        .grid-3 {
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        }

        .grid-4 {
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        }

        /* Forms */
        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #374151;
        }

        .form-input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 0.875rem;
            transition: border-color 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-select {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            background: white;
            font-size: 0.875rem;
        }

        /* Alerts */
        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }

        .alert-success {
            background: #dcfce7;
            color: #166534;
            border: 1px solid #bbf7d0;
        }

        .alert-error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fecaca;
        }

        .alert-warning {
            background: #fef3c7;
            color: #92400e;
            border: 1px solid #fed7aa;
        }

        .alert-info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #bfdbfe;
        }

        /* Mobile Menu Toggle */
        .mobile-menu-toggle {
            display: none;
            background: none;
            border: none;
            font-size: 1.5rem;
            color: #667eea;
            cursor: pointer;
        }

        /* Responsive - chỉ khi thực sự cần thiết */
        @media (max-width: 1200px) {
            .header-container {
                padding: 0 0.5rem;
                gap: 0.5rem;
                align-items: center;
            }

            .nav-links {
                gap: 1rem;
                justify-content: center;
                flex: 1;
                order: 2;
                width: 100%;
                margin-top: 0.5rem;
            }

            .nav-links a {
                padding: 0.5rem 0.75rem;
                font-size: 0.875rem;
            }

            .language-toggle {
                padding: 0.5rem 0.75rem;
                font-size: 0.875rem;
            }

            .logo {
                font-size: 1.25rem;
                order: 1;
            }

            .user-menu {
                order: 3;
            }

            .language-switcher {
                order: 3;
            }
        }

        @media (max-width: 1024px) {
            .nav-links {
                gap: 0.75rem;
            }

            .nav-links a {
                padding: 0.4rem 0.6rem;
                font-size: 0.8rem;
            }

            .language-toggle {
                padding: 0.4rem 0.6rem;
                font-size: 0.8rem;
            }
        }

        @media (max-width: 768px) {
            .header-container {
                padding: 0.5rem;
            }

            .logo {
                font-size: 1.1rem;
            }

            .nav-links {
                gap: 0.5rem;
                margin-top: 0.75rem;
            }

            .nav-links a {
                padding: 0.4rem 0.6rem;
                font-size: 0.8rem;
                white-space: nowrap;
            }

            .language-toggle {
                padding: 0.4rem 0.6rem;
                font-size: 0.8rem;
                white-space: nowrap;
            }

            .main-container {
                padding: 0 0.5rem;
                margin-top: 1rem;
            }

            .card {
                padding: 1.5rem;
            }
        }

        @media (max-width: 480px) {
            .header-container {
                flex-direction: column;
                align-items: stretch;
                gap: 0.5rem;
            }

            .logo {
                text-align: center;
                font-size: 1rem;
            }

            .nav-links {
                flex-direction: column;
                gap: 0.25rem;
                margin-top: 0;
            }

            .nav-links a {
                text-align: center;
                padding: 0.5rem;
                font-size: 0.875rem;
            }

            .user-menu {
                order: 0;
                align-self: center;
            }

            .user-dropdown {
                position: fixed;
                top: auto;
                bottom: 0;
                left: 0;
                right: 0;
                min-width: auto;
                border-radius: 0;
                border-bottom: none;
            }

            .language-dropdown {
                position: fixed;
                top: auto;
                bottom: 0;
                left: 0;
                right: 0;
                min-width: auto;
                border-radius: 0;
                border-bottom: none;
                margin-top: 0;
            }

            .language-switcher {
                align-self: center;
                width: 100%;
                text-align: center;
            }

            .language-toggle {
                justify-content: center;
                width: 100%;
            }

            .main-container {
                margin-top: 0.5rem;
            }

            .card {
                padding: 1rem;
            }

            .grid-4 {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }

            .grid-3 {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            }

            .grid-2 {
                grid-template-columns: 1fr;
            }
        }

        @yield('styles')
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="header-container">
            <a href="{{ route('user.dashboard') }}" class="logo">
                {{ __('user.game_portal') }}
            </a>

            @if(session('user_account'))
            <div class="nav-links">
                <a href="{{ route('user.dashboard') }}" class="{{ request()->routeIs('user.dashboard*') ? 'active' : '' }}">
                    <i class="fas fa-home"></i> {{ __('user.dashboard') }}
                </a>
                <a href="{{ route('user.recharge') }}" class="{{ request()->routeIs('user.recharge*') ? 'active' : '' }}">
                    <i class="fas fa-gem"></i> {{ __('user.recharge') }}
                </a>
                <a href="{{ route('user.withdraw') }}" class="{{ request()->routeIs('user.withdraw*') ? 'active' : '' }}">
                    <i class="fas fa-money-bill-transfer"></i> {{ __('user.withdraw') }}
                </a>
                <a href="{{ route('user.giftcode') }}" class="{{ request()->routeIs('user.giftcode*') ? 'active' : '' }}">
                    <i class="fas fa-gift"></i> {{ __('user.giftcode') }}
                </a>
                <a href="{{ route('user.profile') }}" class="{{ request()->routeIs('user.profile*') ? 'active' : '' }}">
                    <i class="fas fa-user-cog"></i> {{ __('user.profile') }}
                </a>
                <a href="{{ route('user.monthly-card') }}" class="{{ request()->routeIs('user.monthly-card*') ? 'active' : '' }}">
                    <i class="fas fa-credit-card"></i> {{ __('user.monthly_card') }}
                </a>

                <div class="user-menu">
                    <a href="#" onclick="toggleUserMenu()" class="user-toggle">
                        <i class="fas fa-user-circle"></i> {{ session('user_account.username') }}
                        <i class="fas fa-chevron-down"></i>
                    </a>
                    <div class="user-dropdown" id="userDropdown">
                        <a href="{{ route('user.profile') }}">
                            <i class="fas fa-user"></i> {{ __('user.profile') }}
                        </a>
                        <a href="{{ route('user.recharge.history') }}">
                            <i class="fas fa-history"></i> {{ __('user.transaction_history') }}
                        </a>
                        <a href="#" onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                            <i class="fas fa-sign-out-alt"></i> {{ __('user.logout') }}
                        </a>
                    </div>
                </div>

                <!-- Language Switcher -->
                <div class="language-switcher">
                    <a href="#" onclick="toggleLanguageMenu('languageDropdownAuth')" class="language-toggle">
                        <i class="fas fa-globe"></i> {{ __('user.language') }}
                        <i class="fas fa-chevron-down"></i>
                    </a>
                    <div class="language-dropdown" id="languageDropdownAuth">
                        <a href="{{ route('language.switch', 'vi') }}" class="{{ app()->getLocale() === 'vi' ? 'active' : '' }}">
                            🇻🇳 {{ __('user.vietnamese') }}
                        </a>
                        <a href="{{ route('language.switch', 'en') }}" class="{{ app()->getLocale() === 'en' ? 'active' : '' }}">
                            🇺🇸 {{ __('user.english') }}
                        </a>
                    </div>
                </div>

                <form id="logout-form" action="{{ route('user.logout') }}" method="POST" style="display: none;">
                    @csrf
                </form>
            </div>
            @else
            <div class="nav-links">
                <!-- Language Switcher for non-logged in users -->
                <div class="language-switcher">
                    <a href="#" onclick="toggleLanguageMenu('languageDropdownGuest')" class="language-toggle">
                        <i class="fas fa-globe"></i> {{ __('user.language') }}
                        <i class="fas fa-chevron-down"></i>
                    </a>
                    <div class="language-dropdown" id="languageDropdownGuest">
                        <a href="{{ route('language.switch', 'vi') }}" class="{{ app()->getLocale() === 'vi' ? 'active' : '' }}">
                            🇻🇳 {{ __('user.vietnamese') }}
                        </a>
                        <a href="{{ route('language.switch', 'en') }}" class="{{ app()->getLocale() === 'en' ? 'active' : '' }}">
                            🇺🇸 {{ __('user.english') }}
                        </a>
                    </div>
                </div>

                <a href="{{ route('user.login') }}" class="btn btn-outline">
                    <i class="fas fa-sign-in-alt"></i> {{ __('user.login') }}
                </a>
                <a href="{{ route('user.register') }}" class="btn btn-primary">
                    <i class="fas fa-user-plus"></i> {{ __('user.register') }}
                </a>
            </div>
            @endif
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-container">
        @if(session('success'))
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> {{ session('success') }}
            </div>
        @endif

        @if(session('error'))
            <div class="alert alert-error">
                <i class="fas fa-exclamation-circle"></i> {{ session('error') }}
            </div>
        @endif

        @if($errors->any())
            <div class="alert alert-error">
                <i class="fas fa-exclamation-triangle"></i>
                <ul style="margin: 0; padding-left: 1rem;">
                    @foreach($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        @yield('content')
    </div>

    <script>
        function toggleUserMenu() {
            const dropdown = document.getElementById('userDropdown');
            if (dropdown) {
                dropdown.classList.toggle('show');
            }
        }

        function toggleLanguageMenu(dropdownId) {
            const dropdown = document.getElementById(dropdownId);
            if (dropdown) {
                dropdown.classList.toggle('show');
            }
        }

        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            const userMenu = document.querySelector('.user-menu');
            const userDropdown = document.getElementById('userDropdown');
            const languageMenus = document.querySelectorAll('.language-switcher');
            const languageDropdowns = document.querySelectorAll('.language-dropdown');

            // Close user dropdown
            if (userMenu && userDropdown && !userMenu.contains(event.target)) {
                userDropdown.classList.remove('show');
            }

            // Close language dropdowns
            languageMenus.forEach((menu, index) => {
                if (!menu.contains(event.target)) {
                    languageDropdowns[index]?.classList.remove('show');
                }
            });
        });

        @yield('scripts')
    </script>
</body>
</html>
