<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 Performance Test Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: white;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(10px);
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .test-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .test-card h3 {
            margin-bottom: 15px;
            color: #fbbf24;
        }
        
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .test-item:last-child {
            border-bottom: none;
        }
        
        .test-name {
            font-weight: 500;
        }
        
        .test-result {
            font-weight: 600;
            padding: 4px 8px;
            border-radius: 8px;
            font-size: 12px;
        }
        
        .result-fast {
            background: #10b981;
            color: white;
        }
        
        .result-medium {
            background: #f59e0b;
            color: white;
        }
        
        .result-slow {
            background: #ef4444;
            color: white;
        }
        
        .result-pending {
            background: #6b7280;
            color: white;
        }
        
        .btn {
            background: linear-gradient(45deg, #3b82f6, #1d4ed8);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            margin: 10px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);
        }
        
        .btn:disabled {
            background: #6b7280;
            cursor: not-allowed;
            transform: none;
        }
        
        .summary {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 15px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .summary h3 {
            color: #fbbf24;
            margin-bottom: 15px;
        }
        
        .loading {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Performance Test Dashboard</h1>
            <p>Kiểm tra hiệu suất hệ thống Game Admin Panel</p>
        </div>
        
        <div style="text-align: center; margin-bottom: 30px;">
            <button class="btn" onclick="runAllTests()">🔄 Chạy tất cả test</button>
            <button class="btn" onclick="clearResults()">🗑️ Xóa kết quả</button>
        </div>
        
        <div class="test-grid">
            <div class="test-card">
                <h3>🌐 Basic Routes</h3>
                <div class="test-item">
                    <span class="test-name">Health Check</span>
                    <span class="test-result result-pending" id="health-result">Pending</span>
                </div>
                <div class="test-item">
                    <span class="test-name">Home Page</span>
                    <span class="test-result result-pending" id="home-result">Pending</span>
                </div>
                <div class="test-item">
                    <span class="test-name">Admin Login</span>
                    <span class="test-result result-pending" id="admin-login-result">Pending</span>
                </div>
                <div class="test-item">
                    <span class="test-name">User Login</span>
                    <span class="test-result result-pending" id="user-login-result">Pending</span>
                </div>
            </div>
            
            <div class="test-card">
                <h3>🔐 Protected Routes</h3>
                <div class="test-item">
                    <span class="test-name">Admin Users</span>
                    <span class="test-result result-pending" id="admin-users-result">Pending</span>
                </div>
                <div class="test-item">
                    <span class="test-name">Admin Accounts</span>
                    <span class="test-result result-pending" id="admin-accounts-result">Pending</span>
                </div>
                <div class="test-item">
                    <span class="test-name">User Giftcode</span>
                    <span class="test-result result-pending" id="user-giftcode-result">Pending</span>
                </div>
                <div class="test-item">
                    <span class="test-name">Admin Giftcode</span>
                    <span class="test-result result-pending" id="admin-giftcode-result">Pending</span>
                </div>
            </div>
            
            <div class="test-card">
                <h3>🎁 API Endpoints</h3>
                <div class="test-item">
                    <span class="test-name">Giftcode API</span>
                    <span class="test-result result-pending" id="giftcode-api-result">Pending</span>
                </div>
                <div class="test-item">
                    <span class="test-name">Auth API</span>
                    <span class="test-result result-pending" id="auth-api-result">Pending</span>
                </div>
                <div class="test-item">
                    <span class="test-name">User CP API</span>
                    <span class="test-result result-pending" id="usercp-api-result">Pending</span>
                </div>
            </div>
        </div>
        
        <div class="summary">
            <h3>📊 Test Summary</h3>
            <div id="summary-content">
                <p>Nhấn "Chạy tất cả test" để bắt đầu kiểm tra hiệu suất.</p>
            </div>
        </div>
    </div>

    <script>
        const tests = [
            { id: 'health', name: 'Health Check', url: '/health' },
            { id: 'home', name: 'Home Page', url: '/' },
            { id: 'admin-login', name: 'Admin Login', url: '/admin/login' },
            { id: 'user-login', name: 'User Login', url: '/user/login' },
            { id: 'admin-users', name: 'Admin Users', url: '/admin/admin-users' },
            { id: 'admin-accounts', name: 'Admin Accounts', url: '/admin/accounts' },
            { id: 'user-giftcode', name: 'User Giftcode', url: '/user/giftcode' },
            { id: 'admin-giftcode', name: 'Admin Giftcode', url: '/admin/giftcode' },
            { id: 'giftcode-api', name: 'Giftcode API', url: '/api/giftcode', method: 'POST' },
            { id: 'auth-api', name: 'Auth API', url: '/api/login', method: 'POST' },
            { id: 'usercp-api', name: 'User CP API', url: '/api/init' }
        ];

        async function runTest(test) {
            const resultElement = document.getElementById(test.id + '-result');
            resultElement.innerHTML = '<span class="loading"></span>';
            resultElement.className = 'test-result result-pending';

            const startTime = performance.now();
            
            try {
                const options = {
                    method: test.method || 'GET',
                    headers: {
                        'Accept': 'text/html,application/json',
                    }
                };

                if (test.method === 'POST') {
                    options.headers['Content-Type'] = 'application/x-www-form-urlencoded';
                    options.body = 'test=1';
                }

                const response = await fetch(test.url, options);
                const endTime = performance.now();
                const duration = Math.round(endTime - startTime);

                let resultClass = 'result-fast';
                if (duration > 1000) resultClass = 'result-slow';
                else if (duration > 500) resultClass = 'result-medium';

                resultElement.textContent = `${duration}ms`;
                resultElement.className = `test-result ${resultClass}`;

                return { test: test.name, duration, status: response.status, success: true };
            } catch (error) {
                const endTime = performance.now();
                const duration = Math.round(endTime - startTime);
                
                resultElement.textContent = 'Error';
                resultElement.className = 'test-result result-slow';

                return { test: test.name, duration, error: error.message, success: false };
            }
        }

        async function runAllTests() {
            const results = [];
            
            for (const test of tests) {
                const result = await runTest(test);
                results.push(result);
                await new Promise(resolve => setTimeout(resolve, 100)); // Small delay
            }

            updateSummary(results);
        }

        function updateSummary(results) {
            const summaryContent = document.getElementById('summary-content');
            const successful = results.filter(r => r.success);
            const failed = results.filter(r => !r.success);
            const avgTime = successful.length > 0 ? 
                Math.round(successful.reduce((sum, r) => sum + r.duration, 0) / successful.length) : 0;

            let html = `
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                    <div>
                        <strong>✅ Successful:</strong> ${successful.length}/${results.length}
                    </div>
                    <div>
                        <strong>❌ Failed:</strong> ${failed.length}/${results.length}
                    </div>
                    <div>
                        <strong>⚡ Avg Response:</strong> ${avgTime}ms
                    </div>
                    <div>
                        <strong>🏆 Fastest:</strong> ${successful.length > 0 ? Math.min(...successful.map(r => r.duration)) : 0}ms
                    </div>
                </div>
            `;

            if (failed.length > 0) {
                html += '<h4 style="margin-top: 20px; color: #ef4444;">❌ Failed Tests:</h4><ul>';
                failed.forEach(f => {
                    html += `<li>${f.test}: ${f.error || 'Unknown error'}</li>`;
                });
                html += '</ul>';
            }

            summaryContent.innerHTML = html;
        }

        function clearResults() {
            tests.forEach(test => {
                const resultElement = document.getElementById(test.id + '-result');
                resultElement.textContent = 'Pending';
                resultElement.className = 'test-result result-pending';
            });

            document.getElementById('summary-content').innerHTML = 
                '<p>Nhấn "Chạy tất cả test" để bắt đầu kiểm tra hiệu suất.</p>';
        }
    </script>
</body>
</html>
