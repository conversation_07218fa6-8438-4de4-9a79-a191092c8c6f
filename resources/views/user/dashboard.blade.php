@extends('layouts.user')

@section('title', __('user.dashboard') . ' - ' . __('user.portal'))

@section('content')
<!-- Welcome Message -->
<div class="card">
    <h1 style="font-size: 2rem; font-weight: 700; color: #1f2937; margin-bottom: 1rem;">
        🎮 {{ __('user.welcome_message', ['username' => $stats['username'] ?? 'User']) }}
    </h1>
    <p style="color: #6b7280; font-size: 1.1rem;">
        {{ __('user.welcome_description') }}
    </p>
</div>

<!-- Account Info Cards -->
<div class="grid grid-4">
    <!-- Current Coins -->
    <div class="card" style="text-align: center;">
        <div style="background: #fef3c7; color: #d97706; width: 60px; height: 60px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 1rem;">
            <i class="fas fa-coins" style="font-size: 1.5rem;"></i>
        </div>
        <h3 style="font-size: 1.1rem; font-weight: 600; color: #1f2937; margin-bottom: 0.5rem;">{{ __('user.current_diamonds') }}</h3>
        <p style="color: #d97706; font-weight: 700; font-size: 1.2rem;">{{ number_format($stats['coins'] ?? 0) }}</p>
    </div>

    <!-- Account Status -->
    <div class="card" style="text-align: center;">
        <div style="background: #dcfce7; color: #166534; width: 60px; height: 60px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 1rem;">
            <i class="fas fa-user-check" style="font-size: 1.5rem;"></i>
        </div>
        <h3 style="font-size: 1.1rem; font-weight: 600; color: #1f2937; margin-bottom: 0.5rem;">{{ __('user.account_status') }}</h3>
        <p style="color: #059669; font-weight: 600;">{{ $stats['status'] ?? __('user.undefined') }}</p>
    </div>

    <!-- Total Recharged -->
    <div class="card" style="text-align: center;">
        <div style="background: #dbeafe; color: #1e40af; width: 60px; height: 60px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 1rem;">
            <i class="fas fa-chart-line" style="font-size: 1.5rem;"></i>
        </div>
        <h3 style="font-size: 1.1rem; font-weight: 600; color: #1f2937; margin-bottom: 0.5rem;">{{ __('user.total_recharged') }}</h3>
        <p style="color: #1e40af; font-weight: 600;">{{ number_format($stats['total_recharged_vnd'] ?? 0) }} VNĐ</p>
    </div>

    <!-- Last Login -->
    <div class="card" style="text-align: center;">
        <div style="background: #fed7aa; color: #ea580c; width: 60px; height: 60px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 1rem;">
            <i class="fas fa-clock" style="font-size: 1.5rem;"></i>
        </div>
        <h3 style="font-size: 1.1rem; font-weight: 600; color: #1f2937; margin-bottom: 0.5rem;">{{ __('user.last_login') }}</h3>
        <p style="color: #6b7280; font-size: 0.9rem;">
            @if($stats['last_login'])
                {{ is_string($stats['last_login']) ? \Carbon\Carbon::parse($stats['last_login'])->format('d/m/Y H:i') : $stats['last_login']->format('d/m/Y H:i') }}
            @else
                {{ __('user.no_info') }}
            @endif
        </p>
    </div>
</div>

<!-- Quick Actions -->
<div class="card">
    <div class="card-header">
        <h2 class="card-title">
            <i class="fas fa-bolt" style="color: #f59e0b;"></i>
            {{ __('user.quick_actions') }}
        </h2>
    </div>
    <div class="grid grid-4">
        <a href="{{ route('user.recharge') }}" class="btn btn-primary" style="text-align: center; display: flex; flex-direction: column; gap: 0.5rem;">
            <i class="fas fa-gem" style="font-size: 1.5rem;"></i>
            {{ __('user.recharge_diamonds') }}
        </a>
        <a href="{{ route('user.withdraw') }}" class="btn btn-success" style="text-align: center; display: flex; flex-direction: column; gap: 0.5rem;">
            <i class="fas fa-money-bill-transfer" style="font-size: 1.5rem;"></i>
            {{ __('user.withdraw_diamonds') }}
        </a>
        <a href="{{ route('user.giftcode') }}" class="btn btn-warning" style="text-align: center; display: flex; flex-direction: column; gap: 0.5rem;">
            <i class="fas fa-gift" style="font-size: 1.5rem;"></i>
            {{ __('user.enter_giftcode') }}
        </a>
        <a href="{{ route('user.recharge.history') }}" class="btn btn-outline" style="text-align: center; display: flex; flex-direction: column; gap: 0.5rem;">
            <i class="fas fa-history" style="font-size: 1.5rem;"></i>
            {{ __('user.history') }}
        </a>
    </div>
</div>

<!-- Coin Information -->
<div class="card">
    <div class="card-header">
        <h2 class="card-title">
            <i class="fas fa-gem" style="color: #8b5cf6;"></i>
            {{ __('user.diamond_info') }}
        </h2>
    </div>
    <div class="grid grid-3">
        <div style="text-align: center; padding: 1.5rem; background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%); border-radius: 12px;">
            <div style="font-size: 2rem; margin-bottom: 0.5rem;">💰</div>
            <h3 style="font-size: 1.1rem; font-weight: 600; color: #7c3aed; margin-bottom: 0.5rem;">{{ __('user.current_diamonds') }}</h3>
            <p style="font-size: 1.5rem; font-weight: 700; color: #8b5cf6;">{{ number_format($stats['coins'] ?? 0) }} 💎</p>
        </div>
        <div style="text-align: center; padding: 1.5rem; background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%); border-radius: 12px;">
            <div style="font-size: 2rem; margin-bottom: 0.5rem;">📈</div>
            <h3 style="font-size: 1.1rem; font-weight: 600; color: #1e40af; margin-bottom: 0.5rem;">{{ __('user.total_recharged_amount') }}</h3>
            <p style="font-size: 1.2rem; font-weight: 600; color: #2563eb;">{{ number_format($stats['total_recharged_vnd'] ?? 0) }} VNĐ</p>
        </div>
        <div style="text-align: center; padding: 1.5rem; background: linear-gradient(135deg, #fce7f3 0%, #fbcfe8 100%); border-radius: 12px;">
            <div style="font-size: 2rem; margin-bottom: 0.5rem;">💸</div>
            <h3 style="font-size: 1.1rem; font-weight: 600; color: #be185d; margin-bottom: 0.5rem;">{{ __('user.transactions') }}</h3>
            <p style="font-size: 1.2rem; font-weight: 600; color: #db2777;">{{ number_format($stats['total_transactions'] ?? 0) }}</p>
        </div>
    </div>
</div>

<!-- Game Statistics -->
<div class="card">
    <div class="card-header">
        <h2 class="card-title">
            <i class="fas fa-gamepad" style="color: #8b5cf6;"></i>
            {{ __('user.game_stats') }}
        </h2>
    </div>
    <div class="grid grid-4">
        <div style="text-align: center; padding: 1.5rem; background: linear-gradient(135deg, #f3e8ff 0%, #e9d5ff 100%); border-radius: 12px;">
            <div style="font-size: 2rem; margin-bottom: 0.5rem;">🎁</div>
            <h3 style="font-size: 1.1rem; font-weight: 600; color: #7c3aed; margin-bottom: 0.5rem;">{{ __('user.giftcodes_used') }}</h3>
            <p style="font-size: 1.5rem; font-weight: 700; color: #8b5cf6;">{{ number_format($stats['total_giftcodes_used'] ?? 0) }}</p>
        </div>
        <div style="text-align: center; padding: 1.5rem; background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%); border-radius: 12px;">
            <div style="font-size: 2rem; margin-bottom: 0.5rem;">👤</div>
            <h3 style="font-size: 1.1rem; font-weight: 600; color: #059669; margin-bottom: 0.5rem;">{{ __('user.characters') }}</h3>
            <p style="font-size: 1.5rem; font-weight: 700; color: #10b981;">{{ number_format($stats['total_characters'] ?? 0) }}</p>
        </div>
        <div style="text-align: center; padding: 1.5rem; background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%); border-radius: 12px;">
            <div style="font-size: 2rem; margin-bottom: 0.5rem;">⭐</div>
            <h3 style="font-size: 1.1rem; font-weight: 600; color: #dc2626; margin-bottom: 0.5rem;">{{ __('user.highest_level') }}</h3>
            <p style="font-size: 1.5rem; font-weight: 700; color: #ef4444;">{{ number_format($stats['highest_level'] ?? 0) }}</p>
        </div>
        <div style="text-align: center; padding: 1.5rem; background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%); border-radius: 12px;">
            <div style="font-size: 2rem; margin-bottom: 0.5rem;">💎</div>
            <h3 style="font-size: 1.1rem; font-weight: 600; color: #d97706; margin-bottom: 0.5rem;">{{ __('user.coins_withdrawn') }}</h3>
            <p style="font-size: 1.5rem; font-weight: 700; color: #f59e0b;">{{ number_format($stats['total_withdrawn'] ?? 0) }}</p>
        </div>
    </div>
</div>

<!-- Account Details -->
<div class="card">
    <div class="card-header">
        <h2 class="card-title">
            <i class="fas fa-user" style="color: #667eea;"></i>
            {{ __('user.account_details') }}
        </h2>
    </div>
    <div class="grid grid-2">
        <div>
            <h3 style="font-size: 1.1rem; font-weight: 600; color: #374151; margin-bottom: 1rem;">{{ __('user.basic_info') }}</h3>
            <div style="display: flex; flex-direction: column; gap: 0.75rem;">
                <div style="display: flex; justify-content: space-between; padding: 0.5rem 0; border-bottom: 1px solid #f3f4f6;">
                    <span style="color: #6b7280;">{{ __('user.username_label') }}:</span>
                    <span style="font-weight: 600;">{{ $user->UserName }}</span>
                </div>
                <div style="display: flex; justify-content: space-between; padding: 0.5rem 0; border-bottom: 1px solid #f3f4f6;">
                    <span style="color: #6b7280;">{{ __('user.email_label') }}:</span>
                    <span style="font-weight: 600;">{{ $user->Email ?: __('user.not_updated') }}</span>
                </div>
                <div style="display: flex; justify-content: space-between; padding: 0.5rem 0;">
                    <span style="color: #6b7280;">{{ __('user.status_label') }}:</span>
                    <span class="status-badge status-active">{{ $user->getStatusText() }}</span>
                </div>
            </div>
        </div>
        <div>
            <h3 style="font-size: 1.1rem; font-weight: 600; color: #374151; margin-bottom: 1rem;">{{ __('user.time_info') }}</h3>
            <div style="display: flex; flex-direction: column; gap: 0.75rem;">
                <div style="display: flex; justify-content: space-between; padding: 0.5rem 0; border-bottom: 1px solid #f3f4f6;">
                    <span style="color: #6b7280;">{{ __('user.created_date') }}:</span>
                    <span style="font-weight: 600;">
                        @if($user->CreateTime)
                            {{ is_string($user->CreateTime) ? \Carbon\Carbon::parse($user->CreateTime)->format('d/m/Y H:i') : $user->CreateTime->format('d/m/Y H:i') }}
                        @else
                            N/A
                        @endif
                    </span>
                </div>
                <div style="display: flex; justify-content: space-between; padding: 0.5rem 0;">
                    <span style="color: #6b7280;">{{ __('user.last_login_time') }}:</span>
                    <span style="font-weight: 600;">
                        @if($user->LastLoginTime)
                            {{ is_string($user->LastLoginTime) ? \Carbon\Carbon::parse($user->LastLoginTime)->format('d/m/Y H:i') : $user->LastLoginTime->format('d/m/Y H:i') }}
                        @else
                            N/A
                        @endif
                    </span>
                </div>
            </div>
        </div>
    </div>
</div>

@endsection
