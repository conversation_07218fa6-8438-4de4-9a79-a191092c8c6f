@extends('layouts.user')

@section('title', __('user.monthly_card_title'))

@section('styles')
<style>
    .monthly-card-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 15px;
        padding: 30px;
        margin-bottom: 30px;
        color: white;
        text-align: center;
    }
    .page-title {
        font-size: 28px;
        font-weight: 700;
        margin-bottom: 10px;
    }
    .page-subtitle {
        opacity: 0.9;
        font-size: 16px;
    }
    .user-info {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(16px);
        border-radius: 15px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        padding: 20px;
        margin-bottom: 30px;
        color: white;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    .user-coins {
        font-size: 18px;
        font-weight: 600;
    }
    .packages-grid {
        display: flex;
        justify-content: center;
        margin-bottom: 40px;
    }
    .package-card {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(16px);
        border-radius: 15px;
        border: 2px solid #f59e0b;
        background: linear-gradient(135deg, rgba(245, 158, 11, 0.2) 0%, rgba(217, 119, 6, 0.2) 100%);
        padding: 30px;
        color: white;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        max-width: 450px;
        width: 100%;
    }
    .package-card:hover {
        transform: translateY(-5px);
        border-color: rgba(255, 255, 255, 0.4);
    }
    .package-card.premium {
        border: 2px solid #f59e0b;
        background: linear-gradient(135deg, rgba(245, 158, 11, 0.2) 0%, rgba(217, 119, 6, 0.2) 100%);
    }
    .package-card.vip {
        border: 2px solid #8b5cf6;
        background: linear-gradient(135deg, rgba(139, 92, 246, 0.2) 0%, rgba(124, 58, 237, 0.2) 100%);
    }
    .package-header {
        text-align: center;
        margin-bottom: 20px;
    }
    .package-name {
        font-size: 20px;
        font-weight: 700;
        margin-bottom: 5px;
    }
    .package-price {
        font-size: 28px;
        font-weight: 800;
        color: #fbbf24;
        margin-bottom: 10px;
    }
    .package-badge {
        background: linear-gradient(45deg, #f59e0b, #d97706);
        color: white;
        padding: 5px 15px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
        text-align: center;
        margin-bottom: 20px;
        text-transform: uppercase;
        letter-spacing: 1px;
    }
    .package-value {
        text-align: center;
        margin: 15px 0;
        padding: 10px;
        background: rgba(34, 197, 94, 0.2);
        border-radius: 8px;
        border: 1px solid rgba(34, 197, 94, 0.3);
    }
    .package-value small {
        color: #22c55e;
        font-weight: 600;
    }
    .package-features {
        list-style: none;
        padding: 0;
        margin: 20px 0;
    }
    .package-features li {
        padding: 8px 0;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        display: flex;
        align-items: center;
    }
    .package-features li:last-child {
        border-bottom: none;
    }
    .package-features li::before {
        content: '✓';
        color: #10b981;
        font-weight: bold;
        margin-right: 10px;
    }
    .btn {
        padding: 12px 24px;
        border: none;
        border-radius: 8px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s;
        text-decoration: none;
        display: inline-block;
        text-align: center;
        font-size: 14px;
        width: 100%;
    }
    .btn-primary {
        background: linear-gradient(45deg, #3b82f6, #2563eb);
        color: white;
    }
    .btn-success {
        background: linear-gradient(45deg, #10b981, #059669);
        color: white;
    }
    .btn-warning {
        background: linear-gradient(45deg, #f59e0b, #d97706);
        color: white;
    }
    .btn:hover {
        transform: translateY(-2px);
    }
    .btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
    }
    .active-cards {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(16px);
        border-radius: 15px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        padding: 25px;
        margin-bottom: 30px;
        color: white;
    }
    .section-title {
        font-size: 20px;
        font-weight: 600;
        margin-bottom: 20px;
        text-align: center;
    }
    .active-card {
        background: rgba(16, 185, 129, 0.2);
        border: 1px solid rgba(16, 185, 129, 0.3);
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 15px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    .card-info h4 {
        margin: 0 0 5px 0;
        color: #10b981;
    }
    .card-info p {
        margin: 0;
        opacity: 0.8;
        font-size: 14px;
    }
    .card-actions {
        display: flex;
        gap: 10px;
        align-items: center;
    }
    .claim-btn {
        background: linear-gradient(45deg, #22c55e, #16a34a);
        border: none;
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        font-weight: 600;
        transition: all 0.3s ease;
        flex: 1;
        font-size: 14px;
    }
    .claim-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(34, 197, 94, 0.3);
    }
    .cancel-btn {
        background: linear-gradient(45deg, #ef4444, #dc2626);
        border: none;
        color: white;
        padding: 12px 16px;
        border-radius: 8px;
        font-weight: 600;
        transition: all 0.3s ease;
        font-size: 12px;
    }
    .cancel-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3);
    }
    .history-section {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(16px);
        border-radius: 15px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        padding: 25px;
        color: white;
    }
    .history-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 15px;
    }
    .history-table th,
    .history-table td {
        padding: 12px;
        text-align: left;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }
    .history-table th {
        background: rgba(255, 255, 255, 0.1);
        font-weight: 600;
    }
    .status-active {
        color: #10b981;
        font-weight: 600;
    }
    .status-expired {
        color: #ef4444;
        font-weight: 600;
    }
    .no-data {
        text-align: center;
        padding: 40px;
        opacity: 0.7;
    }

    /* Mobile Responsive */
    @media (max-width: 768px) {
        .monthly-card-container {
            padding: 15px;
        }
        .packages-grid {
            grid-template-columns: 1fr;
            gap: 20px;
        }
        .user-info {
            flex-direction: column;
            gap: 15px;
            text-align: center;
        }
        .active-card {
            flex-direction: column;
            gap: 15px;
            text-align: center;
        }
        .history-table {
            font-size: 12px;
        }
        .history-table th,
        .history-table td {
            padding: 8px 4px;
        }
    }
</style>
@endsection

@section('content')
<div class="monthly-card-container">
    <!-- Page Header -->
    <div class="page-header">
        <h1 class="page-title">🎫 {{ __('user.monthly_card') }} Premium</h1>
        <p class="page-subtitle">{{ __('user.monthly_card_description') }}</p>
    </div>

    <!-- User Info -->
    <div class="user-info">
        <div>
            <h3>👤 {{ $user->UserName }}</h3>
            <p>{{ __('user.account_info') }}</p>
        </div>
        <div class="user-coins">
            💎 {{ number_format($userCoins->coins) }} {{ __('user.diamonds') }}
        </div>
    </div>

    <!-- Active Cards -->
    @if($activeCards->count() > 0)
    <div class="active-cards">
        <h3 class="section-title">🎯 {{ __('user.monthly_card_status') }}</h3>
        @foreach($activeCards as $card)
        <div class="active-card">
            <div class="card-info">
                <h4>{{ $card->package_name }}</h4>
                @if($card->character_name)
                    <p>🎮 {{ __('user.character') }}: <strong>{{ $card->character_name }}</strong></p>
                @endif
                <p>{{ __('user.expired') }}: {{ date('d/m/Y H:i', strtotime($card->expires_at)) }}</p>
                <p>{{ __('user.claimed') }}: {{ $card->days_claimed }}/{{ $card->duration_days }} {{ __('user.days') }}</p>
                @php
                    $canClaim = true;
                    if ($card->last_claimed_at) {
                        $lastClaimed = \Carbon\Carbon::parse($card->last_claimed_at)->format('Y-m-d');
                        $today = now()->format('Y-m-d');
                        $canClaim = $lastClaimed !== $today;
                    }
                @endphp
                @if(!$canClaim)
                    <p style="color: #fbbf24;">✅ {{ __('user.already_claimed_today') }}</p>
                @endif
            </div>
            <div class="card-actions">
                <button class="btn btn-success claim-btn" onclick="claimDaily({{ $card->id }})" {{ !$canClaim ? 'disabled' : '' }}>
                    @if($canClaim)
                        🎁 {{ __('user.claim_reward') }} (+{{ number_format($card->daily_reward_coins) }} 💎)
                    @else
                        ✅ {{ __('user.claimed') }}
                    @endif
                </button>
                <button class="btn btn-danger cancel-btn" onclick="cancelCard({{ $card->id }})" title="{{ __('user.cancel_card') }}">
                    ❌
                </button>
            </div>
        </div>
        @endforeach
    </div>
    @endif

    <!-- Package Card -->
    <div class="packages-grid">
        @foreach($packages as $key => $package)
        <div class="package-card premium">
            <div class="package-header">
                <h3 class="package-name">🎫 {{ $package['name'] }}</h3>
                <div class="package-price">{{ number_format($package['cost_coins']) }} 💎</div>
                <div class="package-badge">{{ __('user.unique_package') }}</div>
            </div>

            <ul class="package-features">
                <li>💎 {{ number_format($package['daily_reward_coins']) }} {{ __('user.daily_diamonds') }}</li>
                <li>⏰ {{ __('user.duration') }} {{ $package['duration_days'] }} {{ __('user.days') }}</li>
                <li>🎁 {{ __('user.bonus_items_on_purchase') }}</li>
                <li>📦 {{ __('user.daily_premium_items') }}</li>
                <li>⭐ {{ __('user.exclusive_vip_privileges') }}</li>
                <li>🔥 {{ __('user.total_value') }}: {{ number_format($package['daily_reward_coins'] * $package['duration_days']) }} {{ __('user.diamonds') }}</li>
            </ul>

            <div class="package-value">
                <small>{{ __('user.save_amount') }} {{ number_format($package['daily_reward_coins'] * $package['duration_days'] - $package['cost_coins']) }} {{ __('user.diamonds') }}!</small>
            </div>

            <button class="btn btn-primary" onclick="showCharacterSelection('{{ $key }}')">
                💳 {{ __('user.buy_now') }} - {{ number_format($package['cost_coins']) }} 💎
            </button>
        </div>
        @endforeach
    </div>

    <!-- Purchase History -->
    @if($purchaseHistory->count() > 0)
    <div class="history-section">
        <h3 class="section-title">📋 {{ __('user.purchase_history') }}</h3>
        <table class="history-table">
            <thead>
                <tr>
                    <th>{{ __('user.package_name') }}</th>
                    <th>{{ __('user.character') }}</th>
                    <th>{{ __('user.purchase_date') }}</th>
                    <th>{{ __('user.duration') }}</th>
                    <th>{{ __('user.status') }}</th>
                </tr>
            </thead>
            <tbody>
                @foreach($purchaseHistory as $history)
                <tr>
                    <td>{{ $history->package_name }}</td>
                    <td>
                        @if($history->character_name)
                            🎮 {{ $history->character_name }}
                        @else
                            <span style="opacity: 0.6;">{{ __('user.unknown') }}</span>
                        @endif
                    </td>
                    <td>{{ date('d/m/Y H:i', strtotime($history->created_at)) }}</td>
                    <td>{{ $history->duration_days }} {{ __('user.days') }}</td>
                    <td>
                        @if($history->status === 'active' && strtotime($history->expires_at) > time())
                            <span class="status-active">{{ __('user.activated') }}</span>
                        @else
                            <span class="status-expired">{{ __('user.expired') }}</span>
                        @endif
                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    @endif
</div>

<!-- Character Selection Modal -->
<div id="characterModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.8); z-index: 1000; justify-content: center; align-items: center;">
    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 15px; padding: 30px; max-width: 500px; width: 90%; color: white;">
        <h3 style="text-align: center; margin-bottom: 20px;">🎮 {{ __('user.select_character_activate') }}</h3>
        <p style="text-align: center; margin-bottom: 25px; opacity: 0.9;">{{ __('user.card_will_activate_for_character') }}</p>

        <div id="characterList" style="margin-bottom: 25px;">
            <div style="text-align: center; padding: 20px;">
                <div style="font-size: 24px; margin-bottom: 10px;">⏳</div>
                <div>{{ __('user.loading_characters') }}</div>
            </div>
        </div>

        <div style="display: flex; gap: 10px; justify-content: center;">
            <button id="confirmPurchase" class="btn btn-success" style="flex: 1;" disabled>
                💳 {{ __('user.confirm_purchase') }}
            </button>
            <button onclick="closeCharacterModal()" class="btn btn-danger" style="flex: 1;">
                ❌ {{ __('user.cancel') }}
            </button>
        </div>
    </div>
</div>

<script>
let selectedPackageType = null;
let selectedCharacterId = null;

function showCharacterSelection(packageType) {
    selectedPackageType = packageType;
    selectedCharacterId = null;

    // Show modal
    document.getElementById('characterModal').style.display = 'flex';

    // Load characters
    loadUserCharacters();
}

function loadUserCharacters() {
    const characterList = document.getElementById('characterList');
    const confirmBtn = document.getElementById('confirmPurchase');

    characterList.innerHTML = `
        <div style="text-align: center; padding: 20px;">
            <div style="font-size: 24px; margin-bottom: 10px;">⏳</div>
            <div>{{ __('user.loading_characters') }}</div>
        </div>
    `;

    fetch('/user/monthly-card/get-characters')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.characters && data.characters.length > 0) {
                let html = '<div style="margin-bottom: 15px; font-weight: 600; text-align: center;">{{ __('user.select_character_label') }}:</div>';

                data.characters.forEach(character => {
                    html += `
                        <div onclick="selectCharacter(${character.rid})"
                             style="background: rgba(255,255,255,0.1); border: 2px solid rgba(255,255,255,0.2); border-radius: 10px; padding: 15px; margin-bottom: 10px; cursor: pointer; transition: all 0.3s ease;"
                             onmouseover="this.style.background='rgba(255,255,255,0.2)'"
                             onmouseout="this.style.background='rgba(255,255,255,0.1)'"
                             id="char-${character.rid}">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div class="character-info">
                                    <div style="font-weight: 600; font-size: 16px;">🎮 ${character.rname}</div>
                                    <div style="opacity: 0.8; font-size: 14px;">Level ${character.level}</div>
                                </div>
                                <div class="character-icon" style="font-size: 20px;">⚪</div>
                            </div>
                        </div>
                    `;
                });

                characterList.innerHTML = html;
            } else {
                characterList.innerHTML = `
                    <div style="text-align: center; padding: 20px;">
                        <div style="font-size: 24px; margin-bottom: 10px;">😔</div>
                        <div>{{ __('user.no_characters_found') }}</div>
                        <div style="font-size: 14px; opacity: 0.8; margin-top: 10px;">{{ __('user.create_character_first') }}</div>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error loading characters:', error);
            characterList.innerHTML = `
                <div style="text-align: center; padding: 20px;">
                    <div style="font-size: 24px; margin-bottom: 10px;">❌</div>
                    <div>{{ __('user.error_loading_characters') }}</div>
                </div>
            `;
        });
}

function selectCharacter(characterId) {
    // Remove previous selection
    document.querySelectorAll('[id^="char-"]').forEach(el => {
        el.style.border = '2px solid rgba(255,255,255,0.2)';
        const iconEl = el.querySelector('.character-icon');
        if (iconEl) {
            iconEl.innerHTML = '⚪';
        }
    });

    // Select new character
    const selectedEl = document.getElementById(`char-${characterId}`);
    if (selectedEl) {
        selectedEl.style.border = '2px solid #10b981';
        const iconEl = selectedEl.querySelector('.character-icon');
        if (iconEl) {
            iconEl.innerHTML = '✅';
        }
    }

    selectedCharacterId = characterId;
    document.getElementById('confirmPurchase').disabled = false;
}

function closeCharacterModal() {
    document.getElementById('characterModal').style.display = 'none';
    selectedPackageType = null;
    selectedCharacterId = null;
}

function purchasePackage(packageType, characterId) {
    if (!characterId) {
        alert('Vui lòng chọn nhân vật để kích hoạt thẻ tháng');
        return;
    }

    if (!confirm('Bạn có chắc chắn muốn mua gói thẻ tháng này?')) {
        return;
    }

    fetch('/user/monthly-card/purchase', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            package_type: packageType,
            character_id: characterId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            closeCharacterModal();
            location.reload();
        } else {
            alert('Lỗi: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Có lỗi xảy ra khi mua thẻ tháng');
    });
}

// Confirm purchase button handler
document.getElementById('confirmPurchase').addEventListener('click', function() {
    if (selectedPackageType && selectedCharacterId) {
        purchasePackage(selectedPackageType, selectedCharacterId);
    }
});

function claimDaily(cardId) {
    fetch('/user/monthly-card/claim-daily', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            card_id: cardId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            location.reload();
        } else {
            alert('Lỗi: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Có lỗi xảy ra khi nhận thưởng');
    });
}

function cancelCard(cardId) {
    if (confirm('Bạn có chắc chắn muốn hủy thẻ tháng này? Hành động này không thể hoàn tác và bạn sẽ mất tất cả lợi ích còn lại!')) {
        fetch(`/user/monthly-card/${cardId}/cancel`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                location.reload();
            } else {
                alert('Lỗi: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Có lỗi xảy ra khi hủy thẻ tháng');
        });
    }
}
</script>
@endsection
