@extends('layouts.user')

@section('title', __('user.profile_title'))

@section('content')

<!-- Profile Header -->
<div class="card">
    <div class="card-header">
        <h3 class="card-title">
            <i class="fas fa-user"></i>
            {{ __('user.personal_info') }}
        </h3>
    </div>
    <div class="grid grid-2">
        <div>
            <div style="display: flex; flex-direction: column; gap: 1rem;">
                <div>
                    <label style="font-weight: 500; color: #374151;">{{ __('user.username_label') }}:</label>
                    <div style="color: #6b7280; margin-top: 0.25rem;">{{ $user->UserName }}</div>
                </div>
                <div>
                    <label style="font-weight: 500; color: #374151;">{{ __('user.email_label') }}:</label>
                    <div style="color: #6b7280; margin-top: 0.25rem;">{{ $user->Email }}</div>
                </div>
                <div>
                    <label style="font-weight: 500; color: #374151;">{{ __('user.phone_number') }}:</label>
                    <div style="color: #6b7280; margin-top: 0.25rem;">
                        {{ $user->Phone ?: __('user.not_updated') }}
                    </div>
                </div>
                <div>
                    <label style="font-weight: 500; color: #374151;">{{ __('user.account_status') }}:</label>
                    <div style="margin-top: 0.25rem;">
                        <span class="status-badge status-active">{{ __('user.active') }}</span>
                    </div>
                </div>
            </div>
        </div>
        <div>
            <div style="text-align: center; padding: 2rem; background: #f9fafb; border-radius: 12px;">
                <div style="width: 80px; height: 80px; background: #667eea; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 1rem;">
                    <i class="fas fa-user" style="font-size: 2rem; color: white;"></i>
                </div>
                <div style="font-weight: 600; color: #374151; margin-bottom: 0.5rem;">
                    {{ $user->UserName }}
                </div>
                <div style="color: #6b7280; font-size: 0.875rem;">
                    {{ __('user.member_since') }} {{ date('d/m/Y') }}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Game Account Linking -->
<div class="card">
    <div class="card-header">
        <h3 class="card-title">
            <i class="fas fa-gamepad"></i>
            {{ __('user.link_game_account') }}
        </h3>
    </div>
    @if(session('user_account.game_account_id'))
        <div style="background: #f0fdf4; border: 1px solid #bbf7d0; border-radius: 12px; padding: 1.5rem; margin-bottom: 1.5rem;">
            <div style="display: flex; align-items: center; gap: 1rem; margin-bottom: 1rem;">
                <i class="fas fa-check-circle" style="color: #10b981; font-size: 1.5rem;"></i>
                <div>
                    <div style="font-weight: 600; color: #166534;">{{ __('user.linked_successfully') }}</div>
                    <div style="color: #6b7280; font-size: 0.875rem;">{{ __('user.game_account_linked') }}</div>
                </div>
            </div>
            <div style="background: rgba(255,255,255,0.7); border-radius: 8px; padding: 1rem;">
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                    <div>
                        <label style="font-weight: 500; color: #374151;">{{ __('user.game_username') }}:</label>
                        <div style="color: #6b7280; margin-top: 0.25rem;">{{ __('user.loading') }}</div>
                    </div>
                    <div>
                        <label style="font-weight: 500; color: #374151;">{{ __('user.current_coins') }}:</label>
                        <div style="color: #f59e0b; font-weight: 600; margin-top: 0.25rem;">{{ __('user.loading') }}</div>
                    </div>
                    <div>
                        <label style="font-weight: 500; color: #374151;">{{ __('user.total_recharged_profile') }}:</label>
                        <div style="color: #6b7280; margin-top: 0.25rem;">{{ __('user.loading') }}</div>
                    </div>
                </div>
            </div>
        </div>
        <button onclick="unlinkGameAccount()" class="btn btn-danger">
            <i class="fas fa-unlink"></i>
            {{ __('user.unlink_account') }}
        </button>
    @else
        <div style="background: #fef3c7; border: 1px solid #fed7aa; border-radius: 12px; padding: 1.5rem; margin-bottom: 1.5rem;">
            <div style="display: flex; align-items: center; gap: 1rem; margin-bottom: 1rem;">
                <i class="fas fa-exclamation-triangle" style="color: #f59e0b; font-size: 1.5rem;"></i>
                <div>
                    <div style="font-weight: 600; color: #92400e;">{{ __('user.not_linked_game') }}</div>
                    <div style="color: #6b7280; font-size: 0.875rem;">{{ __('user.link_to_use_features') }}</div>
                </div>
            </div>
        </div>
        
        <form method="POST" action="#" id="linkGameForm">
            @csrf
            <div class="form-group">
                <label for="game_username" class="form-label">{{ __('user.game_account_name') }}</label>
                <input
                    type="text"
                    name="game_username"
                    id="game_username"
                    class="form-input"
                    placeholder="{{ __('user.enter_game_username') }}"
                    required
                >
                <div style="color: #6b7280; font-size: 0.75rem; margin-top: 0.25rem;">
                    {{ __('user.enter_exact_username') }}
                </div>
            </div>
            
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-link"></i>
                {{ __('user.link_account_button') }}
            </button>
        </form>
    @endif
</div>



<!-- Account Statistics -->
<div class="card">
    <div class="card-header">
        <h3 class="card-title">
            <i class="fas fa-chart-bar"></i>
            {{ __('user.account_statistics') }}
        </h3>
    </div>
    <div class="grid grid-4">
        <div style="text-align: center; padding: 1.5rem; background: #f0f9ff; border-radius: 8px;">
            <i class="fas fa-coins" style="font-size: 2rem; color: #3b82f6; margin-bottom: 1rem;"></i>
            <div style="font-size: 1.5rem; font-weight: 700; color: #1e40af;">{{ __('user.loading') }}</div>
            <div style="color: #6b7280; font-size: 0.875rem;">{{ __('user.current_diamonds') }}</div>
        </div>
        <div style="text-align: center; padding: 1.5rem; background: #f0fdf4; border-radius: 8px;">
            <i class="fas fa-credit-card" style="font-size: 2rem; color: #10b981; margin-bottom: 1rem;"></i>
            <div style="font-size: 1.5rem; font-weight: 700; color: #166534;">{{ __('user.loading') }}</div>
            <div style="color: #6b7280; font-size: 0.875rem;">{{ __('user.total_recharged') }} (VND)</div>
        </div>
        <div style="text-align: center; padding: 1.5rem; background: #fef3c7; border-radius: 8px;">
            <i class="fas fa-exchange-alt" style="font-size: 2rem; color: #f59e0b; margin-bottom: 1rem;"></i>
            <div style="font-size: 1.5rem; font-weight: 700; color: #92400e;">{{ __('user.loading') }}</div>
            <div style="color: #6b7280; font-size: 0.875rem;">{{ __('user.transactions') }}</div>
        </div>
        <div style="text-align: center; padding: 1.5rem; background: #fdf2f8; border-radius: 8px;">
            <i class="fas fa-gift" style="font-size: 2rem; color: #ec4899; margin-bottom: 1rem;"></i>
            <div style="font-size: 1.5rem; font-weight: 700; color: #be185d;">{{ __('user.loading') }}</div>
            <div style="color: #6b7280; font-size: 0.875rem;">{{ __('user.giftcodes_used') }}</div>
        </div>
    </div>
</div>

<!-- Danger Zone -->
<div class="card" style="border: 2px solid #fecaca;">
    <div class="card-header">
        <h3 class="card-title" style="color: #dc2626;">
            <i class="fas fa-exclamation-triangle"></i>
            {{ __('user.danger_zone') }}
        </h3>
    </div>
    <div style="background: #fee2e2; border-radius: 8px; padding: 1.5rem;">
        <h4 style="color: #991b1b; margin-bottom: 1rem;">{{ __('user.delete_account') }}</h4>
        <p style="color: #6b7280; margin-bottom: 1rem;">
            {{ __('user.delete_account_warning') }}
        </p>
        <button onclick="deleteAccount()" class="btn btn-danger">
            <i class="fas fa-trash"></i>
            {{ __('user.delete_account_button') }}
        </button>
    </div>
</div>
@endsection

@section('scripts')
// Form submissions
document.getElementById('linkGameForm').addEventListener('submit', function(e) {
    e.preventDefault();
    const username = document.getElementById('game_username').value;
    alert(`Chức năng liên kết tài khoản game "${username}" đang được phát triển!`);
});



function unlinkGameAccount() {
    if (confirm('Bạn có chắc chắn muốn hủy liên kết tài khoản game?')) {
        alert('Chức năng hủy liên kết đang được phát triển!');
    }
}

function deleteAccount() {
    if (confirm('Bạn có chắc chắn muốn xóa tài khoản? Hành động này không thể hoàn tác!')) {
        if (confirm('Xác nhận lần cuối: Xóa vĩnh viễn tài khoản?')) {
            alert('Chức năng xóa tài khoản đang được phát triển!');
        }
    }
}

// Load account statistics
function loadAccountStats() {
    fetch('{{ route("user.profile.stats") }}')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const stats = data.stats;
                // Update the display
                const statElements = document.querySelectorAll('[style*="font-size: 1.5rem"]');
                if (statElements.length >= 4) {
                    statElements[0].textContent = new Intl.NumberFormat().format(stats.coins);
                    statElements[1].textContent = new Intl.NumberFormat().format(stats.total_recharged);
                    statElements[2].textContent = stats.transactions;
                    statElements[3].textContent = stats.giftcodes_used;
                }
            }
        })
        .catch(error => {
            console.error('Error loading stats:', error);
        });
}

// Show notification
function showNotification(type, message) {
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        border-radius: 8px;
        color: white;
        font-weight: 600;
        z-index: 1000;
        max-width: 400px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    `;

    if (type === 'success') {
        notification.style.background = '#10b981';
        notification.innerHTML = '<i class="fas fa-check-circle"></i> ' + message;
    } else {
        notification.style.background = '#ef4444';
        notification.innerHTML = '<i class="fas fa-exclamation-circle"></i> ' + message;
    }

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.style.opacity = '0';
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Load data when page loads
document.addEventListener('DOMContentLoaded', loadAccountStats);

// Password confirmation validation
const newPassword = document.getElementById('new_password');
const confirmPassword = document.getElementById('new_password_confirmation');

function validatePasswordMatch() {
    if (newPassword.value !== confirmPassword.value) {
        confirmPassword.setCustomValidity('Mật khẩu xác nhận không khớp');
    } else {
        confirmPassword.setCustomValidity('');
    }
}

newPassword.addEventListener('input', validatePasswordMatch);
confirmPassword.addEventListener('input', validatePasswordMatch);
@endsection
