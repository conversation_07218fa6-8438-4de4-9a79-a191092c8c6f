@extends('layouts.user')

@section('title', __('user.transaction_history_title'))

@section('content')
<!-- Filter Section -->
<div class="card">
    <div class="card-header">
        <h3 class="card-title">
            <i class="fas fa-filter"></i>
            {{ __('user.filter_transactions') }}
        </h3>
    </div>
    <form method="GET" action="{{ route('user.recharge.history') }}" id="filterForm">
        <div class="grid grid-4">
            <div class="form-group">
                <label for="status" class="form-label">{{ __('user.filter_by_status') }}</label>
                <select name="status" id="status" class="form-select">
                    <option value="">{{ __('user.all_transactions') }}</option>
                    <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>{{ __('user.processing_status') }}</option>
                    <option value="processing" {{ request('status') == 'processing' ? 'selected' : '' }}>{{ __('user.processing_status') }}</option>
                    <option value="completed" {{ request('status') == 'completed' ? 'selected' : '' }}>{{ __('user.success_status') }}</option>
                    <option value="rejected" {{ request('status') == 'rejected' ? 'selected' : '' }}>{{ __('user.rejected_status') }}</option>
                </select>
            </div>

            <div class="form-group">
                <label for="method" class="form-label">{{ __('user.filter_by_method') }}</label>
                <select name="method" id="method" class="form-select">
                    <option value="">{{ __('user.all_transactions') }}</option>
                    <option value="card" {{ request('method') == 'card' ? 'selected' : '' }}>{{ __('user.card_method') }}</option>
                    <option value="bank_transfer" {{ request('method') == 'bank_transfer' ? 'selected' : '' }}>{{ __('user.bank_transfer_method') }}</option>
                    <option value="paypal" {{ request('method') == 'paypal' ? 'selected' : '' }}>PayPal</option>
                </select>
            </div>

            <div class="form-group">
                <label for="date_from" class="form-label">{{ __('user.from_date') }}</label>
                <input
                    type="date"
                    name="date_from"
                    id="date_from"
                    class="form-input"
                    value="{{ request('date_from') }}"
                >
            </div>

            <div class="form-group">
                <label for="date_to" class="form-label">{{ __('user.to_date') }}</label>
                <input
                    type="date"
                    name="date_to"
                    id="date_to"
                    class="form-input"
                    value="{{ request('date_to') }}"
                >
            </div>
        </div>
        
        <div style="display: flex; gap: 1rem;">
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-search"></i>
                {{ __('user.apply_filter') }}
            </button>
            <a href="{{ route('user.recharge.history') }}" class="btn btn-outline">
                <i class="fas fa-times"></i>
                {{ __('user.clear_filter') }}
            </a>
        </div>
    </form>
</div>

<!-- Summary Stats -->
<div class="grid grid-4">
    <div class="card">
        <div style="text-align: center;">
            <div style="font-size: 2rem; font-weight: 700; color: #3b82f6; margin-bottom: 0.5rem;">
                {{ $payments->total() }}
            </div>
            <div style="color: #6b7280; font-size: 0.875rem;">{{ __('user.total_transactions') }}</div>
        </div>
    </div>

    <div class="card">
        <div style="text-align: center;">
            <div style="font-size: 2rem; font-weight: 700; color: #10b981; margin-bottom: 0.5rem;">
                {{ $payments->where('status', 'completed')->count() }}
            </div>
            <div style="color: #6b7280; font-size: 0.875rem;">{{ __('user.completed') }}</div>
        </div>
    </div>

    <div class="card">
        <div style="text-align: center;">
            <div style="font-size: 2rem; font-weight: 700; color: #f59e0b; margin-bottom: 0.5rem;">
                {{ $payments->where('status', 'pending')->count() }}
            </div>
            <div style="color: #6b7280; font-size: 0.875rem;">{{ __('user.processing_status') }}</div>
        </div>
    </div>

    <div class="card">
        <div style="text-align: center;">
            <div style="font-size: 2rem; font-weight: 700; color: #ef4444; margin-bottom: 0.5rem;">
                {{ $payments->where('status', 'rejected')->count() }}
            </div>
            <div style="color: #6b7280; font-size: 0.875rem;">{{ __('user.rejected_status') }}</div>
        </div>
    </div>
</div>

<!-- Transaction History -->
<div class="card">
    <div class="card-header">
        <h3 class="card-title">
            <i class="fas fa-history"></i>
            {{ __('user.transaction_history') }}
        </h3>
    </div>
    
    @if($payments->count() > 0)
        <div style="overflow-x: auto;">
            <table style="width: 100%; border-collapse: collapse;">
                <thead>
                    <tr style="border-bottom: 2px solid #e5e7eb;">
                        <th style="text-align: left; padding: 1rem; font-weight: 600; color: #374151;">{{ __('user.transaction_id') }}</th>
                        <th style="text-align: left; padding: 1rem; font-weight: 600; color: #374151;">{{ __('user.transaction_method') }}</th>
                        <th style="text-align: left; padding: 1rem; font-weight: 600; color: #374151;">{{ __('user.amount_vnd') }}</th>
                        <th style="text-align: left; padding: 1rem; font-weight: 600; color: #374151;">{{ __('user.coins') }}</th>
                        <th style="text-align: left; padding: 1rem; font-weight: 600; color: #374151;">{{ __('user.status') }}</th>
                        <th style="text-align: left; padding: 1rem; font-weight: 600; color: #374151;">{{ __('user.transaction_time') }}</th>
                        <th style="text-align: center; padding: 1rem; font-weight: 600; color: #374151;">{{ __('user.actions') }}</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($payments as $payment)
                        <tr style="border-bottom: 1px solid #f3f4f6; {{ $payment->status == 'pending' ? 'background: #fefce8;' : '' }}">
                            <td style="padding: 1rem;">
                                <div style="font-family: monospace; font-weight: 600;">#{{ $payment->id }}</div>
                                @if($payment->transaction_id)
                                    <div style="font-size: 0.75rem; color: #6b7280;">{{ $payment->transaction_id }}</div>
                                @endif
                            </td>
                            <td style="padding: 1rem;">
                                <div style="display: flex; align-items: center; gap: 0.5rem;">
                                    <span style="font-size: 1.25rem;">
                                        @if($payment->type == 'card')
                                            💳
                                        @elseif($payment->type == 'bank')
                                            🏦
                                        @else
                                            💰
                                        @endif
                                    </span>
                                    <span>
                                        @if($payment->type == 'card')
                                            {{ __('user.card_method') }}
                                        @elseif($payment->type == 'bank')
                                            {{ __('user.bank_transfer_method') }}
                                        @else
                                            {{ ucfirst($payment->type) }}
                                        @endif
                                    </span>
                                </div>
                            </td>
                            <td style="padding: 1rem;">
                                <div style="font-weight: 600; color: #374151;">{{ number_format($payment->amount_vnd) }}đ</div>
                            </td>
                            <td style="padding: 1rem;">
                                <div style="font-weight: 600; color: #f59e0b;">{{ number_format($payment->coins_added) }}</div>
                            </td>
                            <td style="padding: 1rem;">
                                <span class="status-badge
                                    @if($payment->status == 'pending') status-pending
                                    @elseif($payment->status == 'processing') status-processing
                                    @elseif($payment->status == 'completed') status-completed
                                    @elseif($payment->status == 'rejected') status-rejected
                                    @endif">
                                    @if($payment->status == 'pending')
                                        {{ __('user.processing_status') }}
                                    @elseif($payment->status == 'processing')
                                        {{ __('user.processing_status') }}
                                    @elseif($payment->status == 'completed')
                                        {{ __('user.success_status') }}
                                    @elseif($payment->status == 'rejected')
                                        {{ __('user.rejected_status') }}
                                    @else
                                        {{ ucfirst($payment->status) }}
                                    @endif
                                </span>
                                @if($payment->note)
                                    <div style="font-size: 0.75rem; color: #6b7280; margin-top: 0.25rem;">
                                        {{ Str::limit($payment->note, 50) }}
                                    </div>
                                @endif
                            </td>
                            <td style="padding: 1rem;">
                                <div style="color: #6b7280; font-size: 0.875rem;">
                                    {{ \Carbon\Carbon::parse($payment->created_at)->format('d/m/Y') }}
                                </div>
                                <div style="color: #6b7280; font-size: 0.75rem;">
                                    {{ \Carbon\Carbon::parse($payment->created_at)->format('H:i:s') }}
                                </div>
                            </td>
                            <td style="padding: 1rem; text-align: center;">
                                <a href="{{ route('user.recharge.show', $payment->id) }}" class="btn btn-outline" style="font-size: 0.75rem; padding: 0.5rem 1rem;">
                                    <i class="fas fa-eye"></i>
                                    {{ __('user.view_details') }}
                                </a>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        <div style="margin-top: 2rem; display: flex; justify-content: center;">
            {{ $payments->appends(request()->query())->links() }}
        </div>
    @else
        <div style="text-align: center; color: #6b7280; padding: 3rem;">
            <i class="fas fa-inbox" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
            <h3 style="margin-bottom: 0.5rem;">{{ __('user.no_transactions_found') }}</h3>
            <p style="margin-bottom: 2rem;">
                @if(request()->hasAny(['status', 'method', 'date_from', 'date_to']))
                    {{ __('user.no_matching_filter') }}
                @else
                    {{ __('user.no_transactions_yet') }}
                @endif
            </p>
            <a href="{{ route('user.recharge') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i>
                {{ __('user.first_recharge') }}
            </a>
        </div>
    @endif
</div>

<!-- Quick Actions -->
<div class="card">
    <div class="card-header">
        <h3 class="card-title">
            <i class="fas fa-bolt"></i>
            {{ __('user.quick_actions') }}
        </h3>
    </div>
    <div class="grid grid-3">
        <a href="{{ route('user.recharge') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i>
            {{ __('user.recharge_more') }}
        </a>
        <button onclick="exportHistory()" class="btn btn-outline">
            <i class="fas fa-download"></i>
            {{ __('user.export_history') }}
        </button>
        <button onclick="refreshPage()" class="btn btn-outline">
            <i class="fas fa-sync-alt"></i>
            {{ __('user.refresh_page') }}
        </button>
    </div>
</div>

<!-- Help Section -->
<div class="card">
    <div class="card-header">
        <h3 class="card-title">
            <i class="fas fa-question-circle"></i>
            {{ __('user.transaction_status') }}
        </h3>
    </div>
    <div class="grid grid-2">
        <div>
            <h4 style="color: #374151; margin-bottom: 1rem;">{{ __('user.status_meanings') }}:</h4>
            <div style="display: flex; flex-direction: column; gap: 0.75rem;">
                <div style="display: flex; align-items: center; gap: 0.5rem;">
                    <span class="status-badge status-pending">{{ __('user.processing_status') }}</span>
                    <span style="color: #6b7280; font-size: 0.875rem;">{{ __('user.transaction_waiting_admin') }}</span>
                </div>
                <div style="display: flex; align-items: center; gap: 0.5rem;">
                    <span class="status-badge status-processing">{{ __('user.processing_status') }}</span>
                    <span style="color: #6b7280; font-size: 0.875rem;">{{ __('user.admin_checking_processing') }}</span>
                </div>
                <div style="display: flex; align-items: center; gap: 0.5rem;">
                    <span class="status-badge status-completed">{{ __('user.success_status') }}</span>
                    <span style="color: #6b7280; font-size: 0.875rem;">{{ __('user.coins_added_to_account') }}</span>
                </div>
                <div style="display: flex; align-items: center; gap: 0.5rem;">
                    <span class="status-badge status-rejected">{{ __('user.rejected_status') }}</span>
                    <span style="color: #6b7280; font-size: 0.875rem;">{{ __('user.transaction_rejected_see_note') }}</span>
                </div>
            </div>
        </div>
        <div>
            <h4 style="color: #374151; margin-bottom: 1rem;">{{ __('user.processing_times') }}:</h4>
            <ul style="color: #6b7280; font-size: 0.875rem; margin: 0; padding-left: 1rem;">
                <li>{{ __('user.card_processing_time') }}</li>
                <li>{{ __('user.bank_processing_time') }}</li>
                <li>{{ __('user.paypal_processing_time') }}</li>
                <li>{{ __('user.working_hours') }}</li>
            </ul>
            <div style="background: #fef3c7; border: 1px solid #fed7aa; border-radius: 8px; padding: 1rem; margin-top: 1rem;">
                <div style="font-weight: 500; color: #92400e; margin-bottom: 0.5rem;">
                    <i class="fas fa-info-circle"></i> {{ __('user.note') }}
                </div>
                <div style="color: #92400e; font-size: 0.875rem;">
                    {{ __('user.contact_admin_if_over_1hour') }}
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
function exportHistory() {
    alert('{{ __('user.export_function_developing') }}');
}

function refreshPage() {
    window.location.reload();
}

// Auto refresh pending transactions every 30 seconds
setInterval(function() {
    const pendingRows = document.querySelectorAll('tr[style*="background: #fefce8"]');
    if (pendingRows.length > 0) {
        // In a real implementation, this would make an AJAX call to check status
        console.log('Checking pending transactions...');
    }
}, 30000);

// Set max date to today
document.getElementById('date_from').max = new Date().toISOString().split('T')[0];
document.getElementById('date_to').max = new Date().toISOString().split('T')[0];

// Auto submit form when date changes
document.getElementById('date_from').addEventListener('change', function() {
    if (this.value && document.getElementById('date_to').value) {
        document.getElementById('filterForm').submit();
    }
});

document.getElementById('date_to').addEventListener('change', function() {
    if (this.value && document.getElementById('date_from').value) {
        document.getElementById('filterForm').submit();
    }
});
@endsection
