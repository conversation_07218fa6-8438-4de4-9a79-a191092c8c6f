@extends('layouts.user')

@section('title', __('user.transaction_detail_title'))

@section('content')
<div class="container">
    <div class="page-header">
        <h1>💰 {{ __('user.transaction_detail_header') }}</h1>
        <p>{{ __('user.transaction_detail_description', ['id' => $recharge->id]) }}</p>
    </div>

    <div class="content-card">
        <!-- Transaction Status -->
        <div class="status-section">
            <div class="status-badge status-{{ $recharge->status }}">
                @switch($recharge->status)
                    @case('pending')
                        <i class="fas fa-clock"></i> {{ __('user.waiting_for_processing') }}
                        @break
                    @case('completed')
                        <i class="fas fa-check-circle"></i> {{ __('user.completed') }}
                        @break
                    @case('failed')
                        <i class="fas fa-times-circle"></i> {{ __('user.failed') }}
                        @break
                    @case('cancelled')
                        <i class="fas fa-ban"></i> {{ __('user.cancelled') }}
                        @break
                    @default
                        <i class="fas fa-question-circle"></i> {{ ucfirst($recharge->status) }}
                @endswitch
            </div>
        </div>

        <!-- Transaction Details -->
        <div class="details-grid">
            <div class="detail-item">
                <label>{{ __('user.transaction_id') }}</label>
                <div class="detail-value">
                    <strong>#{{ $recharge->id }}</strong>
                    <button onclick="copyToClipboard('{{ $recharge->id }}')" class="btn-copy">
                        <i class="fas fa-copy"></i>
                    </button>
                </div>
            </div>

            <div class="detail-item">
                <label>{{ __('user.amount_vnd') }}</label>
                <div class="detail-value amount">
                    {{ number_format($recharge->amount_vnd) }} VNĐ
                </div>
            </div>

            <div class="detail-item">
                <label>{{ __('user.diamonds_received') }}</label>
                <div class="detail-value coins">
                    <i class="fas fa-coins"></i> {{ number_format($recharge->coins_added) }} {{ __('user.coins') }}
                </div>
            </div>

            <div class="detail-item">
                <label>{{ __('user.payment_method') }}</label>
                <div class="detail-value">
                    @if($recharge->type === 'bank')
                        <i class="fas fa-university"></i> {{ __('user.bank_transfer_method') }}
                    @elseif($recharge->type === 'card')
                        <i class="fas fa-credit-card"></i> {{ __('user.phone_card') }}
                    @elseif($recharge->type === 'manual')
                        <i class="fas fa-user-cog"></i> {{ __('user.manual_recharge_admin') }}
                    @else
                        <i class="fas fa-money-bill"></i> {{ ucfirst($recharge->type) }}
                    @endif
                </div>
            </div>

            <div class="detail-item">
                <label>{{ __('user.creation_time') }}</label>
                <div class="detail-value">
                    <i class="fas fa-calendar"></i> @safeDate($recharge->created_at)
                </div>
            </div>

            @if($recharge->completed_at)
            <div class="detail-item">
                <label>{{ __('user.completion_time') }}</label>
                <div class="detail-value">
                    <i class="fas fa-check"></i> @safeDate($recharge->completed_at)
                </div>
            </div>
            @endif

            @if($recharge->note)
            <div class="detail-item full-width">
                <label>{{ __('user.note') }}</label>
                <div class="detail-value note">
                    {{ $recharge->note }}
                </div>
            </div>
            @endif

            <div class="detail-item">
                <label>{{ __('user.category') }}</label>
                <div class="detail-value">
                    @if($recharge->recharge_category === 'founder_team')
                        <i class="fas fa-crown"></i> {{ __('user.team_founder') }}
                    @else
                        <i class="fas fa-user"></i> {{ __('user.customer') }}
                    @endif
                </div>
            </div>
        </div>

        <!-- Payment Information -->
        @if($recharge->type === 'bank')
        <div class="payment-info">
            <h3><i class="fas fa-info-circle"></i> {{ __('user.bank_transfer_info') }}</h3>
            <div class="bank-details">
                <div class="bank-item">
                    <label>{{ __('user.bank') }}</label>
                    <div>Vietcombank</div>
                </div>
                <div class="bank-item">
                    <label>{{ __('user.account_number') }}</label>
                    <div>**********</div>
                </div>
                <div class="bank-item">
                    <label>{{ __('user.account_holder') }}</label>
                    <div>NGUYEN VAN A</div>
                </div>
                <div class="bank-item">
                    <label>{{ __('user.transfer_content') }}</label>
                    <div class="transfer-content">
                        NAP {{ $recharge->id }} {{ $user->Username }}
                        <button onclick="copyToClipboard('NAP {{ $recharge->id }} {{ $user->Username }}')" class="btn-copy">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        @endif

        <!-- Admin Information -->
        @if($recharge->admin_username)
        <div class="admin-info">
            <h3><i class="fas fa-user-shield"></i> {{ __('user.processing_info') }}</h3>
            <div class="admin-details">
                <div class="admin-item">
                    <label>{{ __('user.processing_admin') }}</label>
                    <div>{{ $recharge->admin_username }}</div>
                </div>
                <div class="admin-item">
                    <label>IP Address</label>
                    <div>{{ $recharge->ip_address }}</div>
                </div>
            </div>
        </div>
        @endif

        <!-- Action Buttons -->
        <div class="action-buttons">
            <a href="{{ route('user.recharge.history') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> {{ __('user.back_to_history') }}
            </a>

            @if($recharge->status === 'pending')
            <button onclick="cancelTransaction()" class="btn btn-danger">
                <i class="fas fa-times"></i> {{ __('user.cancel_transaction') }}
            </button>
            @endif

            @if($recharge->status === 'completed')
            <a href="{{ route('user.recharge') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> {{ __('user.recharge_more_coins') }}
            </a>
            @endif

            <button onclick="window.print()" class="btn btn-outline">
                <i class="fas fa-print"></i> {{ __('user.print_receipt') }}
            </button>
        </div>
    </div>
</div>

<style>
.page-header {
    text-align: center;
    margin-bottom: 30px;
}

.page-header h1 {
    color: #2c3e50;
    margin-bottom: 10px;
}

.status-section {
    text-align: center;
    margin-bottom: 30px;
}

.status-badge {
    display: inline-block;
    padding: 12px 24px;
    border-radius: 25px;
    font-weight: 600;
    font-size: 16px;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
    border: 2px solid #ffeaa7;
}

.status-completed {
    background: #d4edda;
    color: #155724;
    border: 2px solid #00b894;
}

.status-failed {
    background: #f8d7da;
    color: #721c24;
    border: 2px solid #e17055;
}

.status-cancelled {
    background: #f1f3f4;
    color: #5f6368;
    border: 2px solid #9e9e9e;
}

.details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.detail-item {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    border-left: 4px solid #007bff;
}

.detail-item.full-width {
    grid-column: 1 / -1;
}

.detail-item label {
    display: block;
    font-weight: 600;
    color: #6c757d;
    margin-bottom: 8px;
    font-size: 14px;
}

.detail-value {
    font-size: 16px;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 10px;
}

.detail-value.amount {
    font-size: 20px;
    font-weight: 700;
    color: #e74c3c;
}

.detail-value.coins {
    font-size: 18px;
    font-weight: 600;
    color: #f39c12;
}

.detail-value.note {
    background: white;
    padding: 10px;
    border-radius: 5px;
    border: 1px solid #dee2e6;
    font-style: italic;
}

.btn-copy {
    background: none;
    border: none;
    color: #007bff;
    cursor: pointer;
    padding: 5px;
    border-radius: 3px;
    transition: all 0.3s ease;
}

.btn-copy:hover {
    background: #007bff;
    color: white;
}

.payment-info {
    background: #e3f2fd;
    padding: 25px;
    border-radius: 10px;
    margin-bottom: 30px;
    border: 1px solid #bbdefb;
}

.payment-info h3 {
    color: #1976d2;
    margin-bottom: 20px;
    font-size: 18px;
}

.bank-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.bank-item {
    background: white;
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #e3f2fd;
}

.bank-item label {
    display: block;
    font-weight: 600;
    color: #666;
    margin-bottom: 5px;
    font-size: 13px;
}

.bank-item div {
    font-weight: 600;
    color: #2c3e50;
}

.transfer-content {
    display: flex;
    align-items: center;
    gap: 10px;
    font-family: monospace;
    background: #f8f9fa;
    padding: 8px;
    border-radius: 4px;
    border: 1px dashed #007bff;
}

.admin-info {
    background: #fff3cd;
    padding: 25px;
    border-radius: 10px;
    margin-bottom: 30px;
    border: 1px solid #ffeaa7;
}

.admin-info h3 {
    color: #856404;
    margin-bottom: 20px;
    font-size: 18px;
}

.admin-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.admin-item {
    background: white;
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #ffeaa7;
}

.admin-item label {
    display: block;
    font-weight: 600;
    color: #666;
    margin-bottom: 5px;
    font-size: 13px;
}

.admin-item div {
    font-weight: 600;
    color: #2c3e50;
}

.action-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.btn {
    padding: 12px 24px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
}

.btn-outline {
    background: white;
    color: #007bff;
    border: 2px solid #007bff;
}

.btn-outline:hover {
    background: #007bff;
    color: white;
}

@media (max-width: 768px) {
    .details-grid {
        grid-template-columns: 1fr;
    }
    
    .bank-details {
        grid-template-columns: 1fr;
    }
    
    .action-buttons {
        flex-direction: column;
    }
}

@media print {
    .action-buttons {
        display: none;
    }
    
    .page-header {
        border-bottom: 2px solid #000;
        padding-bottom: 20px;
    }
}
</style>

<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Show success message
        const toast = document.createElement('div');
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #28a745;
            color: white;
            padding: 12px 20px;
            border-radius: 5px;
            z-index: 1000;
            font-weight: 600;
        `;
        toast.textContent = '{{ __('user.copied_to_clipboard') }}';
        document.body.appendChild(toast);
        
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 2000);
    });
}

function cancelTransaction() {
    if (confirm('Bạn có chắc chắn muốn hủy giao dịch này?')) {
        // Send cancel request
        fetch(`/user/recharge/{{ $recharge->id }}/cancel`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Có lỗi xảy ra: ' + data.message);
            }
        })
        .catch(error => {
            alert('Có lỗi xảy ra khi hủy giao dịch');
        });
    }
}
</script>
@endsection
