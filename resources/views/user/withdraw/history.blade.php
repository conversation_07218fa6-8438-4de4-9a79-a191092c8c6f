@extends('layouts.user')

@section('title', 'Lịch sử rút coin')

@section('content')
<div class="container">
    <div class="page-header">
        <h1>📋 Lịch sử rút coin</h1>
        <p>Xem lại các giao dịch rút coin của bạn</p>
    </div>

    <!-- Filter Section -->
    <div class="filter-section">
        <form method="GET" action="{{ route('user.withdraw.history') }}" class="filter-form">
            <div class="filter-group">
                <label>Từ ngày</label>
                <input type="date" name="date_from" value="{{ request('date_from') }}" class="form-control">
            </div>
            <div class="filter-group">
                <label>Đến ngày</label>
                <input type="date" name="date_to" value="{{ request('date_to') }}" class="form-control">
            </div>
            <div class="filter-actions">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search"></i> <PERSON><PERSON><PERSON>
                </button>
                <a href="{{ route('user.withdraw.history') }}" class="btn btn-secondary">
                    <i class="fas fa-refresh"></i> Reset
                </a>
            </div>
        </form>
    </div>

    <!-- Statistics -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-coins"></i>
            </div>
            <div class="stat-info">
                <div class="stat-value">{{ number_format($withdraws->sum('coins_spent')) }}</div>
                <div class="stat-label">Tổng coin đã rút</div>
            </div>
        </div>
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-list"></i>
            </div>
            <div class="stat-info">
                <div class="stat-value">{{ $withdraws->total() }}</div>
                <div class="stat-label">Tổng giao dịch</div>
            </div>
        </div>
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-calendar"></i>
            </div>
            <div class="stat-info">
                <div class="stat-value">{{ $withdraws->where('created_at', '>=', now()->startOfMonth())->count() }}</div>
                <div class="stat-label">Giao dịch tháng này</div>
            </div>
        </div>
    </div>

    <!-- Transactions Table -->
    <div class="content-card">
        @if($withdraws->count() > 0)
        <div class="table-responsive">
            <table class="transactions-table">
                <thead>
                    <tr>
                        <th>Mã GD</th>
                        <th>Số kim cương</th>
                        <th>Nhân vật</th>
                        <th>Thời gian</th>
                        <th>Trạng thái</th>
                        <th>Thao tác</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($withdraws as $withdraw)
                    <tr>
                        <td>
                            <div class="transaction-id">
                                #{{ $withdraw->id }}
                            </div>
                        </td>
                        <td>
                            <div class="amount">
                                <i class="fas fa-coins"></i>
                                {{ number_format($withdraw->coins_spent) }}
                            </div>
                        </td>
                        <td>
                            <div class="character">
                                <i class="fas fa-user"></i>
                                {{ $withdraw->character_name ?? 'N/A' }}
                            </div>
                        </td>
                        <td>
                            <div class="datetime">
                                <div class="date">{{ \Carbon\Carbon::parse($withdraw->created_at)->format('d/m/Y') }}</div>
                                <div class="time">{{ \Carbon\Carbon::parse($withdraw->created_at)->format('H:i:s') }}</div>
                            </div>
                        </td>
                        <td>
                            <span class="status-badge status-completed">
                                <i class="fas fa-check-circle"></i>
                                Hoàn thành
                            </span>
                        </td>
                        <td>
                            <div class="actions">
                                <a href="{{ route('user.withdraw.show', $withdraw->id) }}" class="btn btn-sm btn-info">
                                    <i class="fas fa-eye"></i> Xem
                                </a>
                            </div>
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="pagination-wrapper">
            {{ $withdraws->appends(request()->query())->links('pagination.custom') }}
        </div>
        @else
        <div class="empty-state">
            <div class="empty-icon">
                <i class="fas fa-inbox"></i>
            </div>
            <h3>Chưa có giao dịch rút coin nào</h3>
            <p>Bạn chưa thực hiện giao dịch rút coin nào. Hãy bắt đầu rút coin để chuyển vào game!</p>
            <a href="{{ route('user.withdraw') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Rút coin ngay
            </a>
        </div>
        @endif
    </div>
</div>

<style>
.page-header {
    text-align: center;
    margin-bottom: 30px;
}

.filter-section {
    background: white;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.filter-form {
    display: grid;
    grid-template-columns: 1fr 1fr auto;
    gap: 20px;
    align-items: end;
}

.filter-group label {
    display: block;
    font-weight: 600;
    margin-bottom: 5px;
    color: #555;
}

.filter-actions {
    display: flex;
    gap: 10px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    padding: 25px;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 20px;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #28a745, #20c997);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
}

.stat-value {
    font-size: 24px;
    font-weight: 700;
    color: #2c3e50;
}

.stat-label {
    color: #6c757d;
    font-size: 14px;
}

.transactions-table {
    width: 100%;
    border-collapse: collapse;
}

.transactions-table th {
    background: #f8f9fa;
    padding: 15px;
    text-align: left;
    font-weight: 600;
    color: #555;
    border-bottom: 2px solid #dee2e6;
}

.transactions-table td {
    padding: 15px;
    border-bottom: 1px solid #dee2e6;
    vertical-align: middle;
}

.transaction-id {
    font-family: monospace;
    font-weight: 600;
    color: #007bff;
}

.amount {
    font-weight: 600;
    color: #28a745;
    font-size: 16px;
}

.character {
    color: #007bff;
    font-weight: 500;
}

.datetime .date {
    font-weight: 600;
    color: #2c3e50;
}

.datetime .time {
    font-size: 12px;
    color: #6c757d;
}

.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
}

.status-completed {
    background: #d4edda;
    color: #155724;
}

.actions {
    display: flex;
    gap: 5px;
}

.btn {
    padding: 8px 16px;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 600;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
}

.btn-info {
    background: #17a2b8;
    color: white;
}

.btn-info:hover {
    background: #138496;
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
}

.empty-icon {
    font-size: 64px;
    color: #dee2e6;
    margin-bottom: 20px;
}

.empty-state h3 {
    color: #6c757d;
    margin-bottom: 10px;
}

.empty-state p {
    color: #6c757d;
    margin-bottom: 30px;
}

.pagination-wrapper {
    margin-top: 30px;
    display: flex;
    justify-content: center;
}

@media (max-width: 768px) {
    .filter-form {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .table-responsive {
        overflow-x: auto;
    }
    
    .transactions-table {
        min-width: 600px;
    }
}
</style>
@endsection
