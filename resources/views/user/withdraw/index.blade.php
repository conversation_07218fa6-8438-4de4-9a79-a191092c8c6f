@extends('layouts.user')

@section('title', __('user.withdraw_title'))

@section('content')
<!-- Current Balance -->
<div class="grid grid-2">
    <!-- Web Coins -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-gem" style="color: #8b5cf6;"></i>
                {{ __('user.diamonds') }} {{ __('user.website') }}
            </h3>
        </div>
        <div style="text-align: center;">
            <div style="font-size: 2.5rem; font-weight: 700; color: #3b82f6; margin-bottom: 0.5rem;">
                {{ number_format($userCoins->coins ?? 0) }}
            </div>
            <div style="color: #6b7280; font-size: 1rem;">
                {{ __('user.available_diamonds') }}
            </div>
            <div style="color: #6b7280; font-size: 0.875rem; margin-top: 0.5rem;">
                {{ __('user.total_recharged_amount') }}: {{ number_format($userCoins->total_recharged ?? 0) }}đ
            </div>
        </div>
    </div>

    <!-- Game Coins -->
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-gamepad" style="color: #10b981;"></i>
                Kim cương {{ __('user.game') }} ({{ $user->getGameUserId() }})
            </h3>
        </div>
        <div style="text-align: center;">
            <div style="font-size: 2.5rem; font-weight: 700; color: #10b981; margin-bottom: 0.5rem;">
                {{ number_format($gameMoney->realmoney ?? 0) }}
            </div>
            <div style="color: #6b7280; font-size: 1rem;">
                Kim cương {{ __('user.in_game') }}
            </div>
            <div style="color: #6b7280; font-size: 0.875rem; margin-top: 0.5rem;">
                {{ __('user.characters') }}: {{ $gameCharacters->count() }} characters
            </div>
        </div>
    </div>
</div>

<!-- Game Characters -->
@if($gameCharacters->count() > 0)
<div class="card">
    <div class="card-header">
        <h3 class="card-title">
            <i class="fas fa-users" style="color: #8b5cf6;"></i>
            {{ __('user.characters') }} {{ __('user.in_game') }}
        </h3>
    </div>
    <div class="grid grid-4">
        @foreach($gameCharacters as $character)
            <div style="background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 1rem; text-align: center;">
                <div style="font-weight: 600; color: #374151; margin-bottom: 0.5rem;">
                    {{ $character->rname }}
                </div>
                <div style="color: #6b7280; font-size: 0.875rem;">
                    ID: {{ $character->rid }}
                </div>
            </div>
        @endforeach
    </div>
</div>
@endif

<!-- Withdraw Limits Info -->
<div class="card">
    <div class="card-header">
        <h3 class="card-title">
            <i class="fas fa-info-circle"></i>
            {{ __('user.withdraw_info') }}
        </h3>
    </div>
    <div class="grid grid-3">
        <div style="text-align: center; padding: 1.5rem; background: #f0f9ff; border-radius: 8px;">
            <i class="fas fa-coins" style="font-size: 2rem; color: #3b82f6; margin-bottom: 1rem;"></i>
            <div style="font-size: 1.25rem; font-weight: 600; color: #1e40af;">{{ number_format($userCoins->coins ?? 0) }}</div>
            <div style="color: #6b7280; font-size: 0.875rem;">Số dư hiện tại</div>
        </div>
        <div style="text-align: center; padding: 1.5rem; background: #f0fdf4; border-radius: 8px;">
            <i class="fas fa-calendar-day" style="font-size: 2rem; color: #10b981; margin-bottom: 1rem;"></i>
            <div style="font-size: 1.25rem; font-weight: 600; color: #166534;">{{ number_format($stats['remaining_today']) }}</div>
            <div style="color: #6b7280; font-size: 0.875rem;">{{ __('user.remaining_today') }}</div>
        </div>
        <div style="text-align: center; padding: 1.5rem; background: #fef3c7; border-radius: 8px;">
            <i class="fas fa-exchange-alt" style="font-size: 2rem; color: #f59e0b; margin-bottom: 1rem;"></i>
            <div style="font-size: 1.25rem; font-weight: 600; color: #92400e;">1:10</div>
            <div style="color: #6b7280; font-size: 0.875rem;">{{ __('user.conversion_rate') }}</div>
        </div>
    </div>
</div>

<!-- Withdraw Form -->
<div class="card">
    <div class="card-header">
        <h3 class="card-title">
            <i class="fas fa-money-bill-transfer"></i>
            {{ __('user.withdraw_to_game') }}
        </h3>
    </div>
    <div style="margin-bottom: 1.5rem;">
        <div style="background: #f0f9ff; border: 1px solid #bae6fd; border-radius: 12px; padding: 1.5rem; margin-bottom: 1.5rem;">
            <div style="font-weight: 500; color: #0369a1; margin-bottom: 0.5rem;">
                <i class="fas fa-info-circle"></i> {{ __('user.withdraw_guide') }}
            </div>
            <ul style="color: #6b7280; font-size: 0.875rem; margin: 0; padding-left: 1rem;">
                <li>{{ __('user.select_character_receive') }}</li>
                <li>{{ __('user.coin_transfer_instant') }}</li>
                <li>Nhập số kim cương muốn rút (tối thiểu 1 kim cương)</li>
                <li>{{ __('user.daily_limit') }}: 500,000 kim cương</li>
                <li>{{ __('user.conversion_rate') }}: 1 kim cương web = 10 kim cương game</li>
            </ul>
        </div>
    </div>

    <form method="POST" action="{{ route('user.withdraw.post') }}" id="withdrawForm">
        @csrf

        <div class="grid grid-2">
            <div class="form-group">
                <label for="character_id" class="form-label">{{ __('user.select_character_receive') }}</label>
                <select
                    name="character_id"
                    id="character_id"
                    class="form-select"
                    required
                >
                    <option value="">{{ __('user.select_character_placeholder') }}</option>
                    @foreach($gameCharacters as $character)
                        <option value="{{ $character->rid }}" {{ old('character_id') == $character->rid ? 'selected' : '' }}>
                            {{ $character->rname }} (ID: {{ $character->rid }})
                        </option>
                    @endforeach
                </select>
                <div style="color: #6b7280; font-size: 0.75rem; margin-top: 0.25rem;">
                    {{ __('user.coin_transfer_note') }}
                </div>
                @error('character_id')
                    <div style="color: #ef4444; font-size: 0.75rem; margin-top: 0.25rem;">{{ $message }}</div>
                @enderror
            </div>

            <div class="form-group">
                <label for="amount" class="form-label">{{ __('user.withdraw_amount') }}</label>
                <input
                    type="number"
                    name="amount"
                    id="amount"
                    class="form-input"
                    value="{{ old('amount') }}"
                    placeholder="{{ __('user.withdraw_amount') }}"
                    required
                    min="1"
                >
                <div style="color: #6b7280; font-size: 0.75rem; margin-top: 0.25rem;">
                    Số dư hiện tại: {{ number_format($userCoins->coins ?? 0) }} kim cương - Còn lại hôm nay: {{ number_format($stats['remaining_today']) }} kim cương
                </div>
                @error('amount')
                    <div style="color: #ef4444; font-size: 0.75rem; margin-top: 0.25rem;">{{ $message }}</div>
                @enderror
            </div>
        </div>

        <div style="background: #fef3c7; border: 1px solid #fed7aa; border-radius: 12px; padding: 1.5rem; margin-bottom: 1.5rem;">
            <div style="font-weight: 500; color: #92400e; margin-bottom: 0.5rem;">
                <i class="fas fa-exclamation-triangle"></i> {{ __('user.important_note') }}
            </div>
            <ul style="color: #92400e; font-size: 0.875rem; margin: 0; padding-left: 1rem;">
                <li>{{ __('user.check_character_before_withdraw') }}</li>
                <li>{{ __('user.coin_transfer_instant_irreversible') }}</li>
                <li>{{ __('user.only_withdraw_to_own_character') }}</li>
                <li>{{ __('user.contact_admin_if_issues') }}</li>
            </ul>
        </div>

        <button type="submit" class="btn btn-primary" id="withdrawBtn">
            <i class="fas fa-money-bill-transfer"></i>
            {{ __('user.withdraw_button') }}
        </button>
    </form>
</div>

<!-- Recent Withdraws -->
<div class="card">
    <div class="card-header">
        <h3 class="card-title">
            <i class="fas fa-history"></i>
            {{ __('user.withdraw_history') }}
        </h3>
    </div>
    @if($recentWithdraws->count() > 0)
        <div style="overflow-x: auto;">
            <table style="width: 100%; border-collapse: collapse;">
                <thead>
                    <tr style="border-bottom: 1px solid #e5e7eb;">
                        <th style="text-align: left; padding: 0.75rem; font-weight: 500; color: #374151;">{{ __('user.transaction_id') }}</th>
                        <th style="text-align: left; padding: 0.75rem; font-weight: 500; color: #374151;">{{ __('user.character') }}</th>
                        <th style="text-align: left; padding: 0.75rem; font-weight: 500; color: #374151;">{{ __('user.diamonds') }}</th>
                        <th style="text-align: left; padding: 0.75rem; font-weight: 500; color: #374151;">{{ __('user.status') }}</th>
                        <th style="text-align: left; padding: 0.75rem; font-weight: 500; color: #374151;">{{ __('user.transaction_time') }}</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($recentWithdraws as $withdraw)
                        @php
                            // Use data from coin_spend_logs table
                            $itemData = json_decode($withdraw->item_data, true);
                            $characterName = $itemData['character_name'] ?? 'N/A';
                        @endphp
                        <tr style="border-bottom: 1px solid #f3f4f6;">
                            <td style="padding: 0.75rem; font-family: monospace;">#{{ $withdraw->id }}</td>
                            <td style="padding: 0.75rem; font-weight: 500;">{{ $characterName }}</td>
                            <td style="padding: 0.75rem; color: #ef4444; font-weight: 500;">-{{ number_format($withdraw->coins_spent) }}</td>
                            <td style="padding: 0.75rem;">
                                <span class="status-badge status-success">
                                    ✅ {{ __('user.success_status') }}
                                </span>
                            </td>
                            <td style="padding: 0.75rem; color: #6b7280; font-size: 0.875rem;">
                                {{ \Carbon\Carbon::parse($withdraw->created_at)->format('d/m/Y H:i') }}
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
        <div style="text-align: center; margin-top: 1rem;">
            <a href="{{ route('user.withdraw.history') }}" class="btn btn-outline">
                {{ __('user.view_all_transactions') }}
            </a>
        </div>
    @else
        <div style="text-align: center; color: #6b7280; padding: 2rem;">
            <i class="fas fa-inbox" style="font-size: 2rem; margin-bottom: 1rem; opacity: 0.5;"></i>
            <p>{{ __('user.no_withdraw_transactions') }}</p>
        </div>
    @endif
</div>

<!-- Statistics -->
<div class="card">
    <div class="card-header">
        <h3 class="card-title">
            <i class="fas fa-chart-bar"></i>
            {{ __('user.withdraw_statistics') }}
        </h3>
    </div>
    <div class="grid grid-4">
        <div style="text-align: center; padding: 1.5rem; background: #f0f9ff; border-radius: 8px;">
            <i class="fas fa-list" style="font-size: 2rem; color: #3b82f6; margin-bottom: 1rem;"></i>
            <div style="font-size: 1.5rem; font-weight: 700; color: #1e40af;">{{ $stats['total_withdraws'] }}</div>
            <div style="color: #6b7280; font-size: 0.875rem;">{{ __('user.total_transactions') }}</div>
        </div>
        <div style="text-align: center; padding: 1.5rem; background: #f0fdf4; border-radius: 8px;">
            <i class="fas fa-check-circle" style="font-size: 2rem; color: #10b981; margin-bottom: 1rem;"></i>
            <div style="font-size: 1.5rem; font-weight: 700; color: #166534;">{{ $stats['completed_withdraws'] }}</div>
            <div style="color: #6b7280; font-size: 0.875rem;">{{ __('user.success_status') }}</div>
        </div>
        <div style="text-align: center; padding: 1.5rem; background: #fef3c7; border-radius: 8px;">
            <i class="fas fa-coins" style="font-size: 2rem; color: #f59e0b; margin-bottom: 1rem;"></i>
            <div style="font-size: 1.5rem; font-weight: 700; color: #92400e;">{{ number_format($stats['total_amount']) }}</div>
            <div style="color: #6b7280; font-size: 0.875rem;">{{ __('user.total_withdrawn') }}</div>
        </div>
        <div style="text-align: center; padding: 1.5rem; background: #fdf2f8; border-radius: 8px;">
            <i class="fas fa-calendar-day" style="font-size: 2rem; color: #ec4899; margin-bottom: 1rem;"></i>
            <div style="font-size: 1.5rem; font-weight: 700; color: #be185d;">{{ number_format($stats['today_amount']) }}</div>
            <div style="color: #6b7280; font-size: 0.875rem;">{{ __('user.today') }}</div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
// Form validation
document.getElementById('withdrawForm').addEventListener('submit', function(e) {
    const amount = parseInt(document.getElementById('amount').value);
    const characterId = document.getElementById('character_id').value;
    const characterName = document.getElementById('character_id').selectedOptions[0]?.text || '';

    if (!characterId) {
        alert('Vui lòng chọn nhân vật nhận kim cương!');
        e.preventDefault();
        return;
    }

    if (amount < 1) {
        alert('Vui lòng nhập số kim cương hợp lệ!');
        e.preventDefault();
        return;
    }

    if (amount > {{ $userCoins->coins ?? 0 }}) {
        alert('Không đủ kim cương để rút!');
        e.preventDefault();
        return;
    }

    if (amount > {{ $stats['remaining_today'] }}) {
        alert('Vượt quá giới hạn rút kim cương hàng ngày!');
        e.preventDefault();
        return;
    }

    const gameCoins = amount * 10; // 1 web coin = 10 KC
    if (!confirm(`Bạn có chắc chắn muốn rút ${new Intl.NumberFormat().format(amount)} kim cương web vào nhân vật "${characterName}"?\n\nSẽ nhận được: ${new Intl.NumberFormat().format(gameCoins)} kim cương game\nTỷ lệ: 1 kim cương web = 10 kim cương game\n\nHành động này không thể hoàn tác!`)) {
        e.preventDefault();
        return;
    }

    // Disable button to prevent double submission
    const btn = document.getElementById('withdrawBtn');
    btn.disabled = true;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Đang xử lý...';
});

// Auto format number input - chỉ loại bỏ ký tự không phải số
document.getElementById('amount').addEventListener('input', function() {
    let value = this.value.replace(/[^0-9]/g, '');
    this.value = value;
});
@endsection
