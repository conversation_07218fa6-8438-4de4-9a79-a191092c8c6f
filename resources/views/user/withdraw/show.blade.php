@extends('layouts.user')

@section('title', 'Chi tiết giao dịch rút coin')

@section('content')
<div class="container">
    <div class="page-header">
        <h1>💸 Chi tiết giao dịch rút coin</h1>
        <p>Thông tin chi tiết về giao dịch #{{ $withdraw->id }}</p>
    </div>

    <div class="content-card">
        <!-- Transaction Status -->
        <div class="status-section">
            <div class="status-badge status-completed">
                <i class="fas fa-check-circle"></i> Hoàn thành
            </div>
        </div>

        <!-- Transaction Details -->
        <div class="details-grid">
            <div class="detail-item">
                <label>Mã giao dịch</label>
                <div class="detail-value">
                    <strong>#{{ $withdraw->id }}</strong>
                    <button onclick="copyToClipboard('{{ $withdraw->id }}')" class="btn-copy">
                        <i class="fas fa-copy"></i>
                    </button>
                </div>
            </div>

            <div class="detail-item">
                <label>Số kim cương đã rút</label>
                <div class="detail-value amount">
                    <i class="fas fa-gem"></i> {{ number_format($withdraw->amount) }} 💎
                </div>
            </div>

            <div class="detail-item">
                <label>Nhân vật nhận</label>
                <div class="detail-value character">
                    <i class="fas fa-user"></i> {{ $withdraw->character_name ?? 'N/A' }}
                </div>
            </div>

            <div class="detail-item">
                <label>Thời gian thực hiện</label>
                <div class="detail-value">
                    <i class="fas fa-calendar"></i> {{ \Carbon\Carbon::parse($withdraw->created_at)->format('d/m/Y H:i:s') }}
                </div>
            </div>

            <div class="detail-item">
                <label>Trạng thái</label>
                <div class="detail-value status">
                    <i class="fas fa-check-circle"></i> Đã chuyển vào game
                </div>
            </div>

            <div class="detail-item">
                <label>Mã giao dịch game</label>
                <div class="detail-value">
                    {{ $withdraw->transaction_id ?? 'N/A' }}
                </div>
            </div>

            @if($withdraw->description)
            <div class="detail-item full-width">
                <label>Mô tả</label>
                <div class="detail-value note">
                    {{ $withdraw->description }}
                </div>
            </div>
            @endif
        </div>

        <!-- Game Balance Info -->
        <div class="balance-info">
            <h3><i class="fas fa-info-circle"></i> Thông tin số dư</h3>
            <div class="balance-grid">
                <div class="balance-item">
                    <label>Số dư trước giao dịch</label>
                    <div class="balance-value">
                        {{ number_format($withdraw->coin_balance_before ?? 0) }} 💎
                    </div>
                </div>
                <div class="balance-item">
                    <label>Số kim cương đã rút</label>
                    <div class="balance-value deduct">
                        -{{ number_format($withdraw->amount) }} 💎
                    </div>
                </div>
                <div class="balance-item">
                    <label>Số dư sau giao dịch</label>
                    <div class="balance-value">
                        {{ number_format($withdraw->coin_balance_after ?? 0) }} 💎
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="action-buttons">
            <a href="{{ route('user.withdraw.history') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Quay lại lịch sử
            </a>

            <a href="{{ route('user.withdraw') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Rút thêm coin
            </a>

            <button onclick="window.print()" class="btn btn-outline">
                <i class="fas fa-print"></i> In hóa đơn
            </button>
        </div>
    </div>
</div>

<style>
.page-header {
    text-align: center;
    margin-bottom: 30px;
}

.page-header h1 {
    color: #2c3e50;
    margin-bottom: 10px;
}

.status-section {
    text-align: center;
    margin-bottom: 30px;
}

.status-badge {
    display: inline-block;
    padding: 12px 24px;
    border-radius: 25px;
    font-weight: 600;
    font-size: 16px;
}

.status-completed {
    background: #d4edda;
    color: #155724;
    border: 2px solid #00b894;
}

.details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.detail-item {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    border-left: 4px solid #28a745;
}

.detail-item.full-width {
    grid-column: 1 / -1;
}

.detail-item label {
    display: block;
    font-weight: 600;
    color: #6c757d;
    margin-bottom: 8px;
    font-size: 14px;
}

.detail-value {
    font-size: 16px;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 10px;
}

.detail-value.amount {
    font-size: 20px;
    font-weight: 700;
    color: #28a745;
}

.detail-value.character {
    font-size: 18px;
    font-weight: 600;
    color: #007bff;
}

.detail-value.status {
    font-weight: 600;
    color: #28a745;
}

.detail-value.note {
    background: white;
    padding: 10px;
    border-radius: 5px;
    border: 1px solid #dee2e6;
    font-style: italic;
}

.btn-copy {
    background: none;
    border: none;
    color: #007bff;
    cursor: pointer;
    padding: 5px;
    border-radius: 3px;
    transition: all 0.3s ease;
}

.btn-copy:hover {
    background: #007bff;
    color: white;
}

.balance-info {
    background: #e8f5e8;
    padding: 25px;
    border-radius: 10px;
    margin-bottom: 30px;
    border: 1px solid #c3e6cb;
}

.balance-info h3 {
    color: #155724;
    margin-bottom: 20px;
    font-size: 18px;
}

.balance-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.balance-item {
    background: white;
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #c3e6cb;
    text-align: center;
}

.balance-item label {
    display: block;
    font-weight: 600;
    color: #666;
    margin-bottom: 8px;
    font-size: 13px;
}

.balance-value {
    font-size: 18px;
    font-weight: 700;
    color: #28a745;
}

.balance-value.deduct {
    color: #dc3545;
}

.action-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.btn {
    padding: 12px 24px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
}

.btn-outline {
    background: white;
    color: #28a745;
    border: 2px solid #28a745;
}

.btn-outline:hover {
    background: #28a745;
    color: white;
}

@media (max-width: 768px) {
    .details-grid {
        grid-template-columns: 1fr;
    }
    
    .balance-grid {
        grid-template-columns: 1fr;
    }
    
    .action-buttons {
        flex-direction: column;
    }
}

@media print {
    .action-buttons {
        display: none;
    }
    
    .page-header {
        border-bottom: 2px solid #000;
        padding-bottom: 20px;
    }
}
</style>

<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Show success message
        const toast = document.createElement('div');
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #28a745;
            color: white;
            padding: 12px 20px;
            border-radius: 5px;
            z-index: 1000;
            font-weight: 600;
        `;
        toast.textContent = 'Đã copy vào clipboard!';
        document.body.appendChild(toast);
        
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 2000);
    });
}
</script>
@endsection
